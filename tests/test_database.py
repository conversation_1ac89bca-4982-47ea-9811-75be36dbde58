"""Tests for database functionality."""

import pytest
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.database import DatabaseManager, League, Team


class TestDatabaseManager:
    """Test database manager functionality."""
    
    def test_database_creation(self):
        """Test database manager creation."""
        db_manager = DatabaseManager()
        assert db_manager is not None
        assert db_manager.engine is not None
    
    def test_table_creation(self):
        """Test database table creation."""
        db_manager = DatabaseManager()
        
        # This will create tables in memory for SQLite
        try:
            db_manager.create_tables()
            assert True  # If no exception, tables were created
        except Exception as e:
            pytest.fail(f"Table creation failed: {e}")
    
    def test_connection(self):
        """Test database connection."""
        db_manager = DatabaseManager()
        assert db_manager.test_connection() is True
    
    def test_save_leagues(self):
        """Test saving league data."""
        db_manager = DatabaseManager()
        db_manager.create_tables()
        
        # Sample league data
        leagues_data = [
            {
                'external_id': 1,
                'name': 'Test League',
                'country': 'Test Country',
                'season_start_month': 8
            }
        ]
        
        saved_count = db_manager.save_leagues(leagues_data)
        assert saved_count == 1
    
    def test_save_teams(self):
        """Test saving team data."""
        db_manager = DatabaseManager()
        db_manager.create_tables()
        
        # First save a league
        leagues_data = [
            {
                'external_id': 1,
                'name': 'Test League',
                'country': 'Test Country',
                'season_start_month': 8
            }
        ]
        db_manager.save_leagues(leagues_data)
        
        # Sample team data
        teams_data = [
            {
                'external_id': 1,
                'name': 'Test Team',
                'short_name': 'TT',
                'league_id': 1
            }
        ]
        
        saved_count = db_manager.save_teams(teams_data)
        assert saved_count == 1
