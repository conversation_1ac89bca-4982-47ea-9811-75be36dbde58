2025-07-18 07:41:08,885 - collect_api_football_data - INFO - Starting API-Football data collection...
2025-07-18 07:41:08,886 - collect_api_football_data - INFO - Configuration: {'leagues': None, 'collection_type': 'recent', 'days_back': 7, 'days_ahead': 7, 'fixture_id': None, 'include_historical': False, 'dry_run': False, 'check_api_status': True}
2025-07-18 07:41:50,015 - collect_api_football_data - INFO - Starting API-Football data collection...
2025-07-18 07:41:50,016 - collect_api_football_data - INFO - Configuration: {'leagues': None, 'collection_type': 'recent', 'days_back': 7, 'days_ahead': 7, 'fixture_id': None, 'include_historical': False, 'dry_run': False, 'check_api_status': True}
2025-07-18 07:41:50,017 - config_check - INFO - API-Football configuration is valid
2025-07-18 07:41:50,018 - api_football_client - INFO - API-Football client initialized
2025-07-18 07:41:50,385 - collect_api_football_data - ERROR - Data collection failed: No module named 'psycopg2'
2025-07-18 07:43:21,171 - collect_api_football_data - INFO - Starting API-Football data collection...
2025-07-18 07:43:21,184 - collect_api_football_data - INFO - Configuration: {'leagues': None, 'collection_type': 'recent', 'days_back': 7, 'days_ahead': 7, 'fixture_id': None, 'include_historical': False, 'dry_run': False, 'check_api_status': True}
2025-07-18 07:43:21,185 - config_check - INFO - API-Football configuration is valid
2025-07-18 07:43:21,186 - api_football_client - INFO - API-Football client initialized
2025-07-18 07:43:21,222 - database - INFO - Created database engine for sqlite
2025-07-18 07:43:22,559 - recent_collection - INFO - Collecting recent data for last 7 days...
2025-07-18 07:43:22,559 - api_football_orchestrator - INFO - Starting comprehensive data collection with API-Football
2025-07-18 07:43:22,560 - api_football_orchestrator - INFO - Collecting data for premier_league (ID: 39)
2025-07-18 07:43:22,560 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:23,189 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:23,190 - collector.api_football - INFO - Collecting teams for league 39, season 2024
2025-07-18 07:43:23,811 - collector.api_football - INFO - Collected 0 teams for league 39
2025-07-18 07:43:23,812 - collector.api_football - INFO - Collecting matches for league 39, season 2024
2025-07-18 07:43:24,824 - collector.api_football - INFO - Collected 0 matches for league 39
2025-07-18 07:43:24,824 - collector.api_football - INFO - Collecting injuries for league 39
2025-07-18 07:43:25,555 - collector.api_football - INFO - Collected 0 injuries for league 39
2025-07-18 07:43:25,555 - api_football_orchestrator - INFO - Completed data collection for premier_league
2025-07-18 07:43:27,698 - api_football_orchestrator - INFO - Collecting data for la_liga (ID: 140)
2025-07-18 07:43:27,700 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:28,600 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:28,600 - collector.api_football - INFO - Collecting teams for league 140, season 2024
2025-07-18 07:43:29,493 - collector.api_football - INFO - Collected 0 teams for league 140
2025-07-18 07:43:29,493 - collector.api_football - INFO - Collecting matches for league 140, season 2024
2025-07-18 07:43:30,155 - collector.api_football - INFO - Collected 0 matches for league 140
2025-07-18 07:43:30,157 - collector.api_football - INFO - Collecting injuries for league 140
2025-07-18 07:43:30,927 - collector.api_football - INFO - Collected 0 injuries for league 140
2025-07-18 07:43:30,927 - api_football_orchestrator - INFO - Completed data collection for la_liga
2025-07-18 07:43:32,931 - api_football_orchestrator - INFO - Collecting data for bundesliga (ID: 78)
2025-07-18 07:43:32,931 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:33,631 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:33,631 - collector.api_football - INFO - Collecting teams for league 78, season 2024
2025-07-18 07:43:34,340 - collector.api_football - INFO - Collected 0 teams for league 78
2025-07-18 07:43:34,340 - collector.api_football - INFO - Collecting matches for league 78, season 2024
2025-07-18 07:43:35,023 - collector.api_football - INFO - Collected 0 matches for league 78
2025-07-18 07:43:35,023 - collector.api_football - INFO - Collecting injuries for league 78
2025-07-18 07:43:35,788 - collector.api_football - INFO - Collected 0 injuries for league 78
2025-07-18 07:43:35,788 - api_football_orchestrator - INFO - Completed data collection for bundesliga
2025-07-18 07:43:37,793 - api_football_orchestrator - INFO - Collecting data for serie_a (ID: 135)
2025-07-18 07:43:37,795 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:38,490 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:38,490 - collector.api_football - INFO - Collecting teams for league 135, season 2024
2025-07-18 07:43:39,140 - collector.api_football - INFO - Collected 0 teams for league 135
2025-07-18 07:43:39,140 - collector.api_football - INFO - Collecting matches for league 135, season 2024
2025-07-18 07:43:39,798 - collector.api_football - INFO - Collected 0 matches for league 135
2025-07-18 07:43:39,799 - collector.api_football - INFO - Collecting injuries for league 135
2025-07-18 07:43:40,515 - collector.api_football - INFO - Collected 0 injuries for league 135
2025-07-18 07:43:40,515 - api_football_orchestrator - INFO - Completed data collection for serie_a
2025-07-18 07:43:42,520 - api_football_orchestrator - INFO - Collecting data for ligue_1 (ID: 61)
2025-07-18 07:43:42,520 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:43,351 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:43,353 - collector.api_football - INFO - Collecting teams for league 61, season 2024
2025-07-18 07:43:44,371 - collector.api_football - INFO - Collected 0 teams for league 61
2025-07-18 07:43:44,371 - collector.api_football - INFO - Collecting matches for league 61, season 2024
2025-07-18 07:43:45,183 - collector.api_football - INFO - Collected 0 matches for league 61
2025-07-18 07:43:45,189 - collector.api_football - INFO - Collecting injuries for league 61
2025-07-18 07:43:46,521 - collector.api_football - INFO - Collected 0 injuries for league 61
2025-07-18 07:43:46,523 - api_football_orchestrator - INFO - Completed data collection for ligue_1
2025-07-18 07:43:48,528 - api_football_orchestrator - INFO - Collecting data for champions_league (ID: 2)
2025-07-18 07:43:48,528 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:49,516 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:49,535 - collector.api_football - INFO - Collecting teams for league 2, season 2024
2025-07-18 07:43:50,366 - collector.api_football - INFO - Collected 0 teams for league 2
2025-07-18 07:43:50,366 - collector.api_football - INFO - Collecting matches for league 2, season 2024
2025-07-18 07:43:51,212 - collector.api_football - INFO - Collected 0 matches for league 2
2025-07-18 07:43:51,212 - collector.api_football - INFO - Collecting injuries for league 2
2025-07-18 07:43:52,171 - collector.api_football - INFO - Collected 0 injuries for league 2
2025-07-18 07:43:52,171 - api_football_orchestrator - INFO - Completed data collection for champions_league
2025-07-18 07:43:54,185 - api_football_orchestrator - INFO - Collecting data for europa_league (ID: 3)
2025-07-18 07:43:54,185 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:43:55,045 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:43:55,049 - collector.api_football - INFO - Collecting teams for league 3, season 2024
2025-07-18 07:43:56,231 - collector.api_football - INFO - Collected 0 teams for league 3
2025-07-18 07:43:56,231 - collector.api_football - INFO - Collecting matches for league 3, season 2024
2025-07-18 07:43:57,153 - collector.api_football - INFO - Collected 0 matches for league 3
2025-07-18 07:43:57,197 - collector.api_football - INFO - Collecting injuries for league 3
2025-07-18 07:43:57,914 - collector.api_football - INFO - Collected 0 injuries for league 3
2025-07-18 07:43:57,915 - api_football_orchestrator - INFO - Completed data collection for europa_league
2025-07-18 07:43:59,916 - api_football_orchestrator - INFO - Data collection completed. Summary: {'leagues_collected': 7, 'teams_collected': 0, 'matches_collected': 0, 'players_collected': 0, 'injuries_collected': 0, 'statistics_collected': 0, 'errors': []}
2025-07-18 07:44:00,700 - collect_api_football_data - INFO - Data collection completed successfully
2025-07-18 07:47:23,508 - collect_api_football_data - INFO - Starting API-Football data collection...
2025-07-18 07:47:23,509 - collect_api_football_data - INFO - Configuration: {'leagues': ['premier_league'], 'collection_type': 'recent', 'days_back': 14, 'days_ahead': 7, 'fixture_id': None, 'include_historical': False, 'dry_run': False, 'check_api_status': False}
2025-07-18 07:47:23,509 - config_check - INFO - API-Football configuration is valid
2025-07-18 07:47:23,509 - api_football_client - INFO - API-Football client initialized
2025-07-18 07:47:23,541 - database - INFO - Created database engine for sqlite
2025-07-18 07:47:23,542 - recent_collection - INFO - Collecting recent data for last 14 days...
2025-07-18 07:47:23,542 - api_football_orchestrator - INFO - Starting comprehensive data collection with API-Football
2025-07-18 07:47:23,542 - api_football_orchestrator - INFO - Collecting data for premier_league (ID: 39)
2025-07-18 07:47:23,542 - collector.api_football - INFO - Collecting league information from API-Football
2025-07-18 07:47:24,601 - collector.api_football - INFO - Collected 0 leagues
2025-07-18 07:47:24,601 - collector.api_football - INFO - Collecting teams for league 39, season 2024
2025-07-18 07:47:25,302 - collector.api_football - INFO - Collected 0 teams for league 39
2025-07-18 07:47:25,303 - collector.api_football - INFO - Collecting matches for league 39, season 2024
2025-07-18 07:47:26,198 - collector.api_football - INFO - Collected 0 matches for league 39
2025-07-18 07:47:26,199 - collector.api_football - INFO - Collecting injuries for league 39
2025-07-18 07:47:27,424 - collector.api_football - INFO - Collected 0 injuries for league 39
2025-07-18 07:47:27,424 - api_football_orchestrator - INFO - Completed data collection for premier_league
2025-07-18 07:47:29,429 - api_football_orchestrator - INFO - Data collection completed. Summary: {'leagues_collected': 1, 'teams_collected': 0, 'matches_collected': 0, 'players_collected': 0, 'injuries_collected': 0, 'statistics_collected': 0, 'errors': []}
2025-07-18 07:47:29,432 - collect_api_football_data - INFO - Data collection completed successfully
