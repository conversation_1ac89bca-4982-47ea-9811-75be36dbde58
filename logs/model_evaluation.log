2025-07-18 05:16:10,990 - evaluate_models - INFO - Starting model evaluation and selection...
2025-07-18 05:16:10,990 - evaluate_models - INFO - Configuration: {'models_dir': 'data/models', 'data_file': None, 'selection_criteria': 'weighted_score', 'backtest': False, 'backtest_start': None, 'backtest_end': None, 'cross_validate': False, 'output_dir': 'data/evaluation'}
2025-07-18 05:16:10,991 - model_loader - WARNING - No model files found in data/models
2025-07-18 05:16:10,991 - evaluate_models - INFO - No trained models found, training sample models for demonstration...
2025-07-18 05:16:10,991 - data_creator - INFO - Creating sample test data for evaluation...
2025-07-18 05:16:11,019 - database - INFO - Created database engine for sqlite
2025-07-18 05:16:11,019 - database - INFO - Created database engine for sqlite
2025-07-18 05:16:11,020 - database - INFO - Created database engine for sqlite
2025-07-18 05:16:11,020 - database - INFO - Created database engine for sqlite
2025-07-18 05:16:11,026 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:16:11,026 - feature_engineering - INFO - Starting feature extraction for 100 matches
2025-07-18 05:16:11,034 - feature_engineering - INFO - Completed 100/100 matches
2025-07-18 05:16:11,090 - feature_engineering - INFO - Feature matrix created with shape: (100, 125)
2025-07-18 05:16:11,090 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:16:11,096 - model_trainer - INFO - Encoded target classes: {'D': 0}
2025-07-18 05:16:11,098 - model_trainer - INFO - Data prepared:
2025-07-18 05:16:11,098 - model_trainer - INFO -   Total samples: 100
2025-07-18 05:16:11,098 - model_trainer - INFO -   Features: 116
2025-07-18 05:16:11,098 - model_trainer - INFO -   Train samples: 70
2025-07-18 05:16:11,099 - model_trainer - INFO -   Validation samples: 10
2025-07-18 05:16:11,099 - model_trainer - INFO -   Test samples: 20
2025-07-18 05:16:11,099 - model_trainer - INFO - Starting model training...
2025-07-18 05:16:11,099 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:16:11,099 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:16:11,099 - model_trainer - INFO - Tuning hyperparameters for xgboost...
2025-07-18 05:16:26,368 - model_trainer - ERROR - Failed to train xgboost: 
All the 1215 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1215 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/sklearn/model_selection/_validation.py", line 859, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/core.py", line 729, in inner_f
    return func(**kwargs)
           ^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/sklearn.py", line 1682, in fit
    self._Booster = train(
                    ^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/core.py", line 729, in inner_f
    return func(**kwargs)
           ^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/training.py", line 183, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/core.py", line 2246, in update
    _check_call(
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/xgboost/core.py", line 310, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: value 0 for Parameter num_class should be greater equal to 1
num_class: Number of output class in the multi-class classification.

2025-07-18 05:16:26,369 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:16:26,369 - model_trainer - INFO - Tuning hyperparameters for lightgbm...
2025-07-18 05:16:26,370 - model.lightgbm_football - WARNING - Optuna not available, falling back to grid search
2025-07-18 05:16:26,909 - model_trainer - ERROR - Failed to train lightgbm: 
All the 135 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
135 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/sklearn/model_selection/_validation.py", line 859, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/sklearn.py", line 1560, in fit
    super().fit(
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/sklearn.py", line 1049, in fit
    self._Booster = train(
                    ^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/engine.py", line 297, in train
    booster = Booster(params=params, train_set=train_set)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/basic.py", line 3656, in __init__
    train_set.construct()
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/basic.py", line 2590, in construct
    self._lazy_init(
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/basic.py", line 2187, in _lazy_init
    self.__init_from_np2d(data, params_str, ref_dataset)
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/basic.py", line 2318, in __init_from_np2d
    _safe_call(
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/lightgbm/basic.py", line 313, in _safe_call
    raise LightGBMError(_LIB.LGBM_GetLastError().decode("utf-8"))
lightgbm.basic.LightGBMError: Number of classes should be specified and greater than 1 for multiclass training

2025-07-18 05:16:26,910 - model_trainer - INFO - Model training completed
2025-07-18 05:16:26,910 - evaluate_models - INFO - Evaluating 0 models on 20 test samples
2025-07-18 05:16:26,910 - evaluate_models - INFO - Evaluating individual models...
2025-07-18 05:16:26,910 - evaluate_models - ERROR - No models could be evaluated
