2025-07-18 05:09:33,259 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:09:33,259 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:09:33,290 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:33,342 - data_loader - INFO - Loaded 20 matches from database
2025-07-18 05:09:33,343 - train_models - ERROR - Insufficient data: 20 samples (minimum: 50)
2025-07-18 05:09:43,928 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:09:43,928 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:09:43,947 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:43,999 - data_loader - INFO - Loaded 20 matches from database
2025-07-18 05:09:44,001 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:09:44,062 - data_cleaner - INFO - Cleaned matches data: 20 matches remaining
2025-07-18 05:09:44,062 - train_models - INFO - Extracting features...
2025-07-18 05:09:44,064 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,064 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,065 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,066 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,070 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:09:44,071 - feature_engineering - INFO - Starting feature extraction for 20 matches
2025-07-18 05:09:44,130 - feature_engineering - INFO - Feature matrix created with shape: (20, 125)
2025-07-18 05:09:44,130 - train_models - INFO - Feature matrix created: (20, 125)
2025-07-18 05:09:44,132 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:09:44,136 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:09:44,139 - model_trainer - INFO - Data prepared:
2025-07-18 05:09:44,139 - model_trainer - INFO -   Total samples: 20
2025-07-18 05:09:44,139 - model_trainer - INFO -   Features: 116
2025-07-18 05:09:44,139 - model_trainer - INFO -   Train samples: 14
2025-07-18 05:09:44,139 - model_trainer - INFO -   Validation samples: 2
2025-07-18 05:09:44,140 - model_trainer - INFO -   Test samples: 4
2025-07-18 05:09:44,140 - model_trainer - INFO - Starting model training...
2025-07-18 05:09:44,140 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:09:44,140 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:09:44,140 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:09:44,261 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:09:44,262 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:09:44,262 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:09:44,295 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:09:44,296 - model_trainer - INFO - Model training completed
2025-07-18 05:09:44,296 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:09:44,297 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 05:10:24,275 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:10:24,275 - train_models - INFO - Configuration: {'data_source': 'file', 'data_file': None, 'models': None, 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:10:24,275 - train_models - INFO - Creating sample training data for demonstration
2025-07-18 05:10:24,275 - data_creator - INFO - Creating sample training data...
2025-07-18 05:10:24,288 - data_creator - INFO - Created 500 sample matches
2025-07-18 05:10:24,288 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:10:24,307 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 05:10:24,307 - train_models - INFO - Extracting features...
2025-07-18 05:10:24,330 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,331 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,332 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,332 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,338 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:10:24,338 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 05:10:24,357 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 05:10:24,374 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 05:10:24,463 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 05:10:24,464 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 05:10:24,464 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:10:24,469 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:10:24,471 - model_trainer - INFO - Data prepared:
2025-07-18 05:10:24,471 - model_trainer - INFO -   Total samples: 500
2025-07-18 05:10:24,472 - model_trainer - INFO -   Features: 116
2025-07-18 05:10:24,472 - model_trainer - INFO -   Train samples: 350
2025-07-18 05:10:24,472 - model_trainer - INFO -   Validation samples: 50
2025-07-18 05:10:24,472 - model_trainer - INFO -   Test samples: 100
2025-07-18 05:10:24,472 - model_trainer - INFO - Starting model training...
2025-07-18 05:10:24,472 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:10:24,473 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:10:24,473 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:10:24,505 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:10:24,505 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:10:24,505 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:10:24,511 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:10:24,511 - model_trainer - INFO - Model training completed
2025-07-18 05:10:24,512 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:10:24,514 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 05:11:09,746 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:11:09,750 - train_models - INFO - Configuration: {'data_source': 'file', 'data_file': None, 'models': None, 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:11:09,750 - train_models - INFO - Creating sample training data for demonstration
2025-07-18 05:11:09,750 - data_creator - INFO - Creating sample training data...
2025-07-18 05:11:09,796 - data_creator - INFO - Created 500 sample matches
2025-07-18 05:11:09,798 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:11:09,901 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 05:11:09,902 - train_models - INFO - Extracting features...
2025-07-18 05:11:09,936 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,936 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,937 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,937 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,942 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:11:09,942 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 05:11:09,972 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 05:11:09,990 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 05:11:10,088 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 05:11:10,089 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 05:11:10,089 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:11:10,095 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:11:10,100 - model_trainer - INFO - Data prepared:
2025-07-18 05:11:10,100 - model_trainer - INFO -   Total samples: 500
2025-07-18 05:11:10,101 - model_trainer - INFO -   Features: 116
2025-07-18 05:11:10,101 - model_trainer - INFO -   Train samples: 350
2025-07-18 05:11:10,101 - model_trainer - INFO -   Validation samples: 50
2025-07-18 05:11:10,101 - model_trainer - INFO -   Test samples: 100
2025-07-18 05:11:10,101 - model_trainer - INFO - Starting model training...
2025-07-18 05:11:10,102 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:11:10,102 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:11:10,102 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:11:10,527 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:11:10,527 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:11:10,527 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:11:10,538 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:11:10,539 - model_trainer - INFO - Model training completed
2025-07-18 05:11:10,539 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:11:10,540 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:03:14,316 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:03:14,316 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:03:14,371 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:14,466 - data_loader - WARNING - No finished matches found in database
2025-07-18 08:03:14,538 - train_models - INFO - No data in database, creating sample data for demonstration
2025-07-18 08:03:14,538 - data_creator - INFO - Creating sample training data...
2025-07-18 08:03:15,048 - data_creator - INFO - Created 500 sample matches
2025-07-18 08:03:15,053 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:03:15,196 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 08:03:15,196 - train_models - INFO - Extracting features...
2025-07-18 08:03:15,197 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,197 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,198 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,198 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,207 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:03:15,207 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 08:03:15,236 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 08:03:15,257 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 08:03:15,388 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 08:03:15,388 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 08:03:15,388 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:03:15,399 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:03:15,411 - model_trainer - INFO - Data prepared:
2025-07-18 08:03:15,412 - model_trainer - INFO -   Total samples: 500
2025-07-18 08:03:15,412 - model_trainer - INFO -   Features: 116
2025-07-18 08:03:15,412 - model_trainer - INFO -   Train samples: 350
2025-07-18 08:03:15,412 - model_trainer - INFO -   Validation samples: 50
2025-07-18 08:03:15,413 - model_trainer - INFO -   Test samples: 100
2025-07-18 08:03:15,413 - model_trainer - INFO - Starting model training...
2025-07-18 08:03:15,413 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:03:15,413 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:03:15,413 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:03:15,499 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:03:15,499 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:03:15,499 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:03:15,538 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:03:15,538 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:03:15,538 - model_trainer - ERROR - Failed to train random_forest: 'random_forest'
2025-07-18 08:03:15,539 - model_trainer - INFO - Model training completed
2025-07-18 08:03:15,539 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:03:15,540 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:14:21,404 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:14:21,405 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:14:21,450 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,610 - data_loader - WARNING - No finished matches found in database
2025-07-18 08:14:21,623 - train_models - INFO - No data in database, creating sample data for demonstration
2025-07-18 08:14:21,623 - data_creator - INFO - Creating sample training data...
2025-07-18 08:14:21,650 - data_creator - INFO - Created 500 sample matches
2025-07-18 08:14:21,650 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:14:21,736 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 08:14:21,736 - train_models - INFO - Extracting features...
2025-07-18 08:14:21,737 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,738 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,739 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,740 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,747 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:14:21,747 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 08:14:21,768 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 08:14:21,782 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 08:14:21,783 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 08:14:21,783 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 08:14:21,784 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 08:14:21,869 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 08:14:21,869 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 08:14:21,869 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:14:21,873 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:14:21,878 - model_trainer - INFO - Data prepared:
2025-07-18 08:14:21,878 - model_trainer - INFO -   Total samples: 500
2025-07-18 08:14:21,878 - model_trainer - INFO -   Features: 116
2025-07-18 08:14:21,878 - model_trainer - INFO -   Train samples: 350
2025-07-18 08:14:21,878 - model_trainer - INFO -   Validation samples: 50
2025-07-18 08:14:21,879 - model_trainer - INFO -   Test samples: 100
2025-07-18 08:14:21,879 - model_trainer - INFO - Starting model training...
2025-07-18 08:14:21,879 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:14:21,879 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:14:21,879 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:14:21,924 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:14:21,925 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:14:21,925 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:14:21,929 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:14:21,929 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:14:21,930 - model_trainer - ERROR - Failed to train random_forest: 'random_forest'
2025-07-18 08:14:21,930 - model_trainer - INFO - Model training completed
2025-07-18 08:14:21,931 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:14:21,932 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:17:46,498 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:17:46,498 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:17:46,555 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,683 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:17:46,688 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:17:46,870 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:17:46,871 - train_models - INFO - Extracting features...
2025-07-18 08:17:46,872 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,873 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,874 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,875 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,910 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:17:46,910 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:17:46,943 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:17:46,944 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:17:46,945 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:17:47,013 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:17:47,014 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:17:47,656 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:17:47,660 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:17:47,666 - model_trainer - INFO - Data prepared:
2025-07-18 08:17:47,666 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:17:47,666 - model_trainer - INFO -   Features: 116
2025-07-18 08:17:47,666 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:17:47,667 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:17:47,667 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:17:47,667 - model_trainer - INFO - Starting model training...
2025-07-18 08:17:47,667 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:17:47,667 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:17:47,667 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:17:47,721 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:17:47,721 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:17:47,721 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:17:47,729 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:17:47,729 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:17:47,729 - model_trainer - ERROR - Failed to train random_forest: Can't instantiate abstract class RandomForestFootballModel without an implementation for abstract methods '_create_model', '_fit_model', '_predict_proba'
2025-07-18 08:17:47,729 - model_trainer - INFO - Model training completed
2025-07-18 08:17:47,730 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:17:47,731 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:20:45,145 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:20:45,145 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:20:45,219 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,325 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:20:45,329 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:20:45,398 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:20:45,398 - train_models - INFO - Extracting features...
2025-07-18 08:20:45,399 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,399 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,400 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,400 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,405 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:20:45,405 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:20:45,427 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:20:45,438 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:20:45,439 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:20:45,511 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:20:45,512 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:20:45,711 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:20:45,715 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:20:45,717 - model_trainer - INFO - Data prepared:
2025-07-18 08:20:45,718 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:20:45,718 - model_trainer - INFO -   Features: 116
2025-07-18 08:20:45,718 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:20:45,718 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:20:45,718 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:20:45,718 - model_trainer - INFO - Starting model training...
2025-07-18 08:20:45,718 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:20:45,718 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:20:45,718 - model_trainer - ERROR - Failed to train random_forest: RandomForestFootballModel.__init__() got an unexpected keyword argument 'model_name'
2025-07-18 08:20:45,718 - model_trainer - INFO - Model training completed
2025-07-18 08:20:45,718 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:20:45,719 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:22:10,437 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:22:10,438 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:22:10,513 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,674 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:22:10,680 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:22:10,726 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:22:10,728 - train_models - INFO - Extracting features...
2025-07-18 08:22:10,730 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,731 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,734 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,734 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,741 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:22:10,743 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:22:10,763 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:22:10,774 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:22:10,775 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:22:10,863 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:22:10,863 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:22:11,284 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:22:11,294 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:22:11,304 - model_trainer - INFO - Data prepared:
2025-07-18 08:22:11,304 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:22:11,304 - model_trainer - INFO -   Features: 116
2025-07-18 08:22:11,304 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:22:11,304 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:22:11,304 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:22:11,305 - model_trainer - INFO - Starting model training...
2025-07-18 08:22:11,305 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:22:11,305 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:22:11,305 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:22:11,629 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,705 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,706 - model.random_forest_football - INFO - Training completed in 0.22 seconds
2025-07-18 08:22:11,768 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,768 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:22:11,768 - model_trainer - INFO - Model training completed
2025-07-18 08:22:11,768 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:22:11,788 - train_models - ERROR - Training failed: 'dict' object has no attribute 'head'
2025-07-18 08:23:33,431 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:23:33,433 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:23:33,481 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,612 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:23:33,621 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:23:33,662 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:23:33,662 - train_models - INFO - Extracting features...
2025-07-18 08:23:33,663 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,663 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,664 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,664 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,670 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:23:33,670 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:23:33,690 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:23:33,694 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:23:33,694 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:23:33,766 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:23:33,766 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:23:34,011 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:23:34,015 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:23:34,019 - model_trainer - INFO - Data prepared:
2025-07-18 08:23:34,019 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:23:34,019 - model_trainer - INFO -   Features: 116
2025-07-18 08:23:34,019 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:23:34,019 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:23:34,020 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:23:34,020 - model_trainer - INFO - Starting model training...
2025-07-18 08:23:34,020 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:23:34,020 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:23:34,020 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:23:34,386 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,469 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,469 - model.random_forest_football - INFO - Training completed in 0.21 seconds
2025-07-18 08:23:34,528 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,528 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:23:34,528 - model_trainer - INFO - Model training completed
2025-07-18 08:23:34,528 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:23:34,543 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 08:24:47,268 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:24:47,271 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:24:47,346 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,543 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:24:47,549 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:24:47,626 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:24:47,626 - train_models - INFO - Extracting features...
2025-07-18 08:24:47,626 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,627 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,628 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,628 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,632 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:24:47,632 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:24:47,656 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:24:47,657 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:24:47,657 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:24:47,740 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:24:47,741 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:24:48,244 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:24:48,252 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:24:48,263 - model_trainer - INFO - Data prepared:
2025-07-18 08:24:48,264 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:24:48,264 - model_trainer - INFO -   Features: 116
2025-07-18 08:24:48,264 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:24:48,264 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:24:48,264 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:24:48,265 - model_trainer - INFO - Starting model training...
2025-07-18 08:24:48,265 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:24:48,265 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:24:48,265 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:24:48,704 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,774 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,774 - model.random_forest_football - INFO - Training completed in 0.28 seconds
2025-07-18 08:24:48,851 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,851 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:24:48,851 - model_trainer - INFO - Model training completed
2025-07-18 08:24:48,851 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:24:48,880 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 08:41:33,897 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:41:33,918 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 08:41:34,008 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,211 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:41:34,216 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:41:34,296 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:41:34,296 - train_models - INFO - Extracting features...
2025-07-18 08:41:34,297 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,298 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,298 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,299 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,328 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:41:34,328 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:41:34,372 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:41:34,374 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:41:34,375 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:41:34,496 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:41:34,496 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:41:35,325 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:41:35,332 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:41:35,349 - model_trainer - INFO - Data prepared:
2025-07-18 08:41:35,350 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:41:35,350 - model_trainer - INFO -   Features: 116
2025-07-18 08:41:35,350 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:41:35,351 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:41:35,351 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:41:35,351 - model_trainer - INFO - Starting model training...
2025-07-18 08:41:35,351 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:41:35,351 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:41:35,351 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:41:35,747 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,814 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,814 - model.random_forest_football - INFO - Training completed in 0.27 seconds
2025-07-18 08:41:35,883 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,884 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:41:35,884 - model_trainer - INFO - Model training completed
2025-07-18 08:41:35,884 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:41:35,908 - train_models - INFO - Saving models...
2025-07-18 08:41:36,003 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_084135.joblib
2025-07-18 08:41:36,004 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_084135.joblib
2025-07-18 08:41:36,009 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_084135.json
2025-07-18 08:41:36,012 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 08:54:59,642 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:54:59,642 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 08:54:59,695 - database - INFO - Created database engine for sqlite
2025-07-18 08:54:59,854 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:54:59,860 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:55:00,153 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:55:00,153 - train_models - INFO - Extracting features...
2025-07-18 08:55:00,154 - database - INFO - Created database engine for sqlite
2025-07-18 08:55:00,155 - database - INFO - Created database engine for sqlite
2025-07-18 08:55:00,156 - database - INFO - Created database engine for sqlite
2025-07-18 08:55:00,157 - database - INFO - Created database engine for sqlite
2025-07-18 08:55:00,162 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:55:00,162 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:55:00,189 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:55:00,215 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:55:00,218 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:55:00,329 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:55:00,329 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:55:01,321 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:55:01,325 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:55:01,329 - model_trainer - INFO - Data prepared:
2025-07-18 08:55:01,330 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:55:01,330 - model_trainer - INFO -   Features: 116
2025-07-18 08:55:01,330 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:55:01,330 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:55:01,330 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:55:01,330 - model_trainer - INFO - Starting model training...
2025-07-18 08:55:01,330 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:55:01,330 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:55:01,330 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:55:01,920 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:55:02,016 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:55:02,016 - model.random_forest_football - INFO - Training completed in 0.45 seconds
2025-07-18 08:55:02,129 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:55:02,129 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:55:02,130 - model_trainer - INFO - Model training completed
2025-07-18 08:55:02,130 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:55:02,168 - train_models - INFO - Saving models...
2025-07-18 08:55:02,287 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_085502.joblib
2025-07-18 08:55:02,287 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_085502.joblib
2025-07-18 08:55:02,291 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_085502.json
2025-07-18 08:55:02,293 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 09:00:37,045 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:00:37,045 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:00:37,135 - database - INFO - Created database engine for sqlite
2025-07-18 09:00:37,394 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:00:37,406 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:00:37,527 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:00:37,527 - train_models - INFO - Extracting features...
2025-07-18 09:00:37,528 - database - INFO - Created database engine for sqlite
2025-07-18 09:00:37,529 - database - INFO - Created database engine for sqlite
2025-07-18 09:00:37,529 - database - INFO - Created database engine for sqlite
2025-07-18 09:00:37,530 - database - INFO - Created database engine for sqlite
2025-07-18 09:00:37,550 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:00:37,550 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:00:37,563 - feature_engineering - ERROR - Error extracting features for match 5: name 'include_targets' is not defined
2025-07-18 09:00:37,598 - feature_engineering - ERROR - Error extracting features for match 3: name 'include_targets' is not defined
2025-07-18 09:00:37,599 - feature_engineering - ERROR - Error extracting features for match 4: name 'include_targets' is not defined
2025-07-18 09:00:37,599 - feature_engineering - ERROR - Error extracting features for match 2: name 'include_targets' is not defined
2025-07-18 09:00:37,599 - feature_engineering - ERROR - Error extracting features for match 6: name 'include_targets' is not defined
2025-07-18 09:00:37,600 - feature_engineering - ERROR - Error extracting features for match 1: name 'include_targets' is not defined
2025-07-18 09:00:37,600 - feature_engineering - ERROR - Error extracting features for match 7: name 'include_targets' is not defined
2025-07-18 09:00:37,600 - feature_engineering - ERROR - Error extracting features for match 8: name 'include_targets' is not defined
2025-07-18 09:00:37,601 - feature_engineering - ERROR - Error extracting features for match 9: name 'include_targets' is not defined
2025-07-18 09:00:37,601 - feature_engineering - ERROR - Error extracting features for match 10: name 'include_targets' is not defined
2025-07-18 09:00:37,601 - feature_engineering - ERROR - Error extracting features for match 11: name 'include_targets' is not defined
2025-07-18 09:00:37,602 - feature_engineering - ERROR - Error extracting features for match 12: name 'include_targets' is not defined
2025-07-18 09:00:37,602 - feature_engineering - ERROR - Error extracting features for match 13: name 'include_targets' is not defined
2025-07-18 09:00:37,602 - feature_engineering - ERROR - Error extracting features for match 14: name 'include_targets' is not defined
2025-07-18 09:00:37,605 - feature_engineering - ERROR - Error extracting features for match 15: name 'include_targets' is not defined
2025-07-18 09:00:37,606 - feature_engineering - ERROR - Error extracting features for match 16: name 'include_targets' is not defined
2025-07-18 09:00:37,606 - feature_engineering - ERROR - Error extracting features for match 17: name 'include_targets' is not defined
2025-07-18 09:00:37,606 - feature_engineering - ERROR - Error extracting features for match 18: name 'include_targets' is not defined
2025-07-18 09:00:37,607 - feature_engineering - ERROR - Error extracting features for match 19: name 'include_targets' is not defined
2025-07-18 09:00:37,608 - feature_engineering - ERROR - Error extracting features for match 20: name 'include_targets' is not defined
2025-07-18 09:00:37,609 - feature_engineering - ERROR - Error extracting features for match 21: name 'include_targets' is not defined
2025-07-18 09:00:37,609 - feature_engineering - ERROR - Error extracting features for match 22: name 'include_targets' is not defined
2025-07-18 09:00:37,610 - feature_engineering - ERROR - Error extracting features for match 23: name 'include_targets' is not defined
2025-07-18 09:00:37,610 - feature_engineering - ERROR - Error extracting features for match 24: name 'include_targets' is not defined
2025-07-18 09:00:37,611 - feature_engineering - ERROR - Error extracting features for match 25: name 'include_targets' is not defined
2025-07-18 09:00:37,611 - feature_engineering - ERROR - Error extracting features for match 26: name 'include_targets' is not defined
2025-07-18 09:00:37,611 - feature_engineering - ERROR - Error extracting features for match 27: name 'include_targets' is not defined
2025-07-18 09:00:37,612 - feature_engineering - ERROR - Error extracting features for match 28: name 'include_targets' is not defined
2025-07-18 09:00:37,612 - feature_engineering - ERROR - Error extracting features for match 29: name 'include_targets' is not defined
2025-07-18 09:00:37,612 - feature_engineering - ERROR - Error extracting features for match 30: name 'include_targets' is not defined
2025-07-18 09:00:37,615 - feature_engineering - ERROR - Error extracting features for match 31: name 'include_targets' is not defined
2025-07-18 09:00:37,615 - feature_engineering - ERROR - Error extracting features for match 32: name 'include_targets' is not defined
2025-07-18 09:00:37,615 - feature_engineering - ERROR - Error extracting features for match 33: name 'include_targets' is not defined
2025-07-18 09:00:37,616 - feature_engineering - ERROR - Error extracting features for match 34: name 'include_targets' is not defined
2025-07-18 09:00:37,616 - feature_engineering - ERROR - Error extracting features for match 35: name 'include_targets' is not defined
2025-07-18 09:00:37,616 - feature_engineering - ERROR - Error extracting features for match 36: name 'include_targets' is not defined
2025-07-18 09:00:37,616 - feature_engineering - ERROR - Error extracting features for match 37: name 'include_targets' is not defined
2025-07-18 09:00:37,617 - feature_engineering - ERROR - Error extracting features for match 38: name 'include_targets' is not defined
2025-07-18 09:00:37,618 - feature_engineering - ERROR - Error extracting features for match 39: name 'include_targets' is not defined
2025-07-18 09:00:37,618 - feature_engineering - ERROR - Error extracting features for match 40: name 'include_targets' is not defined
2025-07-18 09:00:37,619 - feature_engineering - ERROR - Error extracting features for match 41: name 'include_targets' is not defined
2025-07-18 09:00:37,626 - feature_engineering - ERROR - Error extracting features for match 42: name 'include_targets' is not defined
2025-07-18 09:00:37,626 - feature_engineering - ERROR - Error extracting features for match 43: name 'include_targets' is not defined
2025-07-18 09:00:37,627 - feature_engineering - ERROR - Error extracting features for match 44: name 'include_targets' is not defined
2025-07-18 09:00:37,627 - feature_engineering - ERROR - Error extracting features for match 45: name 'include_targets' is not defined
2025-07-18 09:00:37,627 - feature_engineering - ERROR - Error extracting features for match 46: name 'include_targets' is not defined
2025-07-18 09:00:37,627 - feature_engineering - ERROR - Error extracting features for match 47: name 'include_targets' is not defined
2025-07-18 09:00:37,628 - feature_engineering - ERROR - Error extracting features for match 48: name 'include_targets' is not defined
2025-07-18 09:00:37,628 - feature_engineering - ERROR - Error extracting features for match 49: name 'include_targets' is not defined
2025-07-18 09:00:37,628 - feature_engineering - ERROR - Error extracting features for match 50: name 'include_targets' is not defined
2025-07-18 09:00:37,628 - feature_engineering - ERROR - Error extracting features for match 51: name 'include_targets' is not defined
2025-07-18 09:00:37,629 - feature_engineering - ERROR - Error extracting features for match 52: name 'include_targets' is not defined
2025-07-18 09:00:37,631 - feature_engineering - ERROR - Error extracting features for match 53: name 'include_targets' is not defined
2025-07-18 09:00:37,631 - feature_engineering - ERROR - Error extracting features for match 54: name 'include_targets' is not defined
2025-07-18 09:00:37,631 - feature_engineering - ERROR - Error extracting features for match 55: name 'include_targets' is not defined
2025-07-18 09:00:37,631 - feature_engineering - ERROR - Error extracting features for match 56: name 'include_targets' is not defined
2025-07-18 09:00:37,633 - feature_engineering - ERROR - Error extracting features for match 57: name 'include_targets' is not defined
2025-07-18 09:00:37,633 - feature_engineering - ERROR - Error extracting features for match 58: name 'include_targets' is not defined
2025-07-18 09:00:37,634 - feature_engineering - ERROR - Error extracting features for match 59: name 'include_targets' is not defined
2025-07-18 09:00:37,634 - feature_engineering - ERROR - Error extracting features for match 60: name 'include_targets' is not defined
2025-07-18 09:00:37,634 - feature_engineering - ERROR - Error extracting features for match 61: name 'include_targets' is not defined
2025-07-18 09:00:37,634 - feature_engineering - ERROR - Error extracting features for match 62: name 'include_targets' is not defined
2025-07-18 09:00:37,634 - feature_engineering - ERROR - Error extracting features for match 63: name 'include_targets' is not defined
2025-07-18 09:00:37,635 - feature_engineering - ERROR - Error extracting features for match 64: name 'include_targets' is not defined
2025-07-18 09:00:37,639 - feature_engineering - ERROR - Error extracting features for match 65: name 'include_targets' is not defined
2025-07-18 09:00:37,642 - feature_engineering - ERROR - Error extracting features for match 66: name 'include_targets' is not defined
2025-07-18 09:00:37,643 - feature_engineering - ERROR - Error extracting features for match 67: name 'include_targets' is not defined
2025-07-18 09:00:37,646 - feature_engineering - ERROR - Error extracting features for match 68: name 'include_targets' is not defined
2025-07-18 09:00:37,646 - feature_engineering - ERROR - Error extracting features for match 69: name 'include_targets' is not defined
2025-07-18 09:00:37,646 - feature_engineering - ERROR - Error extracting features for match 70: name 'include_targets' is not defined
2025-07-18 09:00:37,647 - feature_engineering - ERROR - Error extracting features for match 71: name 'include_targets' is not defined
2025-07-18 09:00:37,647 - feature_engineering - ERROR - Error extracting features for match 72: name 'include_targets' is not defined
2025-07-18 09:00:37,648 - feature_engineering - ERROR - Error extracting features for match 73: name 'include_targets' is not defined
2025-07-18 09:00:37,648 - feature_engineering - ERROR - Error extracting features for match 74: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 75: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 76: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 77: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 78: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 79: name 'include_targets' is not defined
2025-07-18 09:00:37,649 - feature_engineering - ERROR - Error extracting features for match 80: name 'include_targets' is not defined
2025-07-18 09:00:37,650 - feature_engineering - ERROR - Error extracting features for match 81: name 'include_targets' is not defined
2025-07-18 09:00:37,650 - feature_engineering - ERROR - Error extracting features for match 82: name 'include_targets' is not defined
2025-07-18 09:00:37,650 - feature_engineering - ERROR - Error extracting features for match 83: name 'include_targets' is not defined
2025-07-18 09:00:37,650 - feature_engineering - ERROR - Error extracting features for match 84: name 'include_targets' is not defined
2025-07-18 09:00:37,651 - feature_engineering - ERROR - Error extracting features for match 85: name 'include_targets' is not defined
2025-07-18 09:00:37,651 - feature_engineering - ERROR - Error extracting features for match 86: name 'include_targets' is not defined
2025-07-18 09:00:37,651 - feature_engineering - ERROR - Error extracting features for match 87: name 'include_targets' is not defined
2025-07-18 09:00:37,652 - feature_engineering - ERROR - Error extracting features for match 88: name 'include_targets' is not defined
2025-07-18 09:00:37,652 - feature_engineering - ERROR - Error extracting features for match 89: name 'include_targets' is not defined
2025-07-18 09:00:37,661 - feature_engineering - ERROR - Error extracting features for match 90: name 'include_targets' is not defined
2025-07-18 09:00:37,661 - feature_engineering - ERROR - Error extracting features for match 91: name 'include_targets' is not defined
2025-07-18 09:00:37,661 - feature_engineering - ERROR - Error extracting features for match 92: name 'include_targets' is not defined
2025-07-18 09:00:37,662 - feature_engineering - ERROR - Error extracting features for match 93: name 'include_targets' is not defined
2025-07-18 09:00:37,662 - feature_engineering - ERROR - Error extracting features for match 94: name 'include_targets' is not defined
2025-07-18 09:00:37,662 - feature_engineering - ERROR - Error extracting features for match 95: name 'include_targets' is not defined
2025-07-18 09:00:37,662 - feature_engineering - ERROR - Error extracting features for match 96: name 'include_targets' is not defined
2025-07-18 09:00:37,662 - feature_engineering - ERROR - Error extracting features for match 97: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 98: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 99: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 100: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 101: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 102: name 'include_targets' is not defined
2025-07-18 09:00:37,663 - feature_engineering - ERROR - Error extracting features for match 103: name 'include_targets' is not defined
2025-07-18 09:00:37,664 - feature_engineering - ERROR - Error extracting features for match 104: name 'include_targets' is not defined
2025-07-18 09:00:37,664 - feature_engineering - ERROR - Error extracting features for match 105: name 'include_targets' is not defined
2025-07-18 09:00:37,664 - feature_engineering - ERROR - Error extracting features for match 106: name 'include_targets' is not defined
2025-07-18 09:00:37,665 - feature_engineering - ERROR - Error extracting features for match 107: name 'include_targets' is not defined
2025-07-18 09:00:37,665 - feature_engineering - ERROR - Error extracting features for match 108: name 'include_targets' is not defined
2025-07-18 09:00:37,665 - feature_engineering - ERROR - Error extracting features for match 109: name 'include_targets' is not defined
2025-07-18 09:00:37,665 - feature_engineering - ERROR - Error extracting features for match 110: name 'include_targets' is not defined
2025-07-18 09:00:37,665 - feature_engineering - ERROR - Error extracting features for match 111: name 'include_targets' is not defined
2025-07-18 09:00:37,669 - feature_engineering - ERROR - Error extracting features for match 112: name 'include_targets' is not defined
2025-07-18 09:00:37,671 - feature_engineering - ERROR - Error extracting features for match 113: name 'include_targets' is not defined
2025-07-18 09:00:37,672 - feature_engineering - ERROR - Error extracting features for match 114: name 'include_targets' is not defined
2025-07-18 09:00:37,672 - feature_engineering - ERROR - Error extracting features for match 115: name 'include_targets' is not defined
2025-07-18 09:00:37,684 - feature_engineering - ERROR - Error extracting features for match 116: name 'include_targets' is not defined
2025-07-18 09:00:37,684 - feature_engineering - ERROR - Error extracting features for match 117: name 'include_targets' is not defined
2025-07-18 09:00:37,685 - feature_engineering - ERROR - Error extracting features for match 118: name 'include_targets' is not defined
2025-07-18 09:00:37,685 - feature_engineering - ERROR - Error extracting features for match 119: name 'include_targets' is not defined
2025-07-18 09:00:37,691 - feature_engineering - ERROR - Error extracting features for match 120: name 'include_targets' is not defined
2025-07-18 09:00:37,694 - feature_engineering - ERROR - Error extracting features for match 121: name 'include_targets' is not defined
2025-07-18 09:00:37,695 - feature_engineering - ERROR - Error extracting features for match 122: name 'include_targets' is not defined
2025-07-18 09:00:37,695 - feature_engineering - ERROR - Error extracting features for match 123: name 'include_targets' is not defined
2025-07-18 09:00:37,695 - feature_engineering - ERROR - Error extracting features for match 124: name 'include_targets' is not defined
2025-07-18 09:00:37,696 - feature_engineering - ERROR - Error extracting features for match 125: name 'include_targets' is not defined
2025-07-18 09:00:37,696 - feature_engineering - ERROR - Error extracting features for match 126: name 'include_targets' is not defined
2025-07-18 09:00:37,701 - feature_engineering - ERROR - Error extracting features for match 127: name 'include_targets' is not defined
2025-07-18 09:00:37,701 - feature_engineering - ERROR - Error extracting features for match 128: name 'include_targets' is not defined
2025-07-18 09:00:37,702 - feature_engineering - ERROR - Error extracting features for match 129: name 'include_targets' is not defined
2025-07-18 09:00:37,702 - feature_engineering - ERROR - Error extracting features for match 130: name 'include_targets' is not defined
2025-07-18 09:00:37,705 - feature_engineering - ERROR - Error extracting features for match 131: name 'include_targets' is not defined
2025-07-18 09:00:37,708 - feature_engineering - ERROR - Error extracting features for match 132: name 'include_targets' is not defined
2025-07-18 09:00:37,708 - feature_engineering - ERROR - Error extracting features for match 133: name 'include_targets' is not defined
2025-07-18 09:00:37,709 - feature_engineering - ERROR - Error extracting features for match 134: name 'include_targets' is not defined
2025-07-18 09:00:37,710 - feature_engineering - ERROR - Error extracting features for match 135: name 'include_targets' is not defined
2025-07-18 09:00:37,710 - feature_engineering - ERROR - Error extracting features for match 136: name 'include_targets' is not defined
2025-07-18 09:00:37,710 - feature_engineering - ERROR - Error extracting features for match 137: name 'include_targets' is not defined
2025-07-18 09:00:37,711 - feature_engineering - ERROR - Error extracting features for match 138: name 'include_targets' is not defined
2025-07-18 09:00:37,711 - feature_engineering - ERROR - Error extracting features for match 139: name 'include_targets' is not defined
2025-07-18 09:00:37,711 - feature_engineering - ERROR - Error extracting features for match 140: name 'include_targets' is not defined
2025-07-18 09:00:37,712 - feature_engineering - ERROR - Error extracting features for match 141: name 'include_targets' is not defined
2025-07-18 09:00:37,712 - feature_engineering - ERROR - Error extracting features for match 142: name 'include_targets' is not defined
2025-07-18 09:00:37,712 - feature_engineering - ERROR - Error extracting features for match 143: name 'include_targets' is not defined
2025-07-18 09:00:37,717 - feature_engineering - ERROR - Error extracting features for match 144: name 'include_targets' is not defined
2025-07-18 09:00:37,717 - feature_engineering - ERROR - Error extracting features for match 145: name 'include_targets' is not defined
2025-07-18 09:00:37,718 - feature_engineering - ERROR - Error extracting features for match 146: name 'include_targets' is not defined
2025-07-18 09:00:37,719 - feature_engineering - ERROR - Error extracting features for match 147: name 'include_targets' is not defined
2025-07-18 09:00:37,723 - feature_engineering - ERROR - Error extracting features for match 148: name 'include_targets' is not defined
2025-07-18 09:00:37,725 - feature_engineering - ERROR - Error extracting features for match 149: name 'include_targets' is not defined
2025-07-18 09:00:37,726 - feature_engineering - ERROR - Error extracting features for match 150: name 'include_targets' is not defined
2025-07-18 09:00:37,726 - feature_engineering - ERROR - Error extracting features for match 151: name 'include_targets' is not defined
2025-07-18 09:00:37,726 - feature_engineering - ERROR - Error extracting features for match 152: name 'include_targets' is not defined
2025-07-18 09:00:37,726 - feature_engineering - ERROR - Error extracting features for match 153: name 'include_targets' is not defined
2025-07-18 09:00:37,727 - feature_engineering - ERROR - Error extracting features for match 154: name 'include_targets' is not defined
2025-07-18 09:00:37,727 - feature_engineering - ERROR - Error extracting features for match 155: name 'include_targets' is not defined
2025-07-18 09:00:37,727 - feature_engineering - ERROR - Error extracting features for match 156: name 'include_targets' is not defined
2025-07-18 09:00:37,728 - feature_engineering - ERROR - Error extracting features for match 157: name 'include_targets' is not defined
2025-07-18 09:00:37,728 - feature_engineering - ERROR - Error extracting features for match 158: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 159: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 160: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 161: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 162: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 163: name 'include_targets' is not defined
2025-07-18 09:00:37,729 - feature_engineering - ERROR - Error extracting features for match 164: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 165: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 166: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 167: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 168: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 169: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 170: name 'include_targets' is not defined
2025-07-18 09:00:37,730 - feature_engineering - ERROR - Error extracting features for match 171: name 'include_targets' is not defined
2025-07-18 09:00:37,731 - feature_engineering - ERROR - Error extracting features for match 172: name 'include_targets' is not defined
2025-07-18 09:00:37,732 - feature_engineering - ERROR - Error extracting features for match 173: name 'include_targets' is not defined
2025-07-18 09:00:37,732 - feature_engineering - ERROR - Error extracting features for match 174: name 'include_targets' is not defined
2025-07-18 09:00:37,732 - feature_engineering - ERROR - Error extracting features for match 175: name 'include_targets' is not defined
2025-07-18 09:00:37,733 - feature_engineering - ERROR - Error extracting features for match 176: name 'include_targets' is not defined
2025-07-18 09:00:37,733 - feature_engineering - ERROR - Error extracting features for match 177: name 'include_targets' is not defined
2025-07-18 09:00:37,733 - feature_engineering - ERROR - Error extracting features for match 178: name 'include_targets' is not defined
2025-07-18 09:00:37,733 - feature_engineering - ERROR - Error extracting features for match 179: name 'include_targets' is not defined
2025-07-18 09:00:37,733 - feature_engineering - ERROR - Error extracting features for match 180: name 'include_targets' is not defined
2025-07-18 09:00:37,734 - feature_engineering - ERROR - Error extracting features for match 181: name 'include_targets' is not defined
2025-07-18 09:00:37,734 - feature_engineering - ERROR - Error extracting features for match 182: name 'include_targets' is not defined
2025-07-18 09:00:37,739 - feature_engineering - ERROR - Error extracting features for match 183: name 'include_targets' is not defined
2025-07-18 09:00:37,740 - feature_engineering - ERROR - Error extracting features for match 184: name 'include_targets' is not defined
2025-07-18 09:00:37,741 - feature_engineering - ERROR - Error extracting features for match 185: name 'include_targets' is not defined
2025-07-18 09:00:37,741 - feature_engineering - ERROR - Error extracting features for match 186: name 'include_targets' is not defined
2025-07-18 09:00:37,742 - feature_engineering - ERROR - Error extracting features for match 187: name 'include_targets' is not defined
2025-07-18 09:00:37,742 - feature_engineering - ERROR - Error extracting features for match 188: name 'include_targets' is not defined
2025-07-18 09:00:37,743 - feature_engineering - ERROR - Error extracting features for match 189: name 'include_targets' is not defined
2025-07-18 09:00:37,744 - feature_engineering - ERROR - Error extracting features for match 190: name 'include_targets' is not defined
2025-07-18 09:00:37,744 - feature_engineering - ERROR - Error extracting features for match 191: name 'include_targets' is not defined
2025-07-18 09:00:37,744 - feature_engineering - ERROR - Error extracting features for match 192: name 'include_targets' is not defined
2025-07-18 09:00:37,744 - feature_engineering - ERROR - Error extracting features for match 193: name 'include_targets' is not defined
2025-07-18 09:00:37,745 - feature_engineering - ERROR - Error extracting features for match 194: name 'include_targets' is not defined
2025-07-18 09:00:37,747 - feature_engineering - ERROR - Error extracting features for match 195: name 'include_targets' is not defined
2025-07-18 09:00:37,748 - feature_engineering - ERROR - Error extracting features for match 196: name 'include_targets' is not defined
2025-07-18 09:00:37,749 - feature_engineering - ERROR - Error extracting features for match 197: name 'include_targets' is not defined
2025-07-18 09:00:37,750 - feature_engineering - ERROR - Error extracting features for match 198: name 'include_targets' is not defined
2025-07-18 09:00:37,751 - feature_engineering - ERROR - Error extracting features for match 199: name 'include_targets' is not defined
2025-07-18 09:00:37,751 - feature_engineering - ERROR - Error extracting features for match 200: name 'include_targets' is not defined
2025-07-18 09:00:37,752 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:00:37,752 - feature_engineering - ERROR - Error extracting features for match 201: name 'include_targets' is not defined
2025-07-18 09:00:37,752 - feature_engineering - ERROR - Error extracting features for match 202: name 'include_targets' is not defined
2025-07-18 09:00:37,758 - feature_engineering - ERROR - Error extracting features for match 203: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 204: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 205: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 206: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 207: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 208: name 'include_targets' is not defined
2025-07-18 09:00:37,759 - feature_engineering - ERROR - Error extracting features for match 209: name 'include_targets' is not defined
2025-07-18 09:00:37,760 - feature_engineering - ERROR - Error extracting features for match 210: name 'include_targets' is not defined
2025-07-18 09:00:37,760 - feature_engineering - ERROR - Error extracting features for match 211: name 'include_targets' is not defined
2025-07-18 09:00:37,760 - feature_engineering - ERROR - Error extracting features for match 212: name 'include_targets' is not defined
2025-07-18 09:00:37,760 - feature_engineering - ERROR - Error extracting features for match 213: name 'include_targets' is not defined
2025-07-18 09:00:37,760 - feature_engineering - ERROR - Error extracting features for match 214: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 215: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 216: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 217: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 218: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 219: name 'include_targets' is not defined
2025-07-18 09:00:37,761 - feature_engineering - ERROR - Error extracting features for match 220: name 'include_targets' is not defined
2025-07-18 09:00:37,762 - feature_engineering - ERROR - Error extracting features for match 221: name 'include_targets' is not defined
2025-07-18 09:00:37,762 - feature_engineering - ERROR - Error extracting features for match 222: name 'include_targets' is not defined
2025-07-18 09:00:37,762 - feature_engineering - ERROR - Error extracting features for match 223: name 'include_targets' is not defined
2025-07-18 09:00:37,765 - feature_engineering - ERROR - Error extracting features for match 224: name 'include_targets' is not defined
2025-07-18 09:00:37,765 - feature_engineering - ERROR - Error extracting features for match 225: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 226: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 227: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 229: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 230: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 231: name 'include_targets' is not defined
2025-07-18 09:00:37,766 - feature_engineering - ERROR - Error extracting features for match 232: name 'include_targets' is not defined
2025-07-18 09:00:37,767 - feature_engineering - ERROR - Error extracting features for match 233: name 'include_targets' is not defined
2025-07-18 09:00:37,775 - feature_engineering - ERROR - Error extracting features for match 234: name 'include_targets' is not defined
2025-07-18 09:00:37,776 - feature_engineering - ERROR - Error extracting features for match 235: name 'include_targets' is not defined
2025-07-18 09:00:37,776 - feature_engineering - ERROR - Error extracting features for match 236: name 'include_targets' is not defined
2025-07-18 09:00:37,776 - feature_engineering - ERROR - Error extracting features for match 237: name 'include_targets' is not defined
2025-07-18 09:00:37,776 - feature_engineering - ERROR - Error extracting features for match 238: name 'include_targets' is not defined
2025-07-18 09:00:37,776 - feature_engineering - ERROR - Error extracting features for match 239: name 'include_targets' is not defined
2025-07-18 09:00:37,777 - feature_engineering - ERROR - Error extracting features for match 240: name 'include_targets' is not defined
2025-07-18 09:00:37,777 - feature_engineering - ERROR - Error extracting features for match 241: name 'include_targets' is not defined
2025-07-18 09:00:37,777 - feature_engineering - ERROR - Error extracting features for match 242: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 243: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 244: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 245: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 246: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 247: name 'include_targets' is not defined
2025-07-18 09:00:37,778 - feature_engineering - ERROR - Error extracting features for match 248: name 'include_targets' is not defined
2025-07-18 09:00:37,779 - feature_engineering - ERROR - Error extracting features for match 249: name 'include_targets' is not defined
2025-07-18 09:00:37,780 - feature_engineering - ERROR - Error extracting features for match 250: name 'include_targets' is not defined
2025-07-18 09:00:37,780 - feature_engineering - ERROR - Error extracting features for match 251: name 'include_targets' is not defined
2025-07-18 09:00:37,780 - feature_engineering - ERROR - Error extracting features for match 252: name 'include_targets' is not defined
2025-07-18 09:00:37,780 - feature_engineering - ERROR - Error extracting features for match 253: name 'include_targets' is not defined
2025-07-18 09:00:37,781 - feature_engineering - ERROR - Error extracting features for match 254: name 'include_targets' is not defined
2025-07-18 09:00:37,782 - feature_engineering - ERROR - Error extracting features for match 255: name 'include_targets' is not defined
2025-07-18 09:00:37,782 - feature_engineering - ERROR - Error extracting features for match 256: name 'include_targets' is not defined
2025-07-18 09:00:37,783 - feature_engineering - ERROR - Error extracting features for match 257: name 'include_targets' is not defined
2025-07-18 09:00:37,783 - feature_engineering - ERROR - Error extracting features for match 258: name 'include_targets' is not defined
2025-07-18 09:00:37,783 - feature_engineering - ERROR - Error extracting features for match 259: name 'include_targets' is not defined
2025-07-18 09:00:37,783 - feature_engineering - ERROR - Error extracting features for match 260: name 'include_targets' is not defined
2025-07-18 09:00:37,784 - feature_engineering - ERROR - Error extracting features for match 261: name 'include_targets' is not defined
2025-07-18 09:00:37,784 - feature_engineering - ERROR - Error extracting features for match 262: name 'include_targets' is not defined
2025-07-18 09:00:37,785 - feature_engineering - ERROR - Error extracting features for match 264: name 'include_targets' is not defined
2025-07-18 09:00:37,789 - feature_engineering - ERROR - Error extracting features for match 265: name 'include_targets' is not defined
2025-07-18 09:00:37,791 - feature_engineering - ERROR - Error extracting features for match 266: name 'include_targets' is not defined
2025-07-18 09:00:37,791 - feature_engineering - ERROR - Error extracting features for match 267: name 'include_targets' is not defined
2025-07-18 09:00:37,792 - feature_engineering - ERROR - Error extracting features for match 268: name 'include_targets' is not defined
2025-07-18 09:00:37,792 - feature_engineering - ERROR - Error extracting features for match 269: name 'include_targets' is not defined
2025-07-18 09:00:37,792 - feature_engineering - ERROR - Error extracting features for match 270: name 'include_targets' is not defined
2025-07-18 09:00:37,792 - feature_engineering - ERROR - Error extracting features for match 271: name 'include_targets' is not defined
2025-07-18 09:00:37,792 - feature_engineering - ERROR - Error extracting features for match 272: name 'include_targets' is not defined
2025-07-18 09:00:37,793 - feature_engineering - ERROR - Error extracting features for match 273: name 'include_targets' is not defined
2025-07-18 09:00:37,796 - feature_engineering - ERROR - Error extracting features for match 274: name 'include_targets' is not defined
2025-07-18 09:00:37,797 - feature_engineering - ERROR - Error extracting features for match 275: name 'include_targets' is not defined
2025-07-18 09:00:37,797 - feature_engineering - ERROR - Error extracting features for match 276: name 'include_targets' is not defined
2025-07-18 09:00:37,797 - feature_engineering - ERROR - Error extracting features for match 277: name 'include_targets' is not defined
2025-07-18 09:00:37,798 - feature_engineering - ERROR - Error extracting features for match 278: name 'include_targets' is not defined
2025-07-18 09:00:37,798 - feature_engineering - ERROR - Error extracting features for match 279: name 'include_targets' is not defined
2025-07-18 09:00:37,798 - feature_engineering - ERROR - Error extracting features for match 280: name 'include_targets' is not defined
2025-07-18 09:00:37,798 - feature_engineering - ERROR - Error extracting features for match 281: name 'include_targets' is not defined
2025-07-18 09:00:37,816 - feature_engineering - ERROR - Error extracting features for match 282: name 'include_targets' is not defined
2025-07-18 09:00:37,833 - feature_engineering - ERROR - Error extracting features for match 283: name 'include_targets' is not defined
2025-07-18 09:00:37,834 - feature_engineering - ERROR - Error extracting features for match 284: name 'include_targets' is not defined
2025-07-18 09:00:37,834 - feature_engineering - ERROR - Error extracting features for match 285: name 'include_targets' is not defined
2025-07-18 09:00:37,834 - feature_engineering - ERROR - Error extracting features for match 286: name 'include_targets' is not defined
2025-07-18 09:00:37,835 - feature_engineering - ERROR - Error extracting features for match 287: name 'include_targets' is not defined
2025-07-18 09:00:37,835 - feature_engineering - ERROR - Error extracting features for match 288: name 'include_targets' is not defined
2025-07-18 09:00:37,835 - feature_engineering - ERROR - Error extracting features for match 289: name 'include_targets' is not defined
2025-07-18 09:00:37,835 - feature_engineering - ERROR - Error extracting features for match 290: name 'include_targets' is not defined
2025-07-18 09:00:37,836 - feature_engineering - ERROR - Error extracting features for match 291: name 'include_targets' is not defined
2025-07-18 09:00:37,840 - feature_engineering - ERROR - Error extracting features for match 292: name 'include_targets' is not defined
2025-07-18 09:00:37,841 - feature_engineering - ERROR - Error extracting features for match 293: name 'include_targets' is not defined
2025-07-18 09:00:37,842 - feature_engineering - ERROR - Error extracting features for match 294: name 'include_targets' is not defined
2025-07-18 09:00:37,842 - feature_engineering - ERROR - Error extracting features for match 295: name 'include_targets' is not defined
2025-07-18 09:00:37,843 - feature_engineering - ERROR - Error extracting features for match 296: name 'include_targets' is not defined
2025-07-18 09:00:37,843 - feature_engineering - ERROR - Error extracting features for match 297: name 'include_targets' is not defined
2025-07-18 09:00:37,843 - feature_engineering - ERROR - Error extracting features for match 298: name 'include_targets' is not defined
2025-07-18 09:00:37,843 - feature_engineering - ERROR - Error extracting features for match 299: name 'include_targets' is not defined
2025-07-18 09:00:37,843 - feature_engineering - ERROR - Error extracting features for match 300: name 'include_targets' is not defined
2025-07-18 09:00:37,844 - feature_engineering - ERROR - Error extracting features for match 301: name 'include_targets' is not defined
2025-07-18 09:00:37,844 - feature_engineering - ERROR - Error extracting features for match 302: name 'include_targets' is not defined
2025-07-18 09:00:37,844 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:00:37,844 - feature_engineering - ERROR - Error extracting features for match 303: name 'include_targets' is not defined
2025-07-18 09:00:37,844 - feature_engineering - ERROR - Error extracting features for match 304: name 'include_targets' is not defined
2025-07-18 09:00:37,845 - feature_engineering - ERROR - Error extracting features for match 305: name 'include_targets' is not defined
2025-07-18 09:00:37,845 - feature_engineering - ERROR - Error extracting features for match 306: name 'include_targets' is not defined
2025-07-18 09:00:37,845 - feature_engineering - ERROR - Error extracting features for match 307: name 'include_targets' is not defined
2025-07-18 09:00:37,846 - feature_engineering - ERROR - Error extracting features for match 308: name 'include_targets' is not defined
2025-07-18 09:00:37,847 - feature_engineering - ERROR - Error extracting features for match 309: name 'include_targets' is not defined
2025-07-18 09:00:37,847 - feature_engineering - ERROR - Error extracting features for match 310: name 'include_targets' is not defined
2025-07-18 09:00:37,847 - feature_engineering - ERROR - Error extracting features for match 311: name 'include_targets' is not defined
2025-07-18 09:00:37,848 - feature_engineering - ERROR - Error extracting features for match 312: name 'include_targets' is not defined
2025-07-18 09:00:37,848 - feature_engineering - ERROR - Error extracting features for match 313: name 'include_targets' is not defined
2025-07-18 09:00:37,848 - feature_engineering - ERROR - Error extracting features for match 314: name 'include_targets' is not defined
2025-07-18 09:00:37,848 - feature_engineering - ERROR - Error extracting features for match 315: name 'include_targets' is not defined
2025-07-18 09:00:37,849 - feature_engineering - ERROR - Error extracting features for match 316: name 'include_targets' is not defined
2025-07-18 09:00:37,849 - feature_engineering - ERROR - Error extracting features for match 317: name 'include_targets' is not defined
2025-07-18 09:00:37,849 - feature_engineering - ERROR - Error extracting features for match 318: name 'include_targets' is not defined
2025-07-18 09:00:37,850 - feature_engineering - ERROR - Error extracting features for match 319: name 'include_targets' is not defined
2025-07-18 09:00:37,850 - feature_engineering - ERROR - Error extracting features for match 320: name 'include_targets' is not defined
2025-07-18 09:00:37,856 - feature_engineering - ERROR - Error extracting features for match 321: name 'include_targets' is not defined
2025-07-18 09:00:37,857 - feature_engineering - ERROR - Error extracting features for match 322: name 'include_targets' is not defined
2025-07-18 09:00:37,858 - feature_engineering - ERROR - Error extracting features for match 323: name 'include_targets' is not defined
2025-07-18 09:00:37,859 - feature_engineering - ERROR - Error extracting features for match 324: name 'include_targets' is not defined
2025-07-18 09:00:37,859 - feature_engineering - ERROR - Error extracting features for match 325: name 'include_targets' is not defined
2025-07-18 09:00:37,861 - feature_engineering - ERROR - Error extracting features for match 326: name 'include_targets' is not defined
2025-07-18 09:00:37,861 - feature_engineering - ERROR - Error extracting features for match 327: name 'include_targets' is not defined
2025-07-18 09:00:37,862 - feature_engineering - ERROR - Error extracting features for match 328: name 'include_targets' is not defined
2025-07-18 09:00:37,863 - feature_engineering - ERROR - Error extracting features for match 329: name 'include_targets' is not defined
2025-07-18 09:00:37,863 - feature_engineering - ERROR - Error extracting features for match 330: name 'include_targets' is not defined
2025-07-18 09:00:37,864 - feature_engineering - ERROR - Error extracting features for match 331: name 'include_targets' is not defined
2025-07-18 09:00:37,864 - feature_engineering - ERROR - Error extracting features for match 332: name 'include_targets' is not defined
2025-07-18 09:00:37,864 - feature_engineering - ERROR - Error extracting features for match 333: name 'include_targets' is not defined
2025-07-18 09:00:37,865 - feature_engineering - ERROR - Error extracting features for match 334: name 'include_targets' is not defined
2025-07-18 09:00:37,865 - feature_engineering - ERROR - Error extracting features for match 335: name 'include_targets' is not defined
2025-07-18 09:00:37,865 - feature_engineering - ERROR - Error extracting features for match 336: name 'include_targets' is not defined
2025-07-18 09:00:37,865 - feature_engineering - ERROR - Error extracting features for match 337: name 'include_targets' is not defined
2025-07-18 09:00:37,866 - feature_engineering - ERROR - Error extracting features for match 338: name 'include_targets' is not defined
2025-07-18 09:00:37,866 - feature_engineering - ERROR - Error extracting features for match 339: name 'include_targets' is not defined
2025-07-18 09:00:37,866 - feature_engineering - ERROR - Error extracting features for match 263: name 'include_targets' is not defined
2025-07-18 09:00:37,866 - feature_engineering - ERROR - Error extracting features for match 341: name 'include_targets' is not defined
2025-07-18 09:00:37,866 - feature_engineering - ERROR - Error extracting features for match 342: name 'include_targets' is not defined
2025-07-18 09:00:37,867 - feature_engineering - ERROR - Error extracting features for match 343: name 'include_targets' is not defined
2025-07-18 09:00:37,867 - feature_engineering - ERROR - Error extracting features for match 344: name 'include_targets' is not defined
2025-07-18 09:00:37,868 - feature_engineering - ERROR - Error extracting features for match 345: name 'include_targets' is not defined
2025-07-18 09:00:37,872 - feature_engineering - ERROR - Error extracting features for match 346: name 'include_targets' is not defined
2025-07-18 09:00:37,875 - feature_engineering - ERROR - Error extracting features for match 347: name 'include_targets' is not defined
2025-07-18 09:00:37,875 - feature_engineering - ERROR - Error extracting features for match 348: name 'include_targets' is not defined
2025-07-18 09:00:37,876 - feature_engineering - ERROR - Error extracting features for match 349: name 'include_targets' is not defined
2025-07-18 09:00:37,877 - feature_engineering - ERROR - Error extracting features for match 350: name 'include_targets' is not defined
2025-07-18 09:00:37,877 - feature_engineering - ERROR - Error extracting features for match 351: name 'include_targets' is not defined
2025-07-18 09:00:37,878 - feature_engineering - ERROR - Error extracting features for match 352: name 'include_targets' is not defined
2025-07-18 09:00:37,878 - feature_engineering - ERROR - Error extracting features for match 353: name 'include_targets' is not defined
2025-07-18 09:00:37,878 - feature_engineering - ERROR - Error extracting features for match 354: name 'include_targets' is not defined
2025-07-18 09:00:37,878 - feature_engineering - ERROR - Error extracting features for match 355: name 'include_targets' is not defined
2025-07-18 09:00:37,878 - feature_engineering - ERROR - Error extracting features for match 356: name 'include_targets' is not defined
2025-07-18 09:00:37,879 - feature_engineering - ERROR - Error extracting features for match 357: name 'include_targets' is not defined
2025-07-18 09:00:37,879 - feature_engineering - ERROR - Error extracting features for match 358: name 'include_targets' is not defined
2025-07-18 09:00:37,879 - feature_engineering - ERROR - Error extracting features for match 359: name 'include_targets' is not defined
2025-07-18 09:00:37,879 - feature_engineering - ERROR - Error extracting features for match 360: name 'include_targets' is not defined
2025-07-18 09:00:37,879 - feature_engineering - ERROR - Error extracting features for match 361: name 'include_targets' is not defined
2025-07-18 09:00:37,880 - feature_engineering - ERROR - Error extracting features for match 362: name 'include_targets' is not defined
2025-07-18 09:00:37,880 - feature_engineering - ERROR - Error extracting features for match 363: name 'include_targets' is not defined
2025-07-18 09:00:37,880 - feature_engineering - ERROR - Error extracting features for match 364: name 'include_targets' is not defined
2025-07-18 09:00:37,880 - feature_engineering - ERROR - Error extracting features for match 365: name 'include_targets' is not defined
2025-07-18 09:00:37,881 - feature_engineering - ERROR - Error extracting features for match 366: name 'include_targets' is not defined
2025-07-18 09:00:37,881 - feature_engineering - ERROR - Error extracting features for match 367: name 'include_targets' is not defined
2025-07-18 09:00:37,882 - feature_engineering - ERROR - Error extracting features for match 368: name 'include_targets' is not defined
2025-07-18 09:00:37,882 - feature_engineering - ERROR - Error extracting features for match 369: name 'include_targets' is not defined
2025-07-18 09:00:37,882 - feature_engineering - ERROR - Error extracting features for match 370: name 'include_targets' is not defined
2025-07-18 09:00:37,882 - feature_engineering - ERROR - Error extracting features for match 371: name 'include_targets' is not defined
2025-07-18 09:00:37,884 - feature_engineering - ERROR - Error extracting features for match 372: name 'include_targets' is not defined
2025-07-18 09:00:37,884 - feature_engineering - ERROR - Error extracting features for match 373: name 'include_targets' is not defined
2025-07-18 09:00:37,884 - feature_engineering - ERROR - Error extracting features for match 374: name 'include_targets' is not defined
2025-07-18 09:00:37,884 - feature_engineering - ERROR - Error extracting features for match 375: name 'include_targets' is not defined
2025-07-18 09:00:37,885 - feature_engineering - ERROR - Error extracting features for match 376: name 'include_targets' is not defined
2025-07-18 09:00:37,885 - feature_engineering - ERROR - Error extracting features for match 377: name 'include_targets' is not defined
2025-07-18 09:00:37,889 - feature_engineering - ERROR - Error extracting features for match 378: name 'include_targets' is not defined
2025-07-18 09:00:37,890 - feature_engineering - ERROR - Error extracting features for match 379: name 'include_targets' is not defined
2025-07-18 09:00:37,890 - feature_engineering - ERROR - Error extracting features for match 380: name 'include_targets' is not defined
2025-07-18 09:00:37,892 - feature_engineering - ERROR - Error extracting features for match 228: name 'include_targets' is not defined
2025-07-18 09:00:37,893 - feature_engineering - ERROR - Error extracting features for match 340: name 'include_targets' is not defined
2025-07-18 09:00:38,005 - feature_engineering - INFO - Feature matrix created with shape: (380, 118)
2025-07-18 09:00:38,006 - train_models - INFO - Feature matrix created: (380, 118)
2025-07-18 09:00:38,006 - train_models - ERROR - Target column 'result' not found. Available columns: ['home_overall_form', 'home_recent_form', 'home_home_form', 'home_away_form', 'home_win_percentage', 'home_draw_percentage', 'home_loss_percentage', 'home_goals_scored_avg', 'home_goals_conceded_avg', 'home_goal_difference_avg', 'home_weighted_form', 'home_form_trend', 'home_consistency', 'away_overall_form', 'away_recent_form', 'away_home_form', 'away_away_form', 'away_win_percentage', 'away_draw_percentage', 'away_loss_percentage', 'away_goals_scored_avg', 'away_goals_conceded_avg', 'away_goal_difference_avg', 'away_weighted_form', 'away_form_trend', 'away_consistency', 'form_difference', 'goal_difference_comparison', 'win_percentage_difference', 'home_team_home_form', 'home_team_home_advantage_form', 'home_team_home_win_rate', 'home_team_home_advantage_win_rate', 'home_team_home_goals_scored_avg', 'home_team_home_goals_conceded_avg', 'home_team_home_goal_difference_avg', 'home_team_home_advantage_goals', 'home_team_home_defensive_advantage', 'home_team_home_matches_played', 'away_team_away_form', 'away_team_away_disadvantage_form', 'away_team_away_win_rate', 'away_team_away_disadvantage_win_rate', 'away_team_away_goals_scored_avg', 'away_team_away_goals_conceded_avg', 'away_team_away_goal_difference_avg', 'away_team_away_disadvantage_goals', 'away_team_away_defensive_disadvantage', 'away_team_away_matches_played', 'venue_familiarity_home', 'venue_familiarity_away', 'home_away_form_difference', 'home_away_advantage_difference', 'home_away_win_rate_difference', 'home_away_goal_difference', 'h2h_matches_played', 'h2h_home_win_rate', 'h2h_draw_rate', 'h2h_away_win_rate', 'h2h_home_goals_per_match', 'h2h_away_goals_per_match', 'h2h_goal_difference', 'h2h_total_goals_per_match', 'h2h_over_2_5_rate', 'recent_h2h_matches_played', 'recent_h2h_home_win_rate', 'recent_h2h_draw_rate', 'recent_h2h_away_win_rate', 'recent_h2h_home_goals_per_match', 'recent_h2h_away_goals_per_match', 'recent_h2h_goal_difference', 'recent_h2h_total_goals_per_match', 'recent_h2h_over_2_5_rate', 'h2h_home_as_home_win_rate', 'h2h_home_as_home_matches', 'h2h_home_as_away_win_rate', 'h2h_home_as_away_matches', 'h2h_both_teams_score_rate', 'h2h_clean_sheet_rate_home', 'h2h_clean_sheet_rate_away', 'h2h_high_scoring_rate', 'h2h_home_trend', 'h2h_goals_trend', 'h2h_competitiveness_trend', 'home_total_injuries', 'home_injury_rate', 'home_goalkeeper_injured', 'home_defense_injury_impact', 'home_midfield_injury_impact', 'home_attack_injury_impact', 'home_severe_injuries', 'home_moderate_injuries', 'home_minor_injuries', 'home_key_players_injured', 'home_injury_weighted_impact', 'away_total_injuries', 'away_injury_rate', 'away_goalkeeper_injured', 'away_defense_injury_impact', 'away_midfield_injury_impact', 'away_attack_injury_impact', 'away_severe_injuries', 'away_moderate_injuries', 'away_minor_injuries', 'away_key_players_injured', 'away_injury_weighted_impact', 'injury_impact_difference', 'injury_rate_difference', 'key_players_difference', 'match_id', 'home_team_id', 'away_team_id', 'match_date', 'league_id', 'season', 'form_advantage', 'expected_home_goals', 'expected_away_goals']
2025-07-18 09:02:50,695 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:02:50,696 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:02:50,758 - database - INFO - Created database engine for sqlite
2025-07-18 09:02:50,883 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:02:50,888 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:02:50,928 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:02:50,929 - train_models - INFO - Extracting features...
2025-07-18 09:02:50,930 - database - INFO - Created database engine for sqlite
2025-07-18 09:02:50,930 - database - INFO - Created database engine for sqlite
2025-07-18 09:02:50,932 - database - INFO - Created database engine for sqlite
2025-07-18 09:02:50,933 - database - INFO - Created database engine for sqlite
2025-07-18 09:02:50,947 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:02:50,947 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:02:50,947 - train_models - ERROR - Training failed: name 'include_targets' is not defined
2025-07-18 09:06:37,844 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:06:37,844 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:06:37,889 - database - INFO - Created database engine for sqlite
2025-07-18 09:06:38,029 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:06:38,039 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:06:38,100 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:06:38,101 - train_models - INFO - Extracting features...
2025-07-18 09:06:38,102 - database - INFO - Created database engine for sqlite
2025-07-18 09:06:38,103 - database - INFO - Created database engine for sqlite
2025-07-18 09:06:38,104 - database - INFO - Created database engine for sqlite
2025-07-18 09:06:38,104 - database - INFO - Created database engine for sqlite
2025-07-18 09:06:38,116 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:06:38,116 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:06:38,164 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:06:38,172 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:06:38,173 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:06:38,290 - feature_engineering - INFO - Feature matrix created with shape: (380, 129)
2025-07-18 09:06:38,290 - train_models - INFO - Feature matrix created: (380, 129)
2025-07-18 09:06:38,463 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:06:38,472 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:06:38,475 - model_trainer - INFO - Data prepared:
2025-07-18 09:06:38,475 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:06:38,476 - model_trainer - INFO -   Features: 120
2025-07-18 09:06:38,476 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:06:38,476 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:06:38,476 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:06:38,476 - model_trainer - INFO - Starting model training...
2025-07-18 09:06:38,476 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 09:06:38,479 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:06:38,480 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:06:39,169 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:06:39,239 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:06:39,239 - model.random_forest_football - INFO - Training completed in 0.56 seconds
2025-07-18 09:06:39,308 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:06:39,308 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:06:39,309 - model_trainer - INFO - Model training completed
2025-07-18 09:06:39,309 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:06:39,329 - train_models - INFO - Saving models...
2025-07-18 09:06:39,472 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_090639.joblib
2025-07-18 09:06:39,472 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_090639.joblib
2025-07-18 09:06:39,474 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_090639.json
2025-07-18 09:06:39,475 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 09:10:54,679 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:10:54,683 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:10:54,734 - database - INFO - Created database engine for sqlite
2025-07-18 09:10:54,884 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:10:54,893 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:10:54,939 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:10:54,939 - train_models - INFO - Extracting features...
2025-07-18 09:10:54,940 - database - INFO - Created database engine for sqlite
2025-07-18 09:10:54,941 - database - INFO - Created database engine for sqlite
2025-07-18 09:10:54,941 - database - INFO - Created database engine for sqlite
2025-07-18 09:10:54,942 - database - INFO - Created database engine for sqlite
2025-07-18 09:10:54,945 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:10:54,946 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:10:54,982 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:10:54,995 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:10:54,995 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:10:55,083 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 09:10:55,084 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 09:10:55,202 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:10:55,210 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:10:55,212 - model_trainer - INFO - Data prepared:
2025-07-18 09:10:55,213 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:10:55,213 - model_trainer - INFO -   Features: 116
2025-07-18 09:10:55,213 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:10:55,213 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:10:55,213 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:10:55,213 - model_trainer - INFO - Starting model training...
2025-07-18 09:10:55,215 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 09:10:55,217 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:10:55,217 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:10:55,678 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:10:55,764 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:10:55,764 - model.random_forest_football - INFO - Training completed in 0.29 seconds
2025-07-18 09:10:55,851 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:10:55,851 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:10:55,851 - model_trainer - INFO - Model training completed
2025-07-18 09:10:55,851 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:10:55,867 - train_models - INFO - Saving models...
2025-07-18 09:10:55,925 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_091055.joblib
2025-07-18 09:10:55,925 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_091055.joblib
2025-07-18 09:10:55,926 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_091055.json
2025-07-18 09:10:55,930 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 09:16:56,558 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:16:56,565 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:16:56,728 - database - INFO - Created database engine for sqlite
2025-07-18 09:16:57,276 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:16:57,290 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:16:57,508 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:16:57,508 - train_models - INFO - Extracting features...
2025-07-18 09:16:57,509 - database - INFO - Created database engine for sqlite
2025-07-18 09:16:57,510 - database - INFO - Created database engine for sqlite
2025-07-18 09:16:57,510 - database - INFO - Created database engine for sqlite
2025-07-18 09:16:57,511 - database - INFO - Created database engine for sqlite
2025-07-18 09:16:57,516 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:16:57,517 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:16:57,568 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:16:57,624 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:16:57,625 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:16:57,908 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 09:16:57,908 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 09:16:58,782 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:16:58,791 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:16:58,816 - model_trainer - INFO - Data prepared:
2025-07-18 09:16:58,816 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:16:58,817 - model_trainer - INFO -   Features: 116
2025-07-18 09:16:58,817 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:16:58,817 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:16:58,817 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:16:58,817 - model_trainer - INFO - Starting model training...
2025-07-18 09:16:58,818 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 09:16:58,818 - model_trainer - INFO - Training xgboost model...
2025-07-18 09:16:58,818 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 09:16:58,900 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 09:16:58,900 - model_trainer - INFO - Training lightgbm model...
2025-07-18 09:16:58,900 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 09:16:58,910 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 09:16:58,910 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:16:58,910 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:16:59,380 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:16:59,466 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:16:59,466 - model.random_forest_football - INFO - Training completed in 0.30 seconds
2025-07-18 09:16:59,534 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:16:59,535 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:16:59,536 - model_trainer - INFO - Model training completed
2025-07-18 09:16:59,536 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:16:59,550 - train_models - INFO - Saving models...
2025-07-18 09:16:59,615 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_091659.joblib
2025-07-18 09:16:59,615 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_091659.joblib
2025-07-18 09:16:59,616 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_091659.json
2025-07-18 09:16:59,622 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 09:20:55,874 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:20:55,875 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'neural_network,ensemble,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:20:56,081 - database - INFO - Created database engine for sqlite
2025-07-18 09:20:56,289 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:20:56,364 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:20:56,577 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:20:56,577 - train_models - INFO - Extracting features...
2025-07-18 09:20:56,578 - database - INFO - Created database engine for sqlite
2025-07-18 09:20:56,578 - database - INFO - Created database engine for sqlite
2025-07-18 09:20:56,578 - database - INFO - Created database engine for sqlite
2025-07-18 09:20:56,579 - database - INFO - Created database engine for sqlite
2025-07-18 09:20:56,583 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:20:56,596 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:20:56,627 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:20:56,643 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:20:56,664 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:20:56,780 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 09:20:56,781 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 09:20:57,277 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:20:57,281 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:20:57,287 - model_trainer - INFO - Data prepared:
2025-07-18 09:20:57,287 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:20:57,287 - model_trainer - INFO -   Features: 116
2025-07-18 09:20:57,287 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:20:57,287 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:20:57,288 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:20:57,288 - model_trainer - INFO - Starting model training...
2025-07-18 09:20:57,288 - model_trainer - INFO - Training models: ['neural_network', 'ensemble', 'random_forest']
2025-07-18 09:20:57,288 - model_trainer - INFO - Training neural_network model...
2025-07-18 09:20:57,290 - model.neural_network_football - INFO - Training neural_network_football model...
2025-07-18 09:20:57,462 - model_trainer - ERROR - Failed to train neural_network: NeuralNetworkFootballModel._calculate_feature_importance() missing 2 required positional arguments: 'X' and 'y'
2025-07-18 09:20:57,462 - model_trainer - INFO - Training ensemble model...
2025-07-18 09:20:57,463 - model.ensemble_football - INFO - Training ensemble_football model...
2025-07-18 09:21:03,149 - model_trainer - ERROR - Failed to train ensemble: EnsembleFootballModel._calculate_feature_importance() missing 2 required positional arguments: 'X' and 'y'
2025-07-18 09:21:03,151 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:21:03,153 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:21:03,491 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:21:03,564 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:21:03,564 - model.random_forest_football - INFO - Training completed in 0.20 seconds
2025-07-18 09:21:03,629 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:21:03,629 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:21:03,629 - model_trainer - INFO - Model training completed
2025-07-18 09:21:03,629 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:21:03,645 - train_models - INFO - Saving models...
2025-07-18 09:21:03,703 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_092103.joblib
2025-07-18 09:21:03,703 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_092103.joblib
2025-07-18 09:21:03,705 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_092103.json
2025-07-18 09:21:03,707 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 09:24:07,232 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:24:07,234 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'neural_network,ensemble,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:24:07,264 - database - INFO - Created database engine for sqlite
2025-07-18 09:24:07,400 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:24:07,406 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:24:07,457 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:24:07,457 - train_models - INFO - Extracting features...
2025-07-18 09:24:07,458 - database - INFO - Created database engine for sqlite
2025-07-18 09:24:07,458 - database - INFO - Created database engine for sqlite
2025-07-18 09:24:07,459 - database - INFO - Created database engine for sqlite
2025-07-18 09:24:07,459 - database - INFO - Created database engine for sqlite
2025-07-18 09:24:07,463 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:24:07,464 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:24:07,491 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:24:07,511 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:24:07,512 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:24:07,604 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 09:24:07,604 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 09:24:07,814 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:24:07,831 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:24:07,843 - model_trainer - INFO - Data prepared:
2025-07-18 09:24:07,843 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:24:07,844 - model_trainer - INFO -   Features: 116
2025-07-18 09:24:07,844 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:24:07,844 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:24:07,844 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:24:07,844 - model_trainer - INFO - Starting model training...
2025-07-18 09:24:07,845 - model_trainer - INFO - Training models: ['neural_network', 'ensemble', 'random_forest']
2025-07-18 09:24:07,845 - model_trainer - INFO - Training neural_network model...
2025-07-18 09:24:07,845 - model.neural_network_football - INFO - Training neural_network_football model...
2025-07-18 09:24:08,023 - model.neural_network_football - INFO - Test accuracy: 0.8872
2025-07-18 09:24:08,045 - model.neural_network_football - INFO - Test accuracy: 0.7632
2025-07-18 09:24:08,046 - model.neural_network_football - INFO - Training completed in 0.16 seconds
2025-07-18 09:24:08,071 - model.neural_network_football - INFO - Test accuracy: 0.8816
2025-07-18 09:24:08,071 - model_trainer - INFO - neural_network training completed. Test accuracy: 0.8816
2025-07-18 09:24:08,071 - model_trainer - INFO - Training ensemble model...
2025-07-18 09:24:08,071 - model.ensemble_football - INFO - Training ensemble_football model...
2025-07-18 09:24:13,192 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,359 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,360 - model.ensemble_football - INFO - Training completed in 4.96 seconds
2025-07-18 09:24:13,490 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,490 - model_trainer - INFO - ensemble training completed. Test accuracy: 1.0000
2025-07-18 09:24:13,490 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:24:13,490 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:24:13,801 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,873 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,873 - model.random_forest_football - INFO - Training completed in 0.19 seconds
2025-07-18 09:24:13,944 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:13,944 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:24:13,945 - model_trainer - INFO - Training ensemble model...
2025-07-18 09:24:13,945 - model.ensemble - INFO - Initialized ensemble with 3 base models using weighted_average
2025-07-18 09:24:13,945 - model.ensemble - INFO - Training ensemble model...
2025-07-18 09:24:13,945 - model.ensemble - INFO - Training base models...
2025-07-18 09:24:13,945 - model.ensemble - INFO - Training base model 1/3: neural_network_football
2025-07-18 09:24:13,945 - model.neural_network_football - INFO - Training neural_network_football model...
2025-07-18 09:24:14,085 - model.neural_network_football - INFO - Test accuracy: 0.8872
2025-07-18 09:24:14,086 - model.neural_network_football - INFO - Training completed in 0.12 seconds
2025-07-18 09:24:14,086 - model.ensemble - INFO - Training base model 2/3: ensemble_football
2025-07-18 09:24:14,086 - model.ensemble_football - INFO - Training ensemble_football model...
2025-07-18 09:24:14,609 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:14,609 - model.ensemble_football - INFO - Training completed in 0.35 seconds
2025-07-18 09:24:14,609 - model.ensemble - INFO - Training base model 3/3: random_forest_football
2025-07-18 09:24:14,610 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:24:14,990 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:24:14,990 - model.random_forest_football - INFO - Training completed in 0.26 seconds
2025-07-18 09:24:17,319 - model.ensemble - INFO - Model weights: {'neural_network_football': np.float64(0.27605118829981723), 'ensemble_football': np.float64(0.36197440585009144), 'random_forest_football': np.float64(0.36197440585009144)}
2025-07-18 09:24:17,319 - model.ensemble - INFO - Ensemble training completed
2025-07-18 09:24:17,320 - model.ensemble - INFO - Feature importance not available for this model type
2025-07-18 09:24:17,873 - model.ensemble - INFO - Training completed in 3.37 seconds
2025-07-18 09:24:18,191 - model_trainer - INFO - Ensemble training completed. Test accuracy: 1.0000
2025-07-18 09:24:18,191 - model_trainer - INFO - Model training completed
2025-07-18 09:24:18,191 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:24:18,209 - train_models - ERROR - Training failed: 'dict' object has no attribute 'empty'
2025-07-18 09:25:50,990 - train_models - INFO - Starting football prediction model training...
2025-07-18 09:25:50,994 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'neural_network,ensemble,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 09:25:51,034 - database - INFO - Created database engine for sqlite
2025-07-18 09:25:51,156 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 09:25:51,167 - train_models - INFO - Validating and cleaning data...
2025-07-18 09:25:51,212 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 09:25:51,213 - train_models - INFO - Extracting features...
2025-07-18 09:25:51,219 - database - INFO - Created database engine for sqlite
2025-07-18 09:25:51,220 - database - INFO - Created database engine for sqlite
2025-07-18 09:25:51,230 - database - INFO - Created database engine for sqlite
2025-07-18 09:25:51,230 - database - INFO - Created database engine for sqlite
2025-07-18 09:25:51,240 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 09:25:51,240 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 09:25:51,276 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 09:25:51,277 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 09:25:51,277 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 09:25:51,370 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 09:25:51,370 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 09:25:51,586 - model_trainer - INFO - Preparing data for training...
2025-07-18 09:25:51,591 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 09:25:51,604 - model_trainer - INFO - Data prepared:
2025-07-18 09:25:51,604 - model_trainer - INFO -   Total samples: 380
2025-07-18 09:25:51,604 - model_trainer - INFO -   Features: 116
2025-07-18 09:25:51,604 - model_trainer - INFO -   Train samples: 266
2025-07-18 09:25:51,604 - model_trainer - INFO -   Validation samples: 38
2025-07-18 09:25:51,604 - model_trainer - INFO -   Test samples: 76
2025-07-18 09:25:51,605 - model_trainer - INFO - Starting model training...
2025-07-18 09:25:51,605 - model_trainer - INFO - Training models: ['neural_network', 'ensemble', 'random_forest']
2025-07-18 09:25:51,605 - model_trainer - INFO - Training neural_network model...
2025-07-18 09:25:51,605 - model.neural_network_football - INFO - Training neural_network_football model...
2025-07-18 09:25:51,765 - model.neural_network_football - INFO - Test accuracy: 0.8872
2025-07-18 09:25:51,788 - model.neural_network_football - INFO - Test accuracy: 0.7632
2025-07-18 09:25:51,788 - model.neural_network_football - INFO - Training completed in 0.14 seconds
2025-07-18 09:25:51,809 - model.neural_network_football - INFO - Test accuracy: 0.8816
2025-07-18 09:25:51,810 - model_trainer - INFO - neural_network training completed. Test accuracy: 0.8816
2025-07-18 09:25:51,810 - model_trainer - INFO - Training ensemble model...
2025-07-18 09:25:51,810 - model.ensemble_football - INFO - Training ensemble_football model...
2025-07-18 09:25:56,918 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,090 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,090 - model.ensemble_football - INFO - Training completed in 4.94 seconds
2025-07-18 09:25:57,209 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,209 - model_trainer - INFO - ensemble training completed. Test accuracy: 1.0000
2025-07-18 09:25:57,209 - model_trainer - INFO - Training random_forest model...
2025-07-18 09:25:57,209 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:25:57,524 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,589 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,589 - model.random_forest_football - INFO - Training completed in 0.20 seconds
2025-07-18 09:25:57,657 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:57,657 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 09:25:57,658 - model_trainer - INFO - Training ensemble model...
2025-07-18 09:25:57,658 - model.ensemble - INFO - Initialized ensemble with 3 base models using weighted_average
2025-07-18 09:25:57,658 - model.ensemble - INFO - Training ensemble model...
2025-07-18 09:25:57,658 - model.ensemble - INFO - Training base models...
2025-07-18 09:25:57,658 - model.ensemble - INFO - Training base model 1/3: neural_network_football
2025-07-18 09:25:57,658 - model.neural_network_football - INFO - Training neural_network_football model...
2025-07-18 09:25:57,823 - model.neural_network_football - INFO - Test accuracy: 0.8872
2025-07-18 09:25:57,823 - model.neural_network_football - INFO - Training completed in 0.15 seconds
2025-07-18 09:25:57,824 - model.ensemble - INFO - Training base model 2/3: ensemble_football
2025-07-18 09:25:57,824 - model.ensemble_football - INFO - Training ensemble_football model...
2025-07-18 09:25:58,569 - model.ensemble_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:58,569 - model.ensemble_football - INFO - Training completed in 0.51 seconds
2025-07-18 09:25:58,570 - model.ensemble - INFO - Training base model 3/3: random_forest_football
2025-07-18 09:25:58,570 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 09:25:58,918 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 09:25:58,919 - model.random_forest_football - INFO - Training completed in 0.23 seconds
2025-07-18 09:26:01,051 - model.ensemble - INFO - Model weights: {'neural_network_football': np.float64(0.27605118829981723), 'ensemble_football': np.float64(0.36197440585009144), 'random_forest_football': np.float64(0.36197440585009144)}
2025-07-18 09:26:01,052 - model.ensemble - INFO - Ensemble training completed
2025-07-18 09:26:01,052 - model.ensemble - INFO - Feature importance not available for this model type
2025-07-18 09:26:01,331 - model.ensemble - INFO - Training completed in 3.39 seconds
2025-07-18 09:26:01,671 - model_trainer - INFO - Ensemble training completed. Test accuracy: 1.0000
2025-07-18 09:26:01,672 - model_trainer - INFO - Model training completed
2025-07-18 09:26:01,672 - train_models - INFO - Training completed! Generating report...
2025-07-18 09:26:01,685 - train_models - INFO - Saving models...
2025-07-18 09:26:01,692 - model.neural_network_football - INFO - Model saved to data/models/neural_network_model_20250718_092601.joblib
2025-07-18 09:26:01,693 - model_trainer - INFO - Saved neural_network to data/models/neural_network_model_20250718_092601.joblib
2025-07-18 09:26:01,694 - model.ensemble - INFO - Model saved to data/models/ensemble_model_20250718_092601.joblib
2025-07-18 09:26:01,694 - model_trainer - INFO - Saved ensemble to data/models/ensemble_model_20250718_092601.joblib
2025-07-18 09:26:02,055 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_092601.joblib
2025-07-18 09:26:02,056 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_092601.joblib
2025-07-18 09:26:02,058 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_092601.json
2025-07-18 09:26:02,059 - train_models - ERROR - Training failed: 'dict' object has no attribute 'empty'
