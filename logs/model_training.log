2025-07-18 05:09:33,259 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:09:33,259 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:09:33,290 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:33,342 - data_loader - INFO - Loaded 20 matches from database
2025-07-18 05:09:33,343 - train_models - ERROR - Insufficient data: 20 samples (minimum: 50)
2025-07-18 05:09:43,928 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:09:43,928 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:09:43,947 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:43,999 - data_loader - INFO - Loaded 20 matches from database
2025-07-18 05:09:44,001 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:09:44,062 - data_cleaner - INFO - Cleaned matches data: 20 matches remaining
2025-07-18 05:09:44,062 - train_models - INFO - Extracting features...
2025-07-18 05:09:44,064 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,064 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,065 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,066 - database - INFO - Created database engine for sqlite
2025-07-18 05:09:44,070 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:09:44,071 - feature_engineering - INFO - Starting feature extraction for 20 matches
2025-07-18 05:09:44,130 - feature_engineering - INFO - Feature matrix created with shape: (20, 125)
2025-07-18 05:09:44,130 - train_models - INFO - Feature matrix created: (20, 125)
2025-07-18 05:09:44,132 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:09:44,136 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:09:44,139 - model_trainer - INFO - Data prepared:
2025-07-18 05:09:44,139 - model_trainer - INFO -   Total samples: 20
2025-07-18 05:09:44,139 - model_trainer - INFO -   Features: 116
2025-07-18 05:09:44,139 - model_trainer - INFO -   Train samples: 14
2025-07-18 05:09:44,139 - model_trainer - INFO -   Validation samples: 2
2025-07-18 05:09:44,140 - model_trainer - INFO -   Test samples: 4
2025-07-18 05:09:44,140 - model_trainer - INFO - Starting model training...
2025-07-18 05:09:44,140 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:09:44,140 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:09:44,140 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:09:44,261 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:09:44,262 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:09:44,262 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:09:44,295 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:09:44,296 - model_trainer - INFO - Model training completed
2025-07-18 05:09:44,296 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:09:44,297 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 05:10:24,275 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:10:24,275 - train_models - INFO - Configuration: {'data_source': 'file', 'data_file': None, 'models': None, 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:10:24,275 - train_models - INFO - Creating sample training data for demonstration
2025-07-18 05:10:24,275 - data_creator - INFO - Creating sample training data...
2025-07-18 05:10:24,288 - data_creator - INFO - Created 500 sample matches
2025-07-18 05:10:24,288 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:10:24,307 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 05:10:24,307 - train_models - INFO - Extracting features...
2025-07-18 05:10:24,330 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,331 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,332 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,332 - database - INFO - Created database engine for sqlite
2025-07-18 05:10:24,338 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:10:24,338 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 05:10:24,357 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 05:10:24,373 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 05:10:24,374 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 05:10:24,463 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 05:10:24,464 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 05:10:24,464 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:10:24,469 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:10:24,471 - model_trainer - INFO - Data prepared:
2025-07-18 05:10:24,471 - model_trainer - INFO -   Total samples: 500
2025-07-18 05:10:24,472 - model_trainer - INFO -   Features: 116
2025-07-18 05:10:24,472 - model_trainer - INFO -   Train samples: 350
2025-07-18 05:10:24,472 - model_trainer - INFO -   Validation samples: 50
2025-07-18 05:10:24,472 - model_trainer - INFO -   Test samples: 100
2025-07-18 05:10:24,472 - model_trainer - INFO - Starting model training...
2025-07-18 05:10:24,472 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:10:24,473 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:10:24,473 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:10:24,505 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:10:24,505 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:10:24,505 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:10:24,511 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:10:24,511 - model_trainer - INFO - Model training completed
2025-07-18 05:10:24,512 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:10:24,514 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 05:11:09,746 - train_models - INFO - Starting football prediction model training...
2025-07-18 05:11:09,750 - train_models - INFO - Configuration: {'data_source': 'file', 'data_file': None, 'models': None, 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 10, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 05:11:09,750 - train_models - INFO - Creating sample training data for demonstration
2025-07-18 05:11:09,750 - data_creator - INFO - Creating sample training data...
2025-07-18 05:11:09,796 - data_creator - INFO - Created 500 sample matches
2025-07-18 05:11:09,798 - train_models - INFO - Validating and cleaning data...
2025-07-18 05:11:09,901 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 05:11:09,902 - train_models - INFO - Extracting features...
2025-07-18 05:11:09,936 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,936 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,937 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,937 - database - INFO - Created database engine for sqlite
2025-07-18 05:11:09,942 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 05:11:09,942 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 05:11:09,972 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 05:11:09,990 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 05:11:09,991 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 05:11:10,088 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 05:11:10,089 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 05:11:10,089 - model_trainer - INFO - Preparing data for training...
2025-07-18 05:11:10,095 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 05:11:10,100 - model_trainer - INFO - Data prepared:
2025-07-18 05:11:10,100 - model_trainer - INFO -   Total samples: 500
2025-07-18 05:11:10,101 - model_trainer - INFO -   Features: 116
2025-07-18 05:11:10,101 - model_trainer - INFO -   Train samples: 350
2025-07-18 05:11:10,101 - model_trainer - INFO -   Validation samples: 50
2025-07-18 05:11:10,101 - model_trainer - INFO -   Test samples: 100
2025-07-18 05:11:10,101 - model_trainer - INFO - Starting model training...
2025-07-18 05:11:10,102 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm']
2025-07-18 05:11:10,102 - model_trainer - INFO - Training xgboost model...
2025-07-18 05:11:10,102 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 05:11:10,527 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 05:11:10,527 - model_trainer - INFO - Training lightgbm model...
2025-07-18 05:11:10,527 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 05:11:10,538 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 05:11:10,539 - model_trainer - INFO - Model training completed
2025-07-18 05:11:10,539 - train_models - INFO - Training completed! Generating report...
2025-07-18 05:11:10,540 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:03:14,316 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:03:14,316 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:03:14,371 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:14,466 - data_loader - WARNING - No finished matches found in database
2025-07-18 08:03:14,538 - train_models - INFO - No data in database, creating sample data for demonstration
2025-07-18 08:03:14,538 - data_creator - INFO - Creating sample training data...
2025-07-18 08:03:15,048 - data_creator - INFO - Created 500 sample matches
2025-07-18 08:03:15,053 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:03:15,196 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 08:03:15,196 - train_models - INFO - Extracting features...
2025-07-18 08:03:15,197 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,197 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,198 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,198 - database - INFO - Created database engine for sqlite
2025-07-18 08:03:15,207 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:03:15,207 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 08:03:15,236 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 08:03:15,257 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 08:03:15,261 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 08:03:15,388 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 08:03:15,388 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 08:03:15,388 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:03:15,399 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:03:15,411 - model_trainer - INFO - Data prepared:
2025-07-18 08:03:15,412 - model_trainer - INFO -   Total samples: 500
2025-07-18 08:03:15,412 - model_trainer - INFO -   Features: 116
2025-07-18 08:03:15,412 - model_trainer - INFO -   Train samples: 350
2025-07-18 08:03:15,412 - model_trainer - INFO -   Validation samples: 50
2025-07-18 08:03:15,413 - model_trainer - INFO -   Test samples: 100
2025-07-18 08:03:15,413 - model_trainer - INFO - Starting model training...
2025-07-18 08:03:15,413 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:03:15,413 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:03:15,413 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:03:15,499 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:03:15,499 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:03:15,499 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:03:15,538 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:03:15,538 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:03:15,538 - model_trainer - ERROR - Failed to train random_forest: 'random_forest'
2025-07-18 08:03:15,539 - model_trainer - INFO - Model training completed
2025-07-18 08:03:15,539 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:03:15,540 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:14:21,404 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:14:21,405 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:14:21,450 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,610 - data_loader - WARNING - No finished matches found in database
2025-07-18 08:14:21,623 - train_models - INFO - No data in database, creating sample data for demonstration
2025-07-18 08:14:21,623 - data_creator - INFO - Creating sample training data...
2025-07-18 08:14:21,650 - data_creator - INFO - Created 500 sample matches
2025-07-18 08:14:21,650 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:14:21,736 - data_cleaner - INFO - Cleaned matches data: 500 matches remaining
2025-07-18 08:14:21,736 - train_models - INFO - Extracting features...
2025-07-18 08:14:21,737 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,738 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,739 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,740 - database - INFO - Created database engine for sqlite
2025-07-18 08:14:21,747 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:14:21,747 - feature_engineering - INFO - Starting feature extraction for 500 matches
2025-07-18 08:14:21,768 - feature_engineering - INFO - Completed 100/500 matches
2025-07-18 08:14:21,782 - feature_engineering - INFO - Completed 200/500 matches
2025-07-18 08:14:21,783 - feature_engineering - INFO - Completed 300/500 matches
2025-07-18 08:14:21,783 - feature_engineering - INFO - Completed 400/500 matches
2025-07-18 08:14:21,784 - feature_engineering - INFO - Completed 500/500 matches
2025-07-18 08:14:21,869 - feature_engineering - INFO - Feature matrix created with shape: (500, 125)
2025-07-18 08:14:21,869 - train_models - INFO - Feature matrix created: (500, 125)
2025-07-18 08:14:21,869 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:14:21,873 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:14:21,878 - model_trainer - INFO - Data prepared:
2025-07-18 08:14:21,878 - model_trainer - INFO -   Total samples: 500
2025-07-18 08:14:21,878 - model_trainer - INFO -   Features: 116
2025-07-18 08:14:21,878 - model_trainer - INFO -   Train samples: 350
2025-07-18 08:14:21,878 - model_trainer - INFO -   Validation samples: 50
2025-07-18 08:14:21,879 - model_trainer - INFO -   Test samples: 100
2025-07-18 08:14:21,879 - model_trainer - INFO - Starting model training...
2025-07-18 08:14:21,879 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:14:21,879 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:14:21,879 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:14:21,924 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:14:21,925 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:14:21,925 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:14:21,929 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:14:21,929 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:14:21,930 - model_trainer - ERROR - Failed to train random_forest: 'random_forest'
2025-07-18 08:14:21,930 - model_trainer - INFO - Model training completed
2025-07-18 08:14:21,931 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:14:21,932 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:17:46,498 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:17:46,498 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'xgboost,lightgbm,random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:17:46,555 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,683 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:17:46,688 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:17:46,870 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:17:46,871 - train_models - INFO - Extracting features...
2025-07-18 08:17:46,872 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,873 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,874 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,875 - database - INFO - Created database engine for sqlite
2025-07-18 08:17:46,910 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:17:46,910 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:17:46,943 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:17:46,944 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:17:46,945 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:17:47,013 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:17:47,014 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:17:47,656 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:17:47,660 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:17:47,666 - model_trainer - INFO - Data prepared:
2025-07-18 08:17:47,666 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:17:47,666 - model_trainer - INFO -   Features: 116
2025-07-18 08:17:47,666 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:17:47,667 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:17:47,667 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:17:47,667 - model_trainer - INFO - Starting model training...
2025-07-18 08:17:47,667 - model_trainer - INFO - Training models: ['xgboost', 'lightgbm', 'random_forest']
2025-07-18 08:17:47,667 - model_trainer - INFO - Training xgboost model...
2025-07-18 08:17:47,667 - model.xgboost_football - INFO - Training xgboost_football model...
2025-07-18 08:17:47,721 - model_trainer - ERROR - Failed to train xgboost: 'list' object cannot be interpreted as an integer
2025-07-18 08:17:47,721 - model_trainer - INFO - Training lightgbm model...
2025-07-18 08:17:47,721 - model.lightgbm_football - INFO - Training lightgbm_football model...
2025-07-18 08:17:47,729 - model_trainer - ERROR - Failed to train lightgbm: '<=' not supported between instances of 'list' and 'int'
2025-07-18 08:17:47,729 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:17:47,729 - model_trainer - ERROR - Failed to train random_forest: Can't instantiate abstract class RandomForestFootballModel without an implementation for abstract methods '_create_model', '_fit_model', '_predict_proba'
2025-07-18 08:17:47,729 - model_trainer - INFO - Model training completed
2025-07-18 08:17:47,730 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:17:47,731 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:20:45,145 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:20:45,145 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:20:45,219 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,325 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:20:45,329 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:20:45,398 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:20:45,398 - train_models - INFO - Extracting features...
2025-07-18 08:20:45,399 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,399 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,400 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,400 - database - INFO - Created database engine for sqlite
2025-07-18 08:20:45,405 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:20:45,405 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:20:45,427 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:20:45,438 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:20:45,439 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:20:45,511 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:20:45,512 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:20:45,711 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:20:45,715 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:20:45,717 - model_trainer - INFO - Data prepared:
2025-07-18 08:20:45,718 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:20:45,718 - model_trainer - INFO -   Features: 116
2025-07-18 08:20:45,718 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:20:45,718 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:20:45,718 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:20:45,718 - model_trainer - INFO - Starting model training...
2025-07-18 08:20:45,718 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:20:45,718 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:20:45,718 - model_trainer - ERROR - Failed to train random_forest: RandomForestFootballModel.__init__() got an unexpected keyword argument 'model_name'
2025-07-18 08:20:45,718 - model_trainer - INFO - Model training completed
2025-07-18 08:20:45,718 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:20:45,719 - train_models - INFO - Model training pipeline completed successfully!
2025-07-18 08:22:10,437 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:22:10,438 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:22:10,513 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,674 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:22:10,680 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:22:10,726 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:22:10,728 - train_models - INFO - Extracting features...
2025-07-18 08:22:10,730 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,731 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,734 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,734 - database - INFO - Created database engine for sqlite
2025-07-18 08:22:10,741 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:22:10,743 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:22:10,763 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:22:10,774 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:22:10,775 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:22:10,863 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:22:10,863 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:22:11,284 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:22:11,294 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:22:11,304 - model_trainer - INFO - Data prepared:
2025-07-18 08:22:11,304 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:22:11,304 - model_trainer - INFO -   Features: 116
2025-07-18 08:22:11,304 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:22:11,304 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:22:11,304 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:22:11,305 - model_trainer - INFO - Starting model training...
2025-07-18 08:22:11,305 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:22:11,305 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:22:11,305 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:22:11,629 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,705 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,706 - model.random_forest_football - INFO - Training completed in 0.22 seconds
2025-07-18 08:22:11,768 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:22:11,768 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:22:11,768 - model_trainer - INFO - Model training completed
2025-07-18 08:22:11,768 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:22:11,788 - train_models - ERROR - Training failed: 'dict' object has no attribute 'head'
2025-07-18 08:23:33,431 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:23:33,433 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:23:33,481 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,612 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:23:33,621 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:23:33,662 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:23:33,662 - train_models - INFO - Extracting features...
2025-07-18 08:23:33,663 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,663 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,664 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,664 - database - INFO - Created database engine for sqlite
2025-07-18 08:23:33,670 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:23:33,670 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:23:33,690 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:23:33,694 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:23:33,694 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:23:33,766 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:23:33,766 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:23:34,011 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:23:34,015 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:23:34,019 - model_trainer - INFO - Data prepared:
2025-07-18 08:23:34,019 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:23:34,019 - model_trainer - INFO -   Features: 116
2025-07-18 08:23:34,019 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:23:34,019 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:23:34,020 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:23:34,020 - model_trainer - INFO - Starting model training...
2025-07-18 08:23:34,020 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:23:34,020 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:23:34,020 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:23:34,386 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,469 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,469 - model.random_forest_football - INFO - Training completed in 0.21 seconds
2025-07-18 08:23:34,528 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:23:34,528 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:23:34,528 - model_trainer - INFO - Model training completed
2025-07-18 08:23:34,528 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:23:34,543 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 08:24:47,268 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:24:47,271 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': False, 'output_dir': 'data/models'}
2025-07-18 08:24:47,346 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,543 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:24:47,549 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:24:47,626 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:24:47,626 - train_models - INFO - Extracting features...
2025-07-18 08:24:47,626 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,627 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,628 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,628 - database - INFO - Created database engine for sqlite
2025-07-18 08:24:47,632 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:24:47,632 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:24:47,656 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:24:47,657 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:24:47,657 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:24:47,740 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:24:47,741 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:24:48,244 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:24:48,252 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:24:48,263 - model_trainer - INFO - Data prepared:
2025-07-18 08:24:48,264 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:24:48,264 - model_trainer - INFO -   Features: 116
2025-07-18 08:24:48,264 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:24:48,264 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:24:48,264 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:24:48,265 - model_trainer - INFO - Starting model training...
2025-07-18 08:24:48,265 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:24:48,265 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:24:48,265 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:24:48,704 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,774 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,774 - model.random_forest_football - INFO - Training completed in 0.28 seconds
2025-07-18 08:24:48,851 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:24:48,851 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:24:48,851 - model_trainer - INFO - Model training completed
2025-07-18 08:24:48,851 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:24:48,880 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-18 08:41:33,897 - train_models - INFO - Starting football prediction model training...
2025-07-18 08:41:33,918 - train_models - INFO - Configuration: {'data_source': 'database', 'data_file': None, 'models': 'random_forest', 'target': 'result', 'tune_hyperparameters': False, 'test_size': 0.2, 'validation_size': 0.1, 'min_samples': 50, 'save_models': True, 'output_dir': 'data/models'}
2025-07-18 08:41:34,008 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,211 - data_loader - INFO - Loaded 380 matches from database
2025-07-18 08:41:34,216 - train_models - INFO - Validating and cleaning data...
2025-07-18 08:41:34,296 - data_cleaner - INFO - Cleaned matches data: 380 matches remaining
2025-07-18 08:41:34,296 - train_models - INFO - Extracting features...
2025-07-18 08:41:34,297 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,298 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,298 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,299 - database - INFO - Created database engine for sqlite
2025-07-18 08:41:34,328 - feature_engineering - INFO - Creating feature matrix...
2025-07-18 08:41:34,328 - feature_engineering - INFO - Starting feature extraction for 380 matches
2025-07-18 08:41:34,372 - feature_engineering - INFO - Completed 100/380 matches
2025-07-18 08:41:34,374 - feature_engineering - INFO - Completed 200/380 matches
2025-07-18 08:41:34,375 - feature_engineering - INFO - Completed 300/380 matches
2025-07-18 08:41:34,496 - feature_engineering - INFO - Feature matrix created with shape: (380, 125)
2025-07-18 08:41:34,496 - train_models - INFO - Feature matrix created: (380, 125)
2025-07-18 08:41:35,325 - model_trainer - INFO - Preparing data for training...
2025-07-18 08:41:35,332 - model_trainer - INFO - Encoded target classes: {'A': 0, 'D': 1, 'H': 2}
2025-07-18 08:41:35,349 - model_trainer - INFO - Data prepared:
2025-07-18 08:41:35,350 - model_trainer - INFO -   Total samples: 380
2025-07-18 08:41:35,350 - model_trainer - INFO -   Features: 116
2025-07-18 08:41:35,350 - model_trainer - INFO -   Train samples: 266
2025-07-18 08:41:35,351 - model_trainer - INFO -   Validation samples: 38
2025-07-18 08:41:35,351 - model_trainer - INFO -   Test samples: 76
2025-07-18 08:41:35,351 - model_trainer - INFO - Starting model training...
2025-07-18 08:41:35,351 - model_trainer - INFO - Training models: ['random_forest']
2025-07-18 08:41:35,351 - model_trainer - INFO - Training random_forest model...
2025-07-18 08:41:35,351 - model.random_forest_football - INFO - Training random_forest_football model...
2025-07-18 08:41:35,747 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,814 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,814 - model.random_forest_football - INFO - Training completed in 0.27 seconds
2025-07-18 08:41:35,883 - model.random_forest_football - INFO - Test accuracy: 1.0000
2025-07-18 08:41:35,884 - model_trainer - INFO - random_forest training completed. Test accuracy: 1.0000
2025-07-18 08:41:35,884 - model_trainer - INFO - Model training completed
2025-07-18 08:41:35,884 - train_models - INFO - Training completed! Generating report...
2025-07-18 08:41:35,908 - train_models - INFO - Saving models...
2025-07-18 08:41:36,003 - model.random_forest_football - INFO - Model saved to data/models/random_forest_model_20250718_084135.joblib
2025-07-18 08:41:36,004 - model_trainer - INFO - Saved random_forest to data/models/random_forest_model_20250718_084135.joblib
2025-07-18 08:41:36,009 - model_trainer - INFO - Saved training results to data/models/training_results_20250718_084135.json
2025-07-18 08:41:36,012 - train_models - ERROR - Training failed: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
