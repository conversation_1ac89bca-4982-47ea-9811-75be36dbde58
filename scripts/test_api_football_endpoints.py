#!/usr/bin/env python3
"""Test API-Football endpoints with real data collection."""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.collectors.api_football_client import APIFootballClient


def test_api_endpoints():
    """Test all major API-Football endpoints."""
    
    print("🏈 TESTING API-FOOTBALL ENDPOINTS")
    print("="*60)
    
    client = APIFootballClient()
    
    # Test 1: API Status
    print("1. 📊 API STATUS")
    print("-" * 30)
    try:
        status = client.get_api_status()
        print(f"✅ Account: {status['account']['firstname']} {status['account']['lastname']}")
        print(f"✅ Plan: {status['subscription']['plan']}")
        print(f"✅ Active: {status['subscription']['active']}")
        print(f"✅ Requests today: {status['requests']['current']}/{status['requests']['limit_day']}")
        remaining = status['requests']['limit_day'] - status['requests']['current']
        print(f"✅ Remaining: {remaining}")
    except Exception as e:
        print(f"❌ API Status error: {e}")
    
    # Test 2: Countries
    print(f"\n2. 🌍 COUNTRIES")
    print("-" * 30)
    try:
        response = client._make_request('/countries')
        countries = response.get('response', [])
        print(f"✅ Found {len(countries)} countries")
        
        # Show some major countries
        major_countries = ['England', 'Spain', 'Germany', 'Italy', 'France']
        for country in countries:
            if country.get('name') in major_countries:
                print(f"   - {country['name']} ({country.get('code', 'N/A')})")
    except Exception as e:
        print(f"❌ Countries error: {e}")
    
    # Test 3: Leagues
    print(f"\n3. 🏆 LEAGUES")
    print("-" * 30)
    try:
        leagues = client.get_leagues()
        print(f"✅ Found {len(leagues)} leagues")
        
        # Find major leagues
        major_league_ids = [39, 140, 78, 135, 61]  # PL, La Liga, Bundesliga, Serie A, Ligue 1
        major_leagues = []
        
        for league_data in leagues:
            league = league_data.get('league', {})
            if league.get('id') in major_league_ids:
                major_leagues.append(league_data)
        
        print(f"✅ Major leagues found: {len(major_leagues)}")
        for league_data in major_leagues:
            league = league_data.get('league', {})
            country = league_data.get('country', {})
            seasons = league_data.get('seasons', [])
            current_season = None
            for season in seasons:
                if season.get('current'):
                    current_season = season.get('year')
                    break
            
            print(f"   - {league.get('name')} ({country.get('name')}) - Current: {current_season}")
    except Exception as e:
        print(f"❌ Leagues error: {e}")
    
    # Test 4: Premier League Teams (2025 season)
    print(f"\n4. ⚽ PREMIER LEAGUE TEAMS (2025)")
    print("-" * 30)
    try:
        teams = client.get_teams(39, 2025)  # Premier League 2025
        print(f"✅ Found {len(teams)} Premier League teams")
        
        if teams:
            print("📋 Teams:")
            for i, team_data in enumerate(teams[:10], 1):
                team = team_data.get('team', {})
                venue = team_data.get('venue', {})
                print(f"   {i:2d}. {team.get('name')} - {venue.get('name', 'Unknown Stadium')}")
            
            if len(teams) > 10:
                print(f"   ... and {len(teams) - 10} more teams")
        else:
            print("⚠️  No teams found - trying 2024 season...")
            teams_2024 = client.get_teams(39, 2024)
            print(f"✅ Found {len(teams_2024)} teams in 2024 season")
            
    except Exception as e:
        print(f"❌ Teams error: {e}")
    
    # Test 5: Recent Fixtures
    print(f"\n5. 🏟️  RECENT FIXTURES")
    print("-" * 30)
    try:
        # Get fixtures from last 30 days
        date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        date_to = datetime.now().strftime('%Y-%m-%d')
        
        fixtures = client.get_fixtures(league_id=39, season=2025, date_from=date_from, date_to=date_to)
        print(f"✅ Found {len(fixtures)} recent Premier League fixtures")
        
        if fixtures:
            print("📋 Recent matches:")
            for i, fixture in enumerate(fixtures[:5], 1):
                fixture_info = fixture.get('fixture', {})
                teams = fixture.get('teams', {})
                goals = fixture.get('goals', {})
                
                home_team = teams.get('home', {}).get('name', 'Home Team')
                away_team = teams.get('away', {}).get('name', 'Away Team')
                match_date = fixture_info.get('date', 'Unknown Date')[:10]  # Just date part
                status = fixture_info.get('status', {}).get('short', 'Unknown')
                
                print(f"   {i}. {match_date}: {home_team} vs {away_team} ({status})")
                
                if goals.get('home') is not None:
                    print(f"      Score: {goals['home']}-{goals['away']}")
        else:
            print("⚠️  No recent fixtures - trying different date range...")
            # Try upcoming fixtures
            date_from = datetime.now().strftime('%Y-%m-%d')
            date_to = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
            
            upcoming = client.get_fixtures(league_id=39, season=2025, date_from=date_from, date_to=date_to)
            print(f"✅ Found {len(upcoming)} upcoming fixtures")
            
    except Exception as e:
        print(f"❌ Fixtures error: {e}")
    
    # Test 6: Injuries
    print(f"\n6. 🏥 INJURIES")
    print("-" * 30)
    try:
        injuries = client.get_injuries(league_id=39, season=2025)
        print(f"✅ Found {len(injuries)} injury records")
        
        if injuries:
            print("📋 Current injuries:")
            for i, injury in enumerate(injuries[:5], 1):
                player = injury.get('player', {})
                team = injury.get('team', {})
                fixture = injury.get('fixture', {})
                
                player_name = player.get('name', 'Unknown Player')
                team_name = team.get('name', 'Unknown Team')
                injury_type = injury.get('player', {}).get('reason', 'Unknown')
                
                print(f"   {i}. {player_name} ({team_name}) - {injury_type}")
                
    except Exception as e:
        print(f"❌ Injuries error: {e}")
    
    # Test 7: Standings
    print(f"\n7. 📊 STANDINGS")
    print("-" * 30)
    try:
        standings = client.get_standings(39, 2025)
        print(f"✅ Found {len(standings)} standings entries")
        
        if standings and len(standings) > 0:
            league_standings = standings[0].get('league', {}).get('standings', [[]])
            if league_standings and len(league_standings[0]) > 0:
                print("📋 Premier League Table (Top 5):")
                for i, team_standing in enumerate(league_standings[0][:5], 1):
                    team = team_standing.get('team', {})
                    stats = team_standing
                    
                    team_name = team.get('name', 'Unknown Team')
                    points = stats.get('points', 0)
                    played = stats.get('played', 0)
                    wins = stats.get('win', 0)
                    draws = stats.get('draw', 0)
                    losses = stats.get('lose', 0)
                    
                    print(f"   {i:2d}. {team_name} - {points} pts ({played} games: {wins}W {draws}D {losses}L)")
                    
    except Exception as e:
        print(f"❌ Standings error: {e}")
    
    print(f"\n🎯 API TESTING COMPLETE!")
    print("="*60)


if __name__ == "__main__":
    test_api_endpoints()
