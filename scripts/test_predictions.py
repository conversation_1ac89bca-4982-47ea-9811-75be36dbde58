#!/usr/bin/env python3
"""Comprehensive prediction system testing script."""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def setup_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_system_imports():
    """Test 1: System imports and basic functionality."""
    print("\n" + "="*80)
    print("🧪 TEST 1: SYSTEM IMPORTS AND COMPONENTS")
    print("="*80)
    
    results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    # Test core imports
    try:
        from src.prediction.pipeline import FootballPredictionPipeline
        print("✅ FootballPredictionPipeline import successful")
        results['passed'] += 1
    except Exception as e:
        print(f"❌ FootballPredictionPipeline import failed: {e}")
        results['failed'] += 1
    
    try:
        from src.prediction.service import PredictionService
        print("✅ PredictionService import successful")
        results['passed'] += 1
    except Exception as e:
        print(f"❌ PredictionService import failed: {e}")
        results['failed'] += 1
    
    try:
        from src.prediction.detailed_exporter import DetailedPredictionExporter
        print("✅ DetailedPredictionExporter import successful")
        results['passed'] += 1
    except Exception as e:
        print(f"❌ DetailedPredictionExporter import failed: {e}")
        results['failed'] += 1
    
    try:
        from src.api.main import app
        print("✅ FastAPI app import successful")
        results['passed'] += 1
    except Exception as e:
        print(f"❌ FastAPI app import failed: {e}")
        results['failed'] += 1
    
    # Test configuration
    try:
        from src.utils.config import config
        leagues = config.get_leagues()
        print(f"✅ Configuration loaded - {len(leagues)} leagues configured")
        results['passed'] += 1
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        results['failed'] += 1
    
    # Test feature engineering
    try:
        from src.features import FeatureEngineeringPipeline
        print("✅ Feature engineering pipeline import successful")
        results['passed'] += 1
    except Exception as e:
        print(f"⚠️  Feature engineering import: {e}")
        results['warnings'] += 1
    
    print(f"\n📊 Test 1 Results: {results['passed']} passed, {results['failed']} failed, {results['warnings']} warnings")
    return results


def test_mock_prediction():
    """Test 2: Mock prediction without requiring trained models."""
    print("\n" + "="*80)
    print("🧪 TEST 2: MOCK PREDICTION SYSTEM")
    print("="*80)
    
    results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    try:
        # Create mock prediction data
        mock_match_data = {
            'home_team_id': 33,  # Manchester City
            'away_team_id': 34,  # Liverpool
            'match_date': datetime.now() + timedelta(days=1),
            'league_id': 39  # Premier League
        }
        
        # Mock context data
        mock_context_data = {
            'home_team_matches': [
                {'home_team_id': 33, 'away_team_id': 35, 'home_score': 3, 'away_score': 1, 'match_date': datetime.now() - timedelta(days=7)},
                {'home_team_id': 36, 'away_team_id': 33, 'home_score': 0, 'away_score': 2, 'match_date': datetime.now() - timedelta(days=14)},
                {'home_team_id': 33, 'away_team_id': 37, 'home_score': 2, 'away_score': 0, 'match_date': datetime.now() - timedelta(days=21)},
            ],
            'away_team_matches': [
                {'home_team_id': 34, 'away_team_id': 38, 'home_score': 2, 'away_score': 1, 'match_date': datetime.now() - timedelta(days=7)},
                {'home_team_id': 39, 'away_team_id': 34, 'home_score': 1, 'away_score': 3, 'match_date': datetime.now() - timedelta(days=14)},
                {'home_team_id': 34, 'away_team_id': 40, 'home_score': 1, 'away_score': 1, 'match_date': datetime.now() - timedelta(days=21)},
            ],
            'h2h_matches': [
                {'home_team_id': 33, 'away_team_id': 34, 'home_score': 1, 'away_score': 2, 'match_date': datetime.now() - timedelta(days=90)},
                {'home_team_id': 34, 'away_team_id': 33, 'home_score': 0, 'away_score': 4, 'match_date': datetime.now() - timedelta(days=180)},
            ],
            'home_team_injuries': [
                {'player_id': 1001, 'injury_type': 'Muscle', 'severity': 'Minor'},
                {'player_id': 1002, 'injury_type': 'Knee', 'severity': 'Moderate'},
            ],
            'away_team_injuries': [
                {'player_id': 2001, 'injury_type': 'Ankle', 'severity': 'Minor'},
            ],
            'home_team_players': [
                {'external_id': 1001, 'name': 'Player 1', 'position': 'Forward', 'rating': 8.5},
                {'external_id': 1002, 'name': 'Player 2', 'position': 'Midfielder', 'rating': 7.8},
                {'external_id': 1003, 'name': 'Player 3', 'position': 'Defender', 'rating': 7.2},
            ],
            'away_team_players': [
                {'external_id': 2001, 'name': 'Player A', 'position': 'Forward', 'rating': 8.2},
                {'external_id': 2002, 'name': 'Player B', 'position': 'Midfielder', 'rating': 7.9},
                {'external_id': 2003, 'name': 'Player C', 'position': 'Defender', 'rating': 7.5},
            ]
        }
        
        print("✅ Mock data created successfully")
        print(f"   Match: Team {mock_match_data['home_team_id']} vs Team {mock_match_data['away_team_id']}")
        print(f"   Date: {mock_match_data['match_date'].strftime('%Y-%m-%d %H:%M')}")
        print(f"   Context: {len(mock_context_data['home_team_matches'])} home matches, {len(mock_context_data['away_team_matches'])} away matches")
        print(f"   H2H: {len(mock_context_data['h2h_matches'])} historical matches")
        print(f"   Injuries: {len(mock_context_data['home_team_injuries'])} home, {len(mock_context_data['away_team_injuries'])} away")
        
        results['passed'] += 1
        
    except Exception as e:
        print(f"❌ Mock data creation failed: {e}")
        results['failed'] += 1
    
    # Test feature extraction with mock data
    try:
        from src.features.engineering.api_football_extractor import APIFootballFeatureExtractor
        
        extractor = APIFootballFeatureExtractor()
        features = extractor.extract_features(mock_match_data, mock_context_data)
        
        print(f"✅ Feature extraction successful - {len(features)} features extracted")
        print("   Key features:")
        for key, value in list(features.items())[:5]:
            print(f"     {key}: {value}")
        
        results['passed'] += 1
        
    except Exception as e:
        print(f"⚠️  Feature extraction: {e}")
        results['warnings'] += 1
    
    print(f"\n📊 Test 2 Results: {results['passed']} passed, {results['failed']} failed, {results['warnings']} warnings")
    return results


def test_mock_ml_prediction():
    """Test 3: Mock ML prediction using simple logic."""
    print("\n" + "="*80)
    print("🧪 TEST 3: MOCK ML PREDICTION")
    print("="*80)
    
    results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    try:
        # Create a simple mock prediction based on team form
        def create_mock_prediction(home_team_id, away_team_id, home_form=0.6, away_form=0.5):
            """Create a mock prediction based on simple logic."""
            
            # Simple logic: better form = higher win probability
            if home_form > away_form:
                home_prob = 0.5 + (home_form - away_form) * 0.3
                away_prob = 0.3 - (home_form - away_form) * 0.1
                draw_prob = 1.0 - home_prob - away_prob
                predicted_outcome = 'H'
                predicted_text = 'Home Win'
            elif away_form > home_form:
                away_prob = 0.4 + (away_form - home_form) * 0.3
                home_prob = 0.3 - (away_form - home_form) * 0.1
                draw_prob = 1.0 - home_prob - away_prob
                predicted_outcome = 'A'
                predicted_text = 'Away Win'
            else:
                home_prob = 0.35
                away_prob = 0.35
                draw_prob = 0.30
                predicted_outcome = 'D'
                predicted_text = 'Draw'
            
            confidence = max(home_prob, away_prob, draw_prob)
            confidence_level = "High" if confidence > 0.6 else "Medium" if confidence > 0.4 else "Low"
            
            return {
                'predicted_outcome': predicted_outcome,
                'predicted_outcome_text': predicted_text,
                'probabilities': {
                    'home_win': round(home_prob, 3),
                    'draw': round(draw_prob, 3),
                    'away_win': round(away_prob, 3)
                },
                'confidence': round(confidence, 3),
                'confidence_level': confidence_level,
                'prediction_timestamp': datetime.now().isoformat(),
                'model_info': {
                    'model_name': 'mock_predictor',
                    'model_type': 'rule_based',
                    'features_used': 2
                },
                'prediction_factors': {
                    'form_advantage': {
                        'home_form': home_form,
                        'away_form': away_form,
                        'advantage': 'Home' if home_form > away_form else 'Away' if away_form > home_form else 'Equal'
                    }
                }
            }
        
        # Test different scenarios
        test_scenarios = [
            {'name': 'Strong Home Team', 'home_form': 0.8, 'away_form': 0.4},
            {'name': 'Strong Away Team', 'home_form': 0.3, 'away_form': 0.7},
            {'name': 'Evenly Matched', 'home_form': 0.5, 'away_form': 0.5},
            {'name': 'Both Strong', 'home_form': 0.8, 'away_form': 0.8},
            {'name': 'Both Weak', 'home_form': 0.2, 'away_form': 0.2},
        ]
        
        print("🔮 Testing prediction scenarios:")
        print("-" * 60)
        
        for scenario in test_scenarios:
            prediction = create_mock_prediction(
                home_team_id=33,
                away_team_id=34,
                home_form=scenario['home_form'],
                away_form=scenario['away_form']
            )
            
            print(f"\n📊 {scenario['name']}:")
            print(f"   Form: Home {scenario['home_form']:.1f}, Away {scenario['away_form']:.1f}")
            print(f"   Prediction: {prediction['predicted_outcome_text']} ({prediction['confidence']:.3f})")
            print(f"   Probabilities: H:{prediction['probabilities']['home_win']:.3f} "
                  f"D:{prediction['probabilities']['draw']:.3f} A:{prediction['probabilities']['away_win']:.3f}")
            print(f"   Confidence: {prediction['confidence_level']}")
            
            results['passed'] += 1
        
        print(f"\n✅ All {len(test_scenarios)} prediction scenarios completed successfully")
        
    except Exception as e:
        print(f"❌ Mock prediction failed: {e}")
        results['failed'] += 1
    
    print(f"\n📊 Test 3 Results: {results['passed']} passed, {results['failed']} failed, {results['warnings']} warnings")
    return results


def test_csv_export():
    """Test 4: CSV export functionality."""
    print("\n" + "="*80)
    print("🧪 TEST 4: CSV EXPORT FUNCTIONALITY")
    print("="*80)
    
    results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    try:
        # Test sample CSV creation
        print("📊 Testing sample CSV creation...")
        
        # Create sample data
        sample_matches = []
        teams = [
            ("Manchester City", "Liverpool"),
            ("Arsenal", "Chelsea"),
            ("Barcelona", "Real Madrid"),
        ]
        
        for i, (home_team, away_team) in enumerate(teams):
            match_data = {
                'match_id': 1000 + i,
                'external_id': 500000 + i,
                'match_date': (datetime.now() - timedelta(days=i*7)).isoformat(),
                'league_name': 'Premier League',
                'home_team': home_team,
                'away_team': away_team,
                'venue': f'Stadium {i+1}',
                'referee': f'Referee {i+1}',
                'match_status': 'FT',
                'actual_home_score': np.random.randint(0, 4),
                'actual_away_score': np.random.randint(0, 3),
                'predicted_result_text': np.random.choice(['Home Win', 'Draw', 'Away Win']),
                'prediction_confidence': round(np.random.uniform(0.4, 0.9), 3),
                'total_cards': np.random.randint(2, 8),
                'home_possession': round(np.random.uniform(35, 75), 1),
                'away_possession': round(100 - np.random.uniform(35, 75), 1),
            }
            sample_matches.append(match_data)
        
        # Create DataFrame
        df = pd.DataFrame(sample_matches)
        
        # Create output directory
        output_dir = Path("data/exports")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Export to CSV
        test_csv_file = output_dir / "test_predictions.csv"
        df.to_csv(test_csv_file, index=False)
        
        print(f"✅ Test CSV created: {test_csv_file}")
        print(f"   Rows: {len(df)}")
        print(f"   Columns: {len(df.columns)}")
        
        # Verify CSV content
        df_read = pd.read_csv(test_csv_file)
        if len(df_read) == len(df):
            print("✅ CSV read/write verification successful")
            results['passed'] += 1
        else:
            print("❌ CSV verification failed")
            results['failed'] += 1
        
        # Show sample data
        print("\n📋 Sample CSV content:")
        print("-" * 60)
        display_cols = ['home_team', 'away_team', 'actual_home_score', 'actual_away_score', 'predicted_result_text']
        print(df[display_cols].to_string(index=False))
        
        results['passed'] += 1
        
    except Exception as e:
        print(f"❌ CSV export test failed: {e}")
        results['failed'] += 1
    
    print(f"\n📊 Test 4 Results: {results['passed']} passed, {results['failed']} failed, {results['warnings']} warnings")
    return results


def test_api_structure():
    """Test 5: API structure and endpoints."""
    print("\n" + "="*80)
    print("🧪 TEST 5: API STRUCTURE")
    print("="*80)
    
    results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    try:
        from src.api.main import app
        
        # Get API routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append({
                    'path': route.path,
                    'methods': list(route.methods) if route.methods else ['GET']
                })
        
        print(f"✅ API app loaded with {len(routes)} routes")
        
        # Display key endpoints
        key_endpoints = ['/predict', '/predict/upcoming', '/predict/batch', '/export/detailed', '/status', '/health']
        
        print("\n📋 Key API Endpoints:")
        print("-" * 40)
        for endpoint in key_endpoints:
            found_routes = [r for r in routes if r['path'] == endpoint]
            if found_routes:
                methods = found_routes[0]['methods']
                print(f"✅ {endpoint} - {', '.join(methods)}")
                results['passed'] += 1
            else:
                print(f"⚠️  {endpoint} - Not found")
                results['warnings'] += 1
        
        # Test API metadata
        print(f"\n📊 API Information:")
        print(f"   Title: {app.title}")
        print(f"   Version: {app.version}")
        print(f"   Description: {app.description[:50]}...")
        
        results['passed'] += 1
        
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        results['failed'] += 1
    
    print(f"\n📊 Test 5 Results: {results['passed']} passed, {results['failed']} failed, {results['warnings']} warnings")
    return results


def main():
    """Run all prediction system tests."""
    setup_logging()
    
    print("🏈 COMPREHENSIVE FOOTBALL PREDICTION SYSTEM TESTING")
    print("="*80)
    print("🚀 Testing all system components and functionality...")
    print("="*80)
    
    # Run all tests
    all_results = {'passed': 0, 'failed': 0, 'warnings': 0}
    
    test_results = [
        test_system_imports(),
        test_mock_prediction(),
        test_mock_ml_prediction(),
        test_csv_export(),
        test_api_structure(),
    ]
    
    # Aggregate results
    for result in test_results:
        all_results['passed'] += result['passed']
        all_results['failed'] += result['failed']
        all_results['warnings'] += result['warnings']
    
    # Final summary
    print("\n" + "="*80)
    print("🏆 FINAL TEST RESULTS")
    print("="*80)
    print(f"✅ Tests Passed: {all_results['passed']}")
    print(f"❌ Tests Failed: {all_results['failed']}")
    print(f"⚠️  Warnings: {all_results['warnings']}")
    
    total_tests = all_results['passed'] + all_results['failed']
    if total_tests > 0:
        success_rate = (all_results['passed'] / total_tests) * 100
        print(f"📊 Success Rate: {success_rate:.1f}%")
    
    print("\n🎯 SYSTEM STATUS:")
    if all_results['failed'] == 0:
        print("🟢 SYSTEM READY - All core components working!")
    elif all_results['failed'] < 3:
        print("🟡 SYSTEM MOSTLY READY - Minor issues detected")
    else:
        print("🔴 SYSTEM NEEDS ATTENTION - Multiple issues detected")
    
    print("\n📋 NEXT STEPS:")
    print("1. 🗄️  Set up database for full functionality")
    print("2. 🤖 Train ML models for real predictions")
    print("3. 🔑 Configure API-Football key for live data")
    print("4. 🚀 Start API server: python scripts/start_api.py")
    print("5. 📊 Export predictions: python scripts/export_detailed_predictions.py")
    
    print("="*80)
    
    return 0 if all_results['failed'] == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
