#!/usr/bin/env python3
"""Train football prediction models using the complete ML pipeline."""

import sys
import os
import argparse
import logging
from pathlib import Path
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.features import FeatureEngineeringPipeline
from src.models import ModelTrainer
from src.data.processors import DataValidator, DataCleaner
from src.utils.database import DatabaseManager
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/model_training.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train football prediction models')
    
    parser.add_argument(
        '--data-source',
        choices=['database', 'file'],
        default='database',
        help='Source of training data'
    )
    
    parser.add_argument(
        '--data-file',
        type=str,
        help='Path to data file (if using file source)'
    )
    
    parser.add_argument(
        '--models',
        type=str,
        help='Comma-separated list of models to train (xgboost,lightgbm,neural_network)'
    )
    
    parser.add_argument(
        '--target',
        type=str,
        default='result',
        help='Target variable to predict'
    )
    
    parser.add_argument(
        '--tune-hyperparameters',
        action='store_true',
        help='Enable hyperparameter tuning'
    )
    
    parser.add_argument(
        '--test-size',
        type=float,
        default=0.2,
        help='Proportion of data for testing'
    )
    
    parser.add_argument(
        '--validation-size',
        type=float,
        default=0.1,
        help='Proportion of data for validation'
    )
    
    parser.add_argument(
        '--min-samples',
        type=int,
        default=100,
        help='Minimum number of samples required for training'
    )
    
    parser.add_argument(
        '--save-models',
        action='store_true',
        help='Save trained models to disk'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='data/models',
        help='Directory to save models and results'
    )
    
    return parser.parse_args()


def load_data_from_database() -> pd.DataFrame:
    """Load training data from database.
    
    Returns:
        DataFrame with match data
    """
    logger = logging.getLogger("data_loader")
    
    try:
        db_manager = DatabaseManager()
        
        # Get finished matches for training
        with db_manager.get_session() as session:
            from src.utils.database.models import Match
            
            matches = session.query(Match).filter(
                Match.status.in_(['FINISHED', 'FT']),  # Support both status formats
                Match.home_score.isnot(None),
                Match.away_score.isnot(None)
            ).all()
            
            if not matches:
                logger.warning("No finished matches found in database")
                return pd.DataFrame()
            
            # Convert to DataFrame
            matches_data = []
            for match in matches:
                match_dict = {
                    'id': match.id,
                    'external_id': match.external_id,
                    'home_team_id': match.home_team_id,
                    'away_team_id': match.away_team_id,
                    'match_date': match.match_date,
                    'league_id': match.league_id,
                    'season': match.season,
                    'status': match.status,
                    'home_score': match.home_score,
                    'away_score': match.away_score,
                    'venue': match.venue
                }
                matches_data.append(match_dict)
            
            matches_df = pd.DataFrame(matches_data)
            logger.info(f"Loaded {len(matches_df)} matches from database")
            return matches_df
            
    except Exception as e:
        logger.error(f"Failed to load data from database: {e}")
        return pd.DataFrame()


def create_sample_training_data() -> pd.DataFrame:
    """Create sample training data for demonstration.
    
    Returns:
        DataFrame with sample match data
    """
    logger = logging.getLogger("data_creator")
    logger.info("Creating sample training data...")
    
    import random
    from datetime import datetime, timedelta
    
    # Create sample matches
    matches_data = []
    base_date = datetime.now() - timedelta(days=365)
    
    for i in range(500):  # 500 sample matches
        match_date = base_date + timedelta(days=i % 300)
        home_team = random.randint(1, 20)
        away_team = random.randint(1, 20)
        while away_team == home_team:
            away_team = random.randint(1, 20)
        
        # Generate realistic scores with some logic
        home_advantage = 0.1
        home_strength = random.uniform(0.3, 0.9)
        away_strength = random.uniform(0.3, 0.9)
        
        # Simulate match outcome
        home_prob = (home_strength + home_advantage) / (home_strength + away_strength + home_advantage)
        
        if random.random() < home_prob:
            if random.random() < 0.7:  # Home win
                home_score = random.randint(1, 4)
                away_score = random.randint(0, home_score - 1)
                result = 'H'
            else:  # Draw
                score = random.randint(0, 3)
                home_score = away_score = score
                result = 'D'
        else:  # Away win
            away_score = random.randint(1, 4)
            home_score = random.randint(0, away_score - 1)
            result = 'A'
        
        matches_data.append({
            'id': i + 1,
            'external_id': 1000 + i,
            'home_team_id': home_team,
            'away_team_id': away_team,
            'match_date': match_date,
            'league_id': 1,
            'season': '2023-24',
            'status': 'FINISHED',
            'home_score': home_score,
            'away_score': away_score,
            'result': result,
            'venue': f'Stadium {home_team}'
        })
    
    df = pd.DataFrame(matches_data)
    logger.info(f"Created {len(df)} sample matches")
    return df


def main():
    """Main training function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("train_models")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    logger.info("Starting football prediction model training...")
    logger.info(f"Configuration: {vars(args)}")
    
    try:
        # Load data
        if args.data_source == 'database':
            matches_df = load_data_from_database()
            if matches_df.empty:
                logger.info("No data in database, creating sample data for demonstration")
                matches_df = create_sample_training_data()
        elif args.data_source == 'file' and args.data_file:
            matches_df = pd.read_csv(args.data_file)
            logger.info(f"Loaded {len(matches_df)} matches from file")
        else:
            logger.info("Creating sample training data for demonstration")
            matches_df = create_sample_training_data()
        
        if len(matches_df) < args.min_samples:
            logger.error(f"Insufficient data: {len(matches_df)} samples (minimum: {args.min_samples})")
            return 1
        
        # Validate and clean data
        logger.info("Validating and cleaning data...")
        validator = DataValidator()
        cleaner = DataCleaner()
        
        validation_result = validator.validate_matches_data(matches_df)
        if not validation_result.is_valid:
            logger.warning(f"Data validation issues: {validation_result.errors}")
        
        matches_clean = cleaner.clean_matches_data(matches_df)
        
        # Feature engineering
        logger.info("Extracting features...")
        feature_pipeline = FeatureEngineeringPipeline()
        
        # Convert to list of dictionaries for feature extraction
        matches_list = matches_clean.to_dict('records')
        
        # Create feature matrix
        features_df = feature_pipeline.create_feature_matrix(
            matches_list, 
            include_targets=True
        )
        
        logger.info(f"Feature matrix created: {features_df.shape}")
        
        # Check if target column exists
        if args.target not in features_df.columns:
            logger.error(f"Target column '{args.target}' not found. Available columns: {list(features_df.columns)}")
            return 1
        
        # Initialize trainer
        trainer = ModelTrainer()
        
        # Prepare data
        data_splits = trainer.prepare_data(
            features_df,
            target_column=args.target,
            test_size=args.test_size,
            validation_size=args.validation_size,
            time_split=True
        )
        
        # Determine models to train
        if args.models:
            model_types = [m.strip() for m in args.models.split(',')]
        else:
            model_types = ['xgboost', 'lightgbm']  # Default models
        
        # Train models
        training_results = trainer.train_models(
            data_splits,
            model_types=model_types,
            tune_hyperparameters=args.tune_hyperparameters
        )
        
        # Generate and display results
        logger.info("Training completed! Generating report...")
        
        # Model comparison
        comparison_df = trainer.compare_models()
        if not comparison_df.empty:
            print("\n" + "="*60)
            print("MODEL PERFORMANCE COMPARISON")
            print("="*60)
            print(comparison_df.to_string(index=False, float_format='%.4f'))
        
        # Training report
        report = trainer.generate_training_report()
        print("\n" + report)
        
        # Save models if requested
        if args.save_models:
            logger.info("Saving models...")
            saved_paths = trainer.save_models(args.output_dir)
            print(f"\nModels saved to: {saved_paths}")
        
        # Best model info
        best_model = trainer.get_best_model()
        if best_model:
            print(f"\nBest Model: {best_model.model_name}")
            
            # Show feature importance
            top_features = best_model.get_feature_importance(10)
            if top_features:
                print("\nTop 10 Most Important Features:")
                for i, (feature, importance) in enumerate(top_features.items(), 1):
                    print(f"{i:2d}. {feature}: {importance:.4f}")
        
        logger.info("Model training pipeline completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
