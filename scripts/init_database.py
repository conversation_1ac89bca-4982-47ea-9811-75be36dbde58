#!/usr/bin/env python3
"""Initialize the database for the football prediction system."""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.database import DatabaseManager
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/init_database.log')
        ]
    )


def main():
    """Initialize the database."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    setup_logging()
    logger = logging.getLogger("init_database")
    
    logger.info("Starting database initialization...")
    
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Test connection
        if not db_manager.test_connection():
            logger.error("Database connection failed. Please check your configuration.")
            return 1
        
        # Create tables
        logger.info("Creating database tables...")
        db_manager.create_tables()
        
        logger.info("Database initialization completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
