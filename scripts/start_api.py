#!/usr/bin/env python3
"""Start the Football Prediction API server."""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import uvicorn
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/api.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Start Football Prediction API')
    
    parser.add_argument(
        '--host',
        type=str,
        default=None,
        help='Host to bind to (overrides config)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=None,
        help='Port to bind to (overrides config)'
    )
    
    parser.add_argument(
        '--reload',
        action='store_true',
        help='Enable auto-reload for development'
    )
    
    parser.add_argument(
        '--workers',
        type=int,
        default=1,
        help='Number of worker processes'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['debug', 'info', 'warning', 'error'],
        default='info',
        help='Log level'
    )
    
    parser.add_argument(
        '--access-log',
        action='store_true',
        help='Enable access logging'
    )
    
    return parser.parse_args()


def check_prerequisites():
    """Check if all prerequisites are met."""
    logger = logging.getLogger("prerequisites")
    
    # Check if models exist
    models_dir = Path("data/models")
    if not models_dir.exists() or not list(models_dir.glob("*.joblib")):
        logger.warning("No trained models found. Train models first for full functionality.")
    
    # Check API-Football configuration
    try:
        data_sources = config._config.get('data_sources', {})
        api_football_config = data_sources.get('api_football', {})

        if not api_football_config.get('api_key'):
            logger.warning("API-Football API key not configured. Some features may not work.")

    except Exception as e:
        logger.warning(f"Configuration check failed: {e}")
    
    logger.info("Prerequisites check completed")


def main():
    """Main function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("start_api")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    logger.info("Starting Football Prediction API server...")
    
    # Check prerequisites
    check_prerequisites()
    
    # Get configuration
    try:
        api_config = config._config.get('api', {})
    except AttributeError:
        api_config = {}
    
    # Determine host and port
    host = args.host or api_config.get('host', '0.0.0.0')
    port = args.port or api_config.get('port', 8000)
    
    # Server configuration
    server_config = {
        'app': 'src.api.main:app',
        'host': host,
        'port': port,
        'log_level': args.log_level,
        'access_log': args.access_log,
        'reload': args.reload,
        'workers': args.workers if not args.reload else 1  # Reload doesn't work with multiple workers
    }
    
    logger.info(f"Server configuration: {server_config}")
    
    print("\n" + "="*80)
    print("🚀 FOOTBALL PREDICTION API SERVER")
    print("="*80)
    print(f"🌐 Server URL: http://{host}:{port}")
    print(f"📚 API Documentation: http://{host}:{port}/docs")
    print(f"🔍 Interactive API: http://{host}:{port}/redoc")
    print(f"❤️  Health Check: http://{host}:{port}/health")
    print(f"📊 Status: http://{host}:{port}/status")
    print("="*80)
    print("Press Ctrl+C to stop the server")
    print("="*80)
    
    try:
        # Start server
        uvicorn.run(**server_config)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        print("\n👋 Server stopped")
        
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        print(f"❌ Server failed to start: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
