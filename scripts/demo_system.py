#!/usr/bin/env python3
"""Demo script showing the complete football prediction ML system workflow."""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.database import DatabaseManager
from src.data.collection_orchestrator import DataCollectionOrchestrator
from src.features.engineering.feature_pipeline import FeatureEngineeringPipeline
from src.data.processors import DataValidator, DataCleaner
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/demo.log')
        ]
    )


def create_sample_data():
    """Create sample data for demonstration."""
    print("\n=== Creating Sample Data ===")
    
    # Sample teams
    teams_data = [
        {'external_id': 1, 'name': 'Manchester United', 'short_name': 'MUN', 'league_id': 1},
        {'external_id': 2, 'name': 'Manchester City', 'short_name': 'MCI', 'league_id': 1},
        {'external_id': 3, 'name': 'Liverpool', 'short_name': 'LIV', 'league_id': 1},
        {'external_id': 4, 'name': 'Chelsea', 'short_name': 'CHE', 'league_id': 1},
        {'external_id': 5, 'name': 'Arsenal', 'short_name': 'ARS', 'league_id': 1},
        {'external_id': 6, 'name': 'Tottenham', 'short_name': 'TOT', 'league_id': 1}
    ]
    
    # Sample matches (historical and upcoming)
    matches_data = []
    base_date = datetime.now() - timedelta(days=30)
    
    # Historical matches
    for i in range(20):
        match_date = base_date + timedelta(days=i)
        home_team = (i % 6) + 1
        away_team = ((i + 1) % 6) + 1
        if home_team == away_team:
            away_team = ((i + 2) % 6) + 1
        
        # Generate realistic scores
        import random
        home_score = random.randint(0, 4)
        away_score = random.randint(0, 4)
        
        matches_data.append({
            'id': i + 1,
            'external_id': 1000 + i,
            'home_team_id': home_team,
            'away_team_id': away_team,
            'match_date': match_date,
            'league_id': 1,
            'season': '2023-24',
            'status': 'FINISHED',
            'home_score': home_score,
            'away_score': away_score,
            'venue': f'Stadium {home_team}'
        })
    
    # Upcoming matches
    for i in range(5):
        match_date = datetime.now() + timedelta(days=i + 1)
        home_team = (i % 6) + 1
        away_team = ((i + 3) % 6) + 1
        if home_team == away_team:
            away_team = ((i + 4) % 6) + 1
        
        matches_data.append({
            'id': 21 + i,
            'external_id': 1020 + i,
            'home_team_id': home_team,
            'away_team_id': away_team,
            'match_date': match_date,
            'league_id': 1,
            'season': '2023-24',
            'status': 'SCHEDULED',
            'venue': f'Stadium {home_team}'
        })
    
    # Sample players
    players_data = []
    positions = ['GK', 'CB', 'LB', 'RB', 'CM', 'CAM', 'LW', 'RW', 'ST']
    for team_id in range(1, 7):
        for i in range(15):  # 15 players per team
            players_data.append({
                'external_id': team_id * 100 + i,
                'name': f'Player {team_id}-{i}',
                'position': positions[i % len(positions)],
                'age': 20 + (i % 15),
                'team_id': team_id
            })
    
    print(f"Created {len(teams_data)} teams, {len(matches_data)} matches, {len(players_data)} players")
    
    return {
        'teams': teams_data,
        'matches': matches_data,
        'players': players_data,
        'leagues': [{'external_id': 1, 'name': 'Premier League', 'country': 'England'}],
        'injuries': []
    }


def demo_data_validation(sample_data):
    """Demonstrate data validation."""
    print("\n=== Data Validation Demo ===")
    
    validator = DataValidator()
    
    # Convert to DataFrames
    teams_df = pd.DataFrame(sample_data['teams'])
    matches_df = pd.DataFrame(sample_data['matches'])
    players_df = pd.DataFrame(sample_data['players'])
    
    # Validate each data type
    validation_results = {
        'teams': validator.validate_teams_data(teams_df),
        'matches': validator.validate_matches_data(matches_df),
        'players': validator.validate_players_data(players_df)
    }
    
    # Generate report
    report = validator.generate_data_quality_report(validation_results)
    print(report)
    
    return validation_results


def demo_data_cleaning(sample_data):
    """Demonstrate data cleaning."""
    print("\n=== Data Cleaning Demo ===")
    
    cleaner = DataCleaner()
    
    # Convert to DataFrames
    teams_df = pd.DataFrame(sample_data['teams'])
    matches_df = pd.DataFrame(sample_data['matches'])
    players_df = pd.DataFrame(sample_data['players'])
    
    print(f"Before cleaning - Teams: {len(teams_df)}, Matches: {len(matches_df)}, Players: {len(players_df)}")
    
    # Clean data
    teams_clean = cleaner.clean_teams_data(teams_df)
    matches_clean = cleaner.clean_matches_data(matches_df)
    players_clean = cleaner.clean_players_data(players_df)
    
    print(f"After cleaning - Teams: {len(teams_clean)}, Matches: {len(matches_clean)}, Players: {len(players_clean)}")
    
    return {
        'teams': teams_clean.to_dict('records'),
        'matches': matches_clean.to_dict('records'),
        'players': players_clean.to_dict('records')
    }


def demo_feature_engineering(sample_data):
    """Demonstrate feature engineering."""
    print("\n=== Feature Engineering Demo ===")
    
    pipeline = FeatureEngineeringPipeline()
    
    # Use historical matches for feature extraction
    historical_matches = [m for m in sample_data['matches'] if m['status'] == 'FINISHED']
    
    print(f"Extracting features for {len(historical_matches)} historical matches...")
    
    # Extract features
    features_df = pipeline.extract_features(historical_matches, parallel=False)
    
    print(f"Feature extraction completed:")
    print(f"  - Shape: {features_df.shape}")
    print(f"  - Features: {list(features_df.columns)[:10]}...")  # Show first 10 features
    
    # Create feature matrix
    feature_matrix = pipeline.create_feature_matrix(historical_matches)
    
    print(f"Feature matrix created:")
    print(f"  - Shape: {feature_matrix.shape}")
    print(f"  - Missing values: {feature_matrix.isnull().sum().sum()}")
    
    return feature_matrix


def demo_database_operations(sample_data):
    """Demonstrate database operations."""
    print("\n=== Database Operations Demo ===")
    
    db_manager = DatabaseManager()
    
    # Create tables
    db_manager.create_tables()
    print("✓ Database tables created")
    
    # Save sample data
    saved_counts = {}
    
    if sample_data.get('leagues'):
        saved_counts['leagues'] = db_manager.save_leagues(sample_data['leagues'])
    
    if sample_data.get('teams'):
        saved_counts['teams'] = db_manager.save_teams(sample_data['teams'])
    
    if sample_data.get('matches'):
        saved_counts['matches'] = db_manager.save_matches(sample_data['matches'])
    
    if sample_data.get('players'):
        saved_counts['players'] = db_manager.save_players(sample_data['players'])
    
    print(f"✓ Data saved to database: {saved_counts}")
    
    # Query some data
    recent_matches = db_manager.get_recent_matches(days=30)
    upcoming_matches = db_manager.get_upcoming_matches(days=7)
    
    print(f"✓ Queried {len(recent_matches)} recent matches and {len(upcoming_matches)} upcoming matches")
    
    return saved_counts


def demo_prediction_workflow(sample_data):
    """Demonstrate the complete prediction workflow."""
    print("\n=== Prediction Workflow Demo ===")
    
    # Get upcoming matches
    upcoming_matches = [m for m in sample_data['matches'] if m['status'] == 'SCHEDULED']
    
    if not upcoming_matches:
        print("No upcoming matches found for prediction")
        return
    
    print(f"Making predictions for {len(upcoming_matches)} upcoming matches...")
    
    # Extract features for upcoming matches
    pipeline = FeatureEngineeringPipeline()
    features_df = pipeline.extract_features(upcoming_matches, parallel=False)
    
    print("Features extracted for upcoming matches:")
    for i, match in enumerate(upcoming_matches):
        home_team = match['home_team_id']
        away_team = match['away_team_id']
        match_date = match['match_date']
        
        print(f"  Match {i+1}: Team {home_team} vs Team {away_team} on {match_date}")
        
        # Show some key features (if available)
        if i < len(features_df):
            row = features_df.iloc[i]
            if 'home_recent_form' in row:
                print(f"    Home form: {row.get('home_recent_form', 'N/A'):.3f}")
            if 'away_recent_form' in row:
                print(f"    Away form: {row.get('away_recent_form', 'N/A'):.3f}")
            if 'form_difference' in row:
                print(f"    Form difference: {row.get('form_difference', 'N/A'):.3f}")
    
    print("✓ Prediction workflow completed (features ready for ML models)")


def main():
    """Run the complete system demo."""
    print("Football Prediction ML System - Complete Demo")
    print("=" * 60)
    
    # Setup
    setup_logging()
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 1. Create sample data
        sample_data = create_sample_data()
        
        # 2. Demonstrate data validation
        validation_results = demo_data_validation(sample_data)
        
        # 3. Demonstrate data cleaning
        cleaned_data = demo_data_cleaning(sample_data)
        
        # 4. Demonstrate database operations
        db_results = demo_database_operations(cleaned_data)
        
        # 5. Demonstrate feature engineering
        feature_matrix = demo_feature_engineering(cleaned_data)
        
        # 6. Demonstrate prediction workflow
        demo_prediction_workflow(cleaned_data)
        
        print("\n" + "=" * 60)
        print("🎉 Demo completed successfully!")
        print("\nNext steps:")
        print("1. Collect real data using the data collection orchestrator")
        print("2. Train ML models using the feature matrix")
        print("3. Set up the API for serving predictions")
        print("4. Implement monitoring and retraining pipelines")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logging.exception("Demo failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
