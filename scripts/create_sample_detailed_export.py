#!/usr/bin/env python3
"""Create a sample detailed predictions CSV to demonstrate the export format."""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_sample_detailed_export():
    """Create a sample detailed predictions CSV."""
    
    # Sample data representing what the detailed export would contain
    sample_data = []
    
    # Create 10 sample matches with comprehensive data
    teams = [
        ("Manchester City", "Liverpool"),
        ("Arsenal", "Chelsea"),
        ("Barcelona", "Real Madrid"),
        ("Bayern Munich", "Borussia Dortmund"),
        ("PSG", "Marseille"),
        ("Juventus", "AC Milan"),
        ("Manchester United", "Tottenham"),
        ("Atletico Madrid", "Valencia"),
        ("Inter Milan", "Napoli"),
        ("Borussia Dortmund", "RB Leipzig")
    ]
    
    leagues = [
        "Premier League", "Premier League", "La Liga", "Bundesliga", "Ligue 1",
        "Serie A", "Premier League", "La Liga", "Serie A", "Bundesliga"
    ]
    
    venues = [
        "Etihad Stadium", "Emirates Stadium", "Camp Nou", "Allianz Arena", "Parc des Princes",
        "Allianz Stadium", "Old Trafford", "Wanda Metropolitano", "San Siro", "Signal Iduna Park"
    ]
    
    referees = [
        "Michael Oliver", "Anthony Taylor", "Antonio Mateu Lahoz", "Felix Brych", "Clement Turpin",
        "Daniele Orsato", "Paul Tierney", "Jesus Gil Manzano", "Davide Massa", "Tobias Stieler"
    ]
    
    for i, ((home_team, away_team), league, venue, referee) in enumerate(zip(teams, leagues, venues, referees)):
        # Generate realistic match data
        match_date = datetime.now() - timedelta(days=np.random.randint(1, 30))
        
        # Generate realistic scores
        home_score = np.random.poisson(1.5)
        away_score = np.random.poisson(1.2)
        home_score_ht = min(home_score, np.random.poisson(0.8))
        away_score_ht = min(away_score, np.random.poisson(0.6))
        
        # Determine results
        if home_score > away_score:
            actual_result = 'H'
            actual_result_text = 'Home Win'
        elif away_score > home_score:
            actual_result = 'A'
            actual_result_text = 'Away Win'
        else:
            actual_result = 'D'
            actual_result_text = 'Draw'
        
        # Generate realistic predictions
        if actual_result == 'H':
            home_prob = np.random.uniform(0.4, 0.8)
            away_prob = np.random.uniform(0.1, 0.3)
            draw_prob = 1.0 - home_prob - away_prob
            predicted_result = 'H'
            predicted_text = 'Home Win'
            prediction_correct = 1
        elif actual_result == 'A':
            away_prob = np.random.uniform(0.4, 0.7)
            home_prob = np.random.uniform(0.1, 0.3)
            draw_prob = 1.0 - home_prob - away_prob
            predicted_result = 'A'
            predicted_text = 'Away Win'
            prediction_correct = 1
        else:
            draw_prob = np.random.uniform(0.3, 0.6)
            home_prob = np.random.uniform(0.2, 0.4)
            away_prob = 1.0 - home_prob - draw_prob
            predicted_result = 'D'
            predicted_text = 'Draw'
            prediction_correct = 1
        
        # Sometimes make wrong predictions
        if np.random.random() < 0.3:  # 30% wrong predictions
            prediction_correct = 0
            if predicted_result == 'H':
                predicted_result = np.random.choice(['D', 'A'])
            elif predicted_result == 'A':
                predicted_result = np.random.choice(['H', 'D'])
            else:
                predicted_result = np.random.choice(['H', 'A'])
            predicted_text = {'H': 'Home Win', 'D': 'Draw', 'A': 'Away Win'}[predicted_result]
        
        confidence = max(home_prob, draw_prob, away_prob)
        confidence_level = "High" if confidence > 0.6 else "Medium" if confidence > 0.4 else "Low"
        
        # Generate match statistics
        home_possession = np.random.uniform(35, 75)
        away_possession = 100 - home_possession
        
        home_shots = np.random.poisson(12)
        away_shots = np.random.poisson(10)
        home_shots_on_target = min(home_shots, np.random.poisson(5))
        away_shots_on_target = min(away_shots, np.random.poisson(4))
        
        home_corners = np.random.poisson(5)
        away_corners = np.random.poisson(4)
        home_fouls = np.random.poisson(12)
        away_fouls = np.random.poisson(11)
        
        # Generate cards
        home_yellow_cards = np.random.poisson(2)
        away_yellow_cards = np.random.poisson(2)
        home_red_cards = 1 if np.random.random() < 0.1 else 0
        away_red_cards = 1 if np.random.random() < 0.1 else 0
        
        # Generate team statistics
        home_matches_played = np.random.randint(20, 35)
        home_wins = np.random.randint(8, 25)
        home_draws = np.random.randint(3, 10)
        home_losses = home_matches_played - home_wins - home_draws
        home_goals_for = np.random.randint(25, 80)
        home_goals_against = np.random.randint(15, 60)
        home_points = home_wins * 3 + home_draws
        
        away_matches_played = np.random.randint(20, 35)
        away_wins = np.random.randint(8, 25)
        away_draws = np.random.randint(3, 10)
        away_losses = away_matches_played - away_wins - away_draws
        away_goals_for = np.random.randint(25, 80)
        away_goals_against = np.random.randint(15, 60)
        away_points = away_wins * 3 + away_draws
        
        sample_record = {
            # Match Identification
            'match_id': 1000 + i,
            'external_id': 500000 + i,
            'match_date': match_date.isoformat(),
            'league_name': league,
            'home_team': home_team,
            'away_team': away_team,
            'venue': venue,
            'referee': referee,
            'match_status': 'FT',
            
            # Actual Results
            'actual_home_score': home_score,
            'actual_away_score': away_score,
            'actual_home_score_ht': home_score_ht,
            'actual_away_score_ht': away_score_ht,
            'actual_home_score_ft': home_score,
            'actual_away_score_ft': away_score,
            'actual_result': actual_result,
            'actual_result_ht': 'H' if home_score_ht > away_score_ht else 'A' if away_score_ht > home_score_ht else 'D',
            
            # Goals and Scoring
            'total_goals': home_score + away_score,
            'goal_difference': home_score - away_score,
            'both_teams_scored': 1 if home_score > 0 and away_score > 0 else 0,
            'clean_sheet_home': 1 if away_score == 0 else 0,
            'clean_sheet_away': 1 if home_score == 0 else 0,
            
            # Predictions
            'predicted_result': predicted_result,
            'predicted_result_text': predicted_text,
            'home_win_probability': round(home_prob, 3),
            'draw_probability': round(draw_prob, 3),
            'away_win_probability': round(away_prob, 3),
            'prediction_confidence': round(confidence, 3),
            'confidence_level': confidence_level,
            'prediction_correct': prediction_correct,
            
            # Prediction Factors
            'home_form_score': round(np.random.uniform(0.3, 0.8), 3),
            'away_form_score': round(np.random.uniform(0.3, 0.8), 3),
            'form_advantage': np.random.choice(['Home', 'Away', 'Equal']),
            'home_injury_impact': round(np.random.uniform(0, 0.5), 3),
            'away_injury_impact': round(np.random.uniform(0, 0.5), 3),
            'injury_advantage': np.random.choice(['Home', 'Away', 'Equal']),
            'h2h_home_win_rate': round(np.random.uniform(0.2, 0.6), 3),
            'h2h_away_win_rate': round(np.random.uniform(0.2, 0.6), 3),
            'h2h_advantage': np.random.choice(['Home', 'Away', 'Equal']),
            
            # Match Statistics
            'home_possession': round(home_possession, 1),
            'away_possession': round(away_possession, 1),
            'home_shots': home_shots,
            'away_shots': away_shots,
            'home_shots_on_target': home_shots_on_target,
            'away_shots_on_target': away_shots_on_target,
            'home_corners': home_corners,
            'away_corners': away_corners,
            'home_fouls': home_fouls,
            'away_fouls': away_fouls,
            'home_offsides': np.random.randint(0, 8),
            'away_offsides': np.random.randint(0, 8),
            
            # Cards and Events
            'home_yellow_cards': home_yellow_cards,
            'away_yellow_cards': away_yellow_cards,
            'home_red_cards': home_red_cards,
            'away_red_cards': away_red_cards,
            'total_cards': home_yellow_cards + away_yellow_cards + home_red_cards + away_red_cards,
            'home_goals_events': home_score,
            'away_goals_events': away_score,
            'home_substitutions': np.random.randint(3, 6),
            'away_substitutions': np.random.randint(3, 6),
            'total_substitutions': np.random.randint(6, 12),
            
            # Team Statistics
            'home_matches_played': home_matches_played,
            'home_wins': home_wins,
            'home_draws': home_draws,
            'home_losses': home_losses,
            'home_goals_for': home_goals_for,
            'home_goals_against': home_goals_against,
            'home_points': home_points,
            'home_win_rate': round(home_wins / home_matches_played, 3),
            
            'away_matches_played': away_matches_played,
            'away_wins': away_wins,
            'away_draws': away_draws,
            'away_losses': away_losses,
            'away_goals_for': away_goals_for,
            'away_goals_against': away_goals_against,
            'away_points': away_points,
            'away_win_rate': round(away_wins / away_matches_played, 3),
        }
        
        sample_data.append(sample_record)
    
    # Create DataFrame
    df = pd.DataFrame(sample_data)
    
    # Create output directory
    output_dir = Path("data/exports")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Export to CSV
    output_file = output_dir / "sample_detailed_predictions.csv"
    df.to_csv(output_file, index=False)
    
    print("\n" + "="*80)
    print("📊 SAMPLE DETAILED PREDICTIONS CSV CREATED")
    print("="*80)
    print(f"📁 File: {output_file}")
    print(f"📋 Rows: {len(df)}")
    print(f"📊 Columns: {len(df.columns)}")
    print("\n🏆 SAMPLE DATA INCLUDES:")
    print("-" * 40)
    print("✅ Match Information (ID, Date, Teams, Venue, Referee)")
    print("✅ Actual Results (Full-time, Half-time, Scores)")
    print("✅ Predictions (Result, Probabilities, Confidence)")
    print("✅ Match Statistics (Possession, Shots, Corners, Fouls)")
    print("✅ Cards & Events (Yellow/Red Cards, Substitutions)")
    print("✅ Team Statistics (Season Performance, Win Rates)")
    print("✅ Analysis Factors (Form, Injuries, Head-to-Head)")
    print("="*80)
    print(f"📈 Prediction Accuracy: {df['prediction_correct'].mean():.1%}")
    print(f"🎯 Average Confidence: {df['prediction_confidence'].mean():.3f}")
    print(f"⚽ Average Goals per Match: {df['total_goals'].mean():.1f}")
    print(f"🟨 Average Cards per Match: {df['total_cards'].mean():.1f}")
    print("="*80)
    
    # Show first few rows
    print("\n📋 FIRST 3 ROWS PREVIEW:")
    print("-" * 40)
    preview_columns = [
        'match_date', 'home_team', 'away_team', 'actual_home_score', 'actual_away_score',
        'predicted_result_text', 'prediction_confidence', 'total_cards', 'home_possession'
    ]
    print(df[preview_columns].head(3).to_string(index=False))
    print("="*80)
    
    return str(output_file)


if __name__ == "__main__":
    create_sample_detailed_export()
