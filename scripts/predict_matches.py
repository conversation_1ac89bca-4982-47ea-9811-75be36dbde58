#!/usr/bin/env python3
"""Comprehensive match prediction CLI tool."""

import sys
import os
import argparse
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.prediction.pipeline import FootballPredictionPipeline
from src.prediction.service import PredictionService
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/predictions.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Football match prediction tool')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Single match prediction
    predict_parser = subparsers.add_parser('predict', help='Predict a single match')
    predict_parser.add_argument('--home-team-id', type=int, required=True, help='Home team ID')
    predict_parser.add_argument('--away-team-id', type=int, required=True, help='Away team ID')
    predict_parser.add_argument('--match-date', type=str, help='Match date (YYYY-MM-DD HH:MM)')
    predict_parser.add_argument('--league-id', type=int, help='League ID')
    predict_parser.add_argument('--save', action='store_true', help='Save prediction to database')
    
    # Upcoming matches prediction
    upcoming_parser = subparsers.add_parser('upcoming', help='Predict upcoming matches')
    upcoming_parser.add_argument('--days-ahead', type=int, default=7, help='Days ahead to predict')
    upcoming_parser.add_argument('--leagues', nargs='+', help='Specific league names')
    upcoming_parser.add_argument('--save-all', action='store_true', help='Save all predictions')
    
    # Daily predictions
    daily_parser = subparsers.add_parser('daily', help='Predict matches for a specific day')
    daily_parser.add_argument('--date', type=str, help='Target date (YYYY-MM-DD), defaults to tomorrow')
    daily_parser.add_argument('--save', action='store_true', help='Save predictions to database')
    
    # Batch predictions
    batch_parser = subparsers.add_parser('batch', help='Batch predict from file')
    batch_parser.add_argument('--input-file', type=str, required=True, help='Input CSV file with matches')
    batch_parser.add_argument('--output-file', type=str, help='Output file for results')
    
    # Performance report
    report_parser = subparsers.add_parser('report', help='Generate prediction performance report')
    report_parser.add_argument('--start-date', type=str, required=True, help='Start date (YYYY-MM-DD)')
    report_parser.add_argument('--end-date', type=str, required=True, help='End date (YYYY-MM-DD)')
    report_parser.add_argument('--export', type=str, help='Export detailed results to file')
    
    # Model info
    info_parser = subparsers.add_parser('info', help='Show model information')
    
    return parser.parse_args()


def predict_single_match(args):
    """Predict a single match."""
    logger = logging.getLogger("predict_single")
    
    try:
        pipeline = FootballPredictionPipeline()
        
        # Parse match date if provided
        match_date = None
        if args.match_date:
            match_date = datetime.strptime(args.match_date, '%Y-%m-%d %H:%M')
        
        logger.info(f"Predicting match: Team {args.home_team_id} vs Team {args.away_team_id}")
        
        # Make prediction
        prediction = pipeline.predict_match(
            home_team_id=args.home_team_id,
            away_team_id=args.away_team_id,
            match_date=match_date,
            league_id=args.league_id
        )
        
        # Display results
        print("\n" + "="*80)
        print("MATCH PREDICTION RESULTS")
        print("="*80)
        print(f"Home Team ID: {args.home_team_id}")
        print(f"Away Team ID: {args.away_team_id}")
        print(f"Match Date: {match_date or 'Not specified'}")
        print(f"League ID: {args.league_id or 'Not specified'}")
        print()
        
        print("PREDICTION:")
        print(f"  Predicted Outcome: {prediction['predicted_outcome_text']} ({prediction['predicted_outcome']})")
        print(f"  Confidence: {prediction['confidence']:.3f} ({prediction['confidence_level']})")
        print()
        
        print("PROBABILITIES:")
        probs = prediction['probabilities']
        print(f"  Home Win: {probs['home_win']:.3f} ({probs['home_win']*100:.1f}%)")
        print(f"  Draw:     {probs['draw']:.3f} ({probs['draw']*100:.1f}%)")
        print(f"  Away Win: {probs['away_win']:.3f} ({probs['away_win']*100:.1f}%)")
        print()
        
        # Show key factors
        factors = prediction.get('prediction_factors', {})
        if factors:
            print("KEY FACTORS:")
            for factor_name, factor_data in factors.items():
                if isinstance(factor_data, dict) and 'advantage' in factor_data:
                    print(f"  {factor_name.replace('_', ' ').title()}: {factor_data['advantage']} advantage")
        
        # Show top features
        feature_importance = prediction.get('feature_importance', {})
        if feature_importance:
            print("\nTOP INFLUENTIAL FEATURES:")
            for i, (feature, importance) in enumerate(list(feature_importance.items())[:5], 1):
                print(f"  {i}. {feature}: {importance:.4f}")
        
        print("="*80)
        
        # Save if requested
        if args.save:
            service = PredictionService()
            if service.save_prediction(prediction):
                print("✅ Prediction saved to database")
            else:
                print("❌ Failed to save prediction")
        
        return 0
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        print(f"❌ Prediction failed: {e}")
        return 1


async def predict_upcoming_matches(args):
    """Predict upcoming matches."""
    logger = logging.getLogger("predict_upcoming")
    
    try:
        service = PredictionService()
        
        # Get league IDs if specified
        league_ids = None
        if args.leagues:
            leagues_config = config.get_leagues()
            league_ids = []
            for league_name in args.leagues:
                if league_name in leagues_config:
                    league_ids.append(leagues_config[league_name]['id'])
                else:
                    logger.warning(f"Unknown league: {league_name}")
        
        logger.info(f"Predicting upcoming matches for next {args.days_ahead} days")
        
        # Get upcoming matches
        upcoming_matches = service.pipeline._get_upcoming_matches(args.days_ahead, league_ids)
        
        if not upcoming_matches:
            print("No upcoming matches found")
            return 0
        
        print(f"\nFound {len(upcoming_matches)} upcoming matches")
        
        # Make predictions
        predictions = await service.predict_batch_async(upcoming_matches)
        
        # Display results
        print("\n" + "="*100)
        print("UPCOMING MATCHES PREDICTIONS")
        print("="*100)
        
        for i, (match, prediction) in enumerate(zip(upcoming_matches, predictions), 1):
            print(f"\n{i}. {match.get('home_team_name', 'Home')} vs {match.get('away_team_name', 'Away')}")
            print(f"   Date: {match['match_date']}")
            print(f"   Prediction: {prediction['predicted_outcome_text']} (Confidence: {prediction['confidence']:.3f})")
            
            probs = prediction['probabilities']
            print(f"   Probabilities: H:{probs['home_win']:.2f} D:{probs['draw']:.2f} A:{probs['away_win']:.2f}")
        
        print("="*100)
        
        # Save if requested
        if args.save_all:
            saved_count = 0
            for match, prediction in zip(upcoming_matches, predictions):
                if service.save_prediction(prediction, match.get('id')):
                    saved_count += 1
            
            print(f"✅ Saved {saved_count}/{len(predictions)} predictions to database")
        
        return 0
        
    except Exception as e:
        logger.error(f"Upcoming predictions failed: {e}")
        print(f"❌ Upcoming predictions failed: {e}")
        return 1


async def predict_daily_matches(args):
    """Predict matches for a specific day."""
    logger = logging.getLogger("predict_daily")
    
    try:
        service = PredictionService()
        
        # Parse target date
        target_date = None
        if args.date:
            target_date = datetime.strptime(args.date, '%Y-%m-%d')
        
        logger.info(f"Predicting daily matches for {target_date or 'tomorrow'}")
        
        # Make daily predictions
        results = await service.predict_daily_matches(target_date)
        
        print("\n" + "="*80)
        print("DAILY PREDICTIONS RESULTS")
        print("="*80)
        print(f"Target Date: {results['target_date']}")
        print(f"Matches Found: {results['matches_found']}")
        print(f"Predictions Made: {results['predictions_made']}")
        print(f"Predictions Saved: {results['predictions_saved']}")
        print("="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"Daily predictions failed: {e}")
        print(f"❌ Daily predictions failed: {e}")
        return 1


def generate_report(args):
    """Generate prediction performance report."""
    logger = logging.getLogger("generate_report")
    
    try:
        service = PredictionService()
        
        # Parse dates
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        
        logger.info(f"Generating report for {start_date.date()} to {end_date.date()}")
        
        # Generate report
        report = service.generate_prediction_report(start_date, end_date)
        
        print("\n" + "="*80)
        print("PREDICTION PERFORMANCE REPORT")
        print("="*80)
        print(f"Period: {report['period']}")
        print(f"Total Predictions: {report['total_predictions']}")
        print(f"Correct Predictions: {report['correct_predictions']}")
        print(f"Accuracy: {report['accuracy']:.3f} ({report['accuracy']*100:.1f}%)")
        print(f"Average Confidence: {report['average_confidence']:.3f}")
        print(f"Performance Grade: {report['performance_grade']}")
        print("="*80)
        
        # Export if requested
        if args.export:
            if service.export_predictions(start_date, end_date, args.export):
                print(f"✅ Detailed results exported to {args.export}")
            else:
                print("❌ Failed to export results")
        
        return 0
        
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        print(f"❌ Report generation failed: {e}")
        return 1


def show_model_info(args):
    """Show model information."""
    logger = logging.getLogger("model_info")
    
    try:
        pipeline = FootballPredictionPipeline()
        
        print("\n" + "="*80)
        print("MODEL INFORMATION")
        print("="*80)
        
        if pipeline.model:
            print(f"Model Name: {getattr(pipeline.model, 'model_name', 'Unknown')}")
            print(f"Model Type: {getattr(pipeline.model, 'model_type', 'Unknown')}")
            print(f"Is Trained: {getattr(pipeline.model, 'is_trained', False)}")
            print(f"Feature Count: {len(pipeline.feature_names)}")
            
            if pipeline.model_metadata:
                print("\nModel Metadata:")
                for key, value in pipeline.model_metadata.items():
                    print(f"  {key}: {value}")
        else:
            print("No model loaded")
        
        print("="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        print(f"❌ Failed to get model info: {e}")
        return 1


async def main():
    """Main function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("predict_matches")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    if not args.command:
        print("No command specified. Use --help for available commands.")
        return 1
    
    logger.info(f"Starting prediction command: {args.command}")
    
    try:
        # Execute command
        if args.command == 'predict':
            return predict_single_match(args)
        elif args.command == 'upcoming':
            return await predict_upcoming_matches(args)
        elif args.command == 'daily':
            return await predict_daily_matches(args)
        elif args.command == 'report':
            return generate_report(args)
        elif args.command == 'info':
            return show_model_info(args)
        else:
            print(f"Unknown command: {args.command}")
            return 1
            
    except Exception as e:
        logger.error(f"Command execution failed: {e}")
        print(f"❌ Command failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
