#!/usr/bin/env python3
"""Test real predictions with trained model using proper feature engineering."""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.implementations.random_forest_model import RandomForestFootballModel
from src.features.engineering.feature_pipeline import FeatureEngineeringPipeline
from src.utils.database import DatabaseManager
from src.utils.database.models import Match, Team
import pandas as pd
import numpy as np


def test_real_predictions():
    """Test real predictions with trained model."""
    
    print("🔮 TESTING REAL PREDICTIONS WITH TRAINED MODEL")
    print("="*70)
    
    # 1. Check for saved models
    model_dir = Path("data/models")
    if not model_dir.exists():
        print("❌ Model directory not found")
        return False
    
    model_files = list(model_dir.glob("*.joblib"))
    if not model_files:
        print("❌ No saved models found")
        return False
    
    print(f"✅ Found {len(model_files)} saved model(s):")
    for model_file in model_files:
        print(f"   - {model_file.name}")
    
    # 2. Load the trained model
    try:
        model = RandomForestFootballModel()
        model_path = model_files[0]  # Use the first (most recent) model
        model.load_model(str(model_path))
        print(f"✅ Model loaded from {model_path.name}")
        
        # Show model info
        model_info = model.get_model_info()
        print(f"   Model type: {model_info['model_type']}")
        print(f"   Features: {model_info.get('n_features_in', 'Unknown')}")
        print(f"   Classes: {model_info.get('n_classes', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return False
    
    # 3. Get real match data from database for feature engineering
    print(f"\n📊 PREPARING REAL MATCH DATA FOR PREDICTION")
    print("-" * 50)
    
    try:
        with DatabaseManager().get_session() as session:
            # Get some real matches from database
            matches = session.query(Match).filter(
                Match.status == 'FT',
                Match.home_score.isnot(None),
                Match.away_score.isnot(None)
            ).limit(5).all()
            
            if not matches:
                print("❌ No finished matches found in database")
                return False
            
            print(f"✅ Found {len(matches)} finished matches for testing")
            
            # Show sample matches and convert to dictionaries while session is active
            teams = {team.id: team.name for team in session.query(Team).all()}

            print("\n📋 Sample matches for prediction testing:")
            match_data = []
            for i, match in enumerate(matches, 1):
                home_team = teams.get(match.home_team_id, f"Team {match.home_team_id}")
                away_team = teams.get(match.away_team_id, f"Team {match.away_team_id}")
                actual_result = "H" if match.home_score > match.away_score else "A" if match.away_score > match.home_score else "D"

                print(f"   {i}. {home_team} {match.home_score}-{match.away_score} {away_team}")
                print(f"      Date: {match.match_date.strftime('%Y-%m-%d')}, Actual: {actual_result}")

                # Convert to dictionary while session is active
                match_dict = {
                    'id': match.id,
                    'external_id': match.external_id,
                    'league_id': match.league_id,
                    'season': match.season,
                    'home_team_id': match.home_team_id,
                    'away_team_id': match.away_team_id,
                    'match_date': match.match_date,
                    'home_score': match.home_score,
                    'away_score': match.away_score,
                    'status': match.status
                }
                match_data.append(match_dict)
    
    except Exception as e:
        print(f"❌ Failed to get match data: {e}")
        return False
    
    # 4. Test feature engineering pipeline
    print(f"\n🔧 TESTING FEATURE ENGINEERING")
    print("-" * 50)
    
    try:
        # Initialize feature engineering pipeline
        feature_pipeline = FeatureEngineeringPipeline()
        print("✅ Feature engineering pipeline initialized")
        
        # Use the pre-converted match_data from the database session
        
        # Create feature matrix
        print("🔄 Extracting features from real match data...")
        feature_matrix = feature_pipeline.create_feature_matrix(match_data)
        
        if feature_matrix is not None and not feature_matrix.empty:
            print(f"✅ Feature matrix created: {feature_matrix.shape}")
            print(f"   Features: {list(feature_matrix.columns)[:5]}... (showing first 5)")
        else:
            print("❌ Failed to create feature matrix")
            return False
    
    except Exception as e:
        print(f"❌ Feature engineering failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. Make real predictions
    print(f"\n🎯 MAKING REAL PREDICTIONS")
    print("-" * 50)
    
    try:
        # Remove target columns if they exist (we're predicting these)
        prediction_features = feature_matrix.copy()
        target_columns = ['result', 'home_score', 'away_score', 'total_goals']
        for col in target_columns:
            if col in prediction_features.columns:
                prediction_features = prediction_features.drop(columns=[col])
        
        print(f"📊 Prediction features shape: {prediction_features.shape}")
        
        # Make predictions
        predictions = model.predict(prediction_features)
        probabilities = model.predict_proba(prediction_features)
        
        print("✅ Predictions completed successfully!")
        
        # Map predictions to results
        class_mapping = {0: 'Away Win', 1: 'Draw', 2: 'Home Win'}
        
        print(f"\n🏆 PREDICTION RESULTS:")
        print("-" * 40)
        
        for i, (match_dict, pred, probs) in enumerate(zip(match_data, predictions, probabilities)):
            home_team = teams.get(match_dict['home_team_id'], f"Team {match_dict['home_team_id']}")
            away_team = teams.get(match_dict['away_team_id'], f"Team {match_dict['away_team_id']}")

            # Actual result
            actual_result = "H" if match_dict['home_score'] > match_dict['away_score'] else "A" if match_dict['away_score'] > match_dict['home_score'] else "D"
            actual_text = {"H": "Home Win", "D": "Draw", "A": "Away Win"}[actual_result]

            # Predicted result
            predicted_text = class_mapping.get(pred, 'Unknown')
            confidence = max(probs)

            # Check if prediction is correct
            pred_code = {0: 'A', 1: 'D', 2: 'H'}[pred]
            correct = "✅" if pred_code == actual_result else "❌"

            print(f"\n{i+1}. {home_team} vs {away_team}")
            print(f"   Actual: {match_dict['home_score']}-{match_dict['away_score']} ({actual_text})")
            print(f"   Predicted: {predicted_text} (confidence: {confidence:.3f}) {correct}")
            print(f"   Probabilities: H:{probs[2]:.3f} D:{probs[1]:.3f} A:{probs[0]:.3f}")

        # Calculate accuracy
        correct_predictions = sum(1 for match_dict, pred in zip(match_data, predictions)
                                if (match_dict['home_score'] > match_dict['away_score'] and pred == 2) or
                                   (match_dict['away_score'] > match_dict['home_score'] and pred == 0) or
                                   (match_dict['home_score'] == match_dict['away_score'] and pred == 1))
        
        accuracy = correct_predictions / len(match_data)
        print(f"\n📈 PREDICTION ACCURACY: {accuracy:.1%} ({correct_predictions}/{len(match_data)})")
        
    except Exception as e:
        print(f"❌ Prediction failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 6. Show feature importance
    print(f"\n🔍 TOP FEATURE IMPORTANCE:")
    print("-" * 40)
    
    try:
        top_features = model.get_feature_importance(10)
        for _, row in top_features.iterrows():
            print(f"   {row['feature']}: {row['importance']:.4f}")
    except Exception as e:
        print(f"⚠️  Could not get feature importance: {e}")
    
    print(f"\n🎉 REAL PREDICTION TEST COMPLETED SUCCESSFULLY!")
    print("="*70)
    print("✅ Model loaded and working correctly")
    print("✅ Feature engineering pipeline operational")
    print("✅ Real predictions made on historical data")
    print("✅ Prediction accuracy calculated")
    print("✅ System ready for live predictions!")
    
    return True


if __name__ == "__main__":
    success = test_real_predictions()
    if success:
        print("\n🚀 READY FOR PRODUCTION PREDICTIONS!")
    else:
        print("\n❌ Some issues need to be resolved")
        sys.exit(1)
