#!/usr/bin/env python3
"""Find leagues that are currently active in July 2025."""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.collectors.api_football_client import APIFootballClient


def find_active_leagues_july():
    """Find leagues that are currently active in July 2025."""
    
    print("🏈 FINDING ACTIVE LEAGUES FOR JULY 2025")
    print("="*70)
    
    client = APIFootballClient()
    
    # Leagues that might be active in July (summer leagues, different seasons)
    summer_leagues = [
        # MLS (USA/Canada) - March to November
        {'id': 253, 'name': 'Major League Soccer', 'country': 'USA'},
        
        # Brazilian leagues - typically run Jan-Dec
        {'id': 71, 'name': 'Serie A', 'country': 'Brazil'},
        {'id': 72, 'name': 'Serie B', 'country': 'Brazil'},
        
        # Argentine leagues
        {'id': 128, 'name': 'Liga Profesional', 'country': 'Argentina'},
        
        # Mexican leagues
        {'id': 262, 'name': 'Liga MX', 'country': 'Mexico'},
        
        # Japanese leagues
        {'id': 98, 'name': 'J1 League', 'country': 'Japan'},
        
        # Australian leagues
        {'id': 188, 'name': 'A-League', 'country': 'Australia'},
        
        # Chinese leagues
        {'id': 169, 'name': 'Super League', 'country': 'China'},
        
        # South Korean leagues
        {'id': 292, 'name': 'K League 1', 'country': 'South Korea'},
        
        # Nordic leagues (summer season)
        {'id': 113, 'name': 'Allsvenskan', 'country': 'Sweden'},
        {'id': 103, 'name': 'Eliteserien', 'country': 'Norway'},
        {'id': 244, 'name': 'Veikkausliiga', 'country': 'Finland'},
        
        # Eastern European leagues
        {'id': 235, 'name': 'Premier League', 'country': 'Russia'},
        {'id': 333, 'name': 'Premier League', 'country': 'Ukraine'},
        
        # International tournaments that might be ongoing
        {'id': 1, 'name': 'World Cup', 'country': 'World'},
        {'id': 4, 'name': 'Euro Championship', 'country': 'Europe'},
        {'id': 9, 'name': 'Copa America', 'country': 'South America'},
        {'id': 5, 'name': 'Nations League', 'country': 'Europe'},
        
        # Club friendlies and pre-season
        {'id': 667, 'name': 'Friendlies Clubs', 'country': 'World'},
        
        # Other potential active leagues
        {'id': 218, 'name': 'Liga I', 'country': 'Romania'},
        {'id': 327, 'name': 'Premier League', 'country': 'Bulgaria'},
        {'id': 271, 'name': 'HNL', 'country': 'Croatia'},
    ]
    
    active_leagues = []
    
    print("🔍 CHECKING SUMMER/ACTIVE LEAGUES...")
    print("-" * 70)
    
    for league_info in summer_leagues:
        league_id = league_info['id']
        league_name = league_info['name']
        country = league_info['country']
        
        print(f"\n📊 {league_name} ({country}) - ID: {league_id}")
        print("-" * 50)
        
        try:
            # Get league information
            response = client._make_request('/leagues', {'id': league_id})
            
            if response.get('response') and len(response['response']) > 0:
                league_data = response['response'][0]
                seasons = league_data.get('seasons', [])
                
                # Find current season
                current_season = None
                current_season_info = None
                for season in seasons:
                    if season.get('current'):
                        current_season = season.get('year')
                        current_season_info = season
                        break
                
                if current_season:
                    print(f"✅ Current Season: {current_season}")
                    print(f"   Start: {current_season_info.get('start', 'Unknown')}")
                    print(f"   End: {current_season_info.get('end', 'Unknown')}")
                    
                    # Check if season is currently active
                    season_start = current_season_info.get('start')
                    season_end = current_season_info.get('end')
                    now = datetime.now().strftime('%Y-%m-%d')
                    
                    is_active = False
                    if season_start and season_end:
                        is_active = season_start <= now <= season_end
                    
                    print(f"   Status: {'🟢 ACTIVE' if is_active else '🔴 INACTIVE'}")
                    
                    if is_active:
                        # Check for recent fixtures
                        date_from = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')
                        date_to = datetime.now().strftime('%Y-%m-%d')
                        
                        recent_fixtures = client.get_fixtures(
                            league_id=league_id, 
                            season=current_season, 
                            date_from=date_from, 
                            date_to=date_to
                        )
                        
                        print(f"   Recent matches (14 days): {len(recent_fixtures)}")
                        
                        # Check for upcoming fixtures
                        date_from_future = datetime.now().strftime('%Y-%m-%d')
                        date_to_future = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
                        
                        upcoming_fixtures = client.get_fixtures(
                            league_id=league_id, 
                            season=current_season, 
                            date_from=date_from_future, 
                            date_to=date_to_future
                        )
                        
                        print(f"   Upcoming matches (14 days): {len(upcoming_fixtures)}")
                        
                        # Check teams
                        teams = client.get_teams(league_id, current_season)
                        print(f"   Teams: {len(teams)}")
                        
                        # If we have data, add to active leagues
                        if len(recent_fixtures) > 0 or len(upcoming_fixtures) > 0 or len(teams) > 0:
                            active_leagues.append({
                                'id': league_id,
                                'name': league_name,
                                'country': country,
                                'season': current_season,
                                'recent_matches': len(recent_fixtures),
                                'upcoming_matches': len(upcoming_fixtures),
                                'teams': len(teams),
                                'is_season_active': is_active
                            })
                            print(f"   🎯 DATA AVAILABLE: YES")
                            
                            # Show sample match if available
                            if recent_fixtures:
                                fixture = recent_fixtures[0]
                                fixture_info = fixture.get('fixture', {})
                                teams_info = fixture.get('teams', {})
                                goals = fixture.get('goals', {})
                                
                                home_team = teams_info.get('home', {}).get('name', 'Home')
                                away_team = teams_info.get('away', {}).get('name', 'Away')
                                match_date = fixture_info.get('date', '')[:10]
                                status = fixture_info.get('status', {}).get('short', 'Unknown')
                                
                                print(f"   📅 Latest: {match_date} - {home_team} vs {away_team} ({status})")
                                if goals.get('home') is not None:
                                    print(f"      Score: {goals['home']}-{goals['away']}")
                        else:
                            print(f"   🎯 DATA AVAILABLE: NO")
                    else:
                        print(f"   🎯 SEASON INACTIVE")
                        
                else:
                    print(f"❌ No current season found")
                    
            else:
                print(f"❌ League not found in API")
                
        except Exception as e:
            print(f"❌ Error checking league: {e}")
    
    # Also check for any leagues with recent activity regardless of season status
    print(f"\n🔍 CHECKING FOR ANY RECENT ACTIVITY...")
    print("-" * 50)
    
    try:
        # Get recent fixtures from any league
        date_from = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        date_to = datetime.now().strftime('%Y-%m-%d')
        
        all_recent_fixtures = client.get_fixtures(date_from=date_from, date_to=date_to)
        print(f"✅ Found {len(all_recent_fixtures)} recent fixtures across all leagues")
        
        if all_recent_fixtures:
            # Group by league
            leagues_with_activity = {}
            for fixture in all_recent_fixtures[:20]:  # Check first 20
                league_info = fixture.get('league', {})
                league_id = league_info.get('id')
                league_name = league_info.get('name', 'Unknown League')
                
                if league_id not in leagues_with_activity:
                    leagues_with_activity[league_id] = {
                        'name': league_name,
                        'count': 0
                    }
                leagues_with_activity[league_id]['count'] += 1
            
            print(f"\n📋 LEAGUES WITH RECENT ACTIVITY:")
            for league_id, info in leagues_with_activity.items():
                print(f"   - {info['name']} (ID: {league_id}): {info['count']} matches")
                
                # Add to active leagues if not already there
                if not any(l['id'] == league_id for l in active_leagues):
                    active_leagues.append({
                        'id': league_id,
                        'name': info['name'],
                        'country': 'Unknown',
                        'season': 'Unknown',
                        'recent_matches': info['count'],
                        'upcoming_matches': 0,
                        'teams': 0,
                        'is_season_active': True
                    })
    
    except Exception as e:
        print(f"❌ Error checking recent activity: {e}")
    
    # Summary
    print(f"\n🏆 ACTIVE LEAGUES SUMMARY")
    print("="*70)
    print(f"Total leagues checked: {len(summer_leagues)}")
    print(f"Leagues with data: {len(active_leagues)}")
    
    if active_leagues:
        print(f"\n📋 LEAGUES WITH AVAILABLE DATA:")
        print("-" * 50)
        for league in active_leagues:
            status_icon = "🟢" if league['is_season_active'] else "🟡"
            print(f"{status_icon} {league['name']} ({league['country']}) - Season {league['season']}")
            print(f"   ID: {league['id']}")
            print(f"   Teams: {league['teams']}, Recent: {league['recent_matches']}, Upcoming: {league['upcoming_matches']}")
            print()
        
        print(f"🎯 BEST LEAGUES FOR IMMEDIATE DATA COLLECTION:")
        print("-" * 50)
        
        # Sort by data availability
        best_leagues = sorted(active_leagues, 
                            key=lambda x: (x['recent_matches'] + x['upcoming_matches'], x['teams']), 
                            reverse=True)
        
        for i, league in enumerate(best_leagues[:3], 1):
            total_matches = league['recent_matches'] + league['upcoming_matches']
            print(f"{i}. {league['name']} ({league['country']})")
            print(f"   ID: {league['id']}, Season: {league['season']}")
            print(f"   Total matches: {total_matches}, Teams: {league['teams']}")
            print()
    
    else:
        print("❌ No leagues with available data found")
        print("\n💡 SUGGESTIONS:")
        print("- European leagues start in August 2025")
        print("- Try checking in August for Premier League, La Liga, etc.")
        print("- Look for international tournaments or friendlies")
        print("- Check MLS, Brazilian, or other summer leagues")
    
    return active_leagues


if __name__ == "__main__":
    active_leagues = find_active_leagues_july()
    
    if active_leagues:
        print("🚀 READY TO COLLECT DATA FROM ACTIVE LEAGUES!")
        print("Use these league IDs for immediate data collection:")
        for league in active_leagues[:3]:
            print(f"  - {league['name']}: ID {league['id']}, Season {league['season']}")
    else:
        print("⏳ WAITING FOR LEAGUE SEASONS TO START...")
        print("Major European leagues will be active from August 2025")
