#!/usr/bin/env python3
"""Comprehensive model evaluation and selection script."""

import sys
import os
import argparse
import logging
from pathlib import Path
import pandas as pd
import joblib

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.evaluation import ModelEvaluator, ModelSelector
from src.models.training import ModelTrainer
from src.features import FeatureEngineeringPipeline
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/model_evaluation.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Evaluate and select football prediction models')
    
    parser.add_argument(
        '--models-dir',
        type=str,
        default='data/models',
        help='Directory containing trained models'
    )
    
    parser.add_argument(
        '--data-file',
        type=str,
        help='Path to test data file'
    )
    
    parser.add_argument(
        '--selection-criteria',
        choices=['accuracy', 'f1_score', 'weighted_score', 'football_specific'],
        default='weighted_score',
        help='Criteria for model selection'
    )
    
    parser.add_argument(
        '--backtest',
        action='store_true',
        help='Perform backtesting analysis'
    )
    
    parser.add_argument(
        '--backtest-start',
        type=str,
        help='Backtest start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--backtest-end',
        type=str,
        help='Backtest end date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--cross-validate',
        action='store_true',
        help='Perform cross-validation for model selection'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='data/evaluation',
        help='Directory to save evaluation results'
    )
    
    return parser.parse_args()


def load_trained_models(models_dir: str) -> list:
    """Load trained models from directory.
    
    Args:
        models_dir: Directory containing model files
        
    Returns:
        List of loaded models
    """
    logger = logging.getLogger("model_loader")
    models_dir = Path(models_dir)
    
    if not models_dir.exists():
        logger.error(f"Models directory not found: {models_dir}")
        return []
    
    models = []
    model_files = list(models_dir.glob("*.joblib"))
    
    if not model_files:
        logger.warning(f"No model files found in {models_dir}")
        return []
    
    for model_file in model_files:
        try:
            # Load model data
            model_data = joblib.load(model_file)
            
            # Reconstruct model object
            if isinstance(model_data, dict) and 'model' in model_data:
                model = model_data['model']
                # Restore additional attributes
                if hasattr(model, 'model_name'):
                    model.model_name = model_data.get('model_name', model_file.stem)
                if hasattr(model, 'is_trained'):
                    model.is_trained = model_data.get('is_trained', True)
                if hasattr(model, 'feature_names'):
                    model.feature_names = model_data.get('feature_names', [])
                
                models.append(model)
                logger.info(f"Loaded model: {model.model_name}")
            else:
                logger.warning(f"Invalid model format in {model_file}")
                
        except Exception as e:
            logger.error(f"Failed to load model from {model_file}: {e}")
    
    return models


def create_sample_test_data() -> pd.DataFrame:
    """Create sample test data for evaluation.
    
    Returns:
        DataFrame with sample test data
    """
    logger = logging.getLogger("data_creator")
    logger.info("Creating sample test data for evaluation...")
    
    import random
    from datetime import datetime, timedelta
    
    # Create sample matches for testing
    test_matches = []
    base_date = datetime.now() - timedelta(days=60)
    
    for i in range(100):  # 100 test matches
        match_date = base_date + timedelta(days=i % 50)
        home_team = random.randint(1, 10)
        away_team = random.randint(1, 10)
        while away_team == home_team:
            away_team = random.randint(1, 10)
        
        # Generate realistic outcome
        home_strength = random.uniform(0.3, 0.9)
        away_strength = random.uniform(0.3, 0.9)
        home_advantage = 0.1
        
        home_prob = (home_strength + home_advantage) / (home_strength + away_strength + home_advantage)
        
        rand = random.random()
        if rand < 0.45:  # Home win (45%)
            result = 'H'
        elif rand < 0.70:  # Draw (25%)
            result = 'D'
        else:  # Away win (30%)
            result = 'A'
        
        test_matches.append({
            'id': i + 1000,
            'home_team_id': home_team,
            'away_team_id': away_team,
            'match_date': match_date,
            'league_id': 1,
            'season': '2023-24',
            'status': 'FINISHED',
            'result': result
        })
    
    return pd.DataFrame(test_matches)


def main():
    """Main evaluation function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("evaluate_models")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    logger.info("Starting model evaluation and selection...")
    logger.info(f"Configuration: {vars(args)}")
    
    try:
        # Load trained models
        models = load_trained_models(args.models_dir)
        
        if not models:
            logger.info("No trained models found, training sample models for demonstration...")
            
            # Create sample data and train models
            test_data = create_sample_test_data()
            
            # Feature engineering
            feature_pipeline = FeatureEngineeringPipeline()
            test_matches_list = test_data.to_dict('records')
            features_df = feature_pipeline.create_feature_matrix(test_matches_list, include_targets=True)
            
            # Train sample models
            trainer = ModelTrainer()
            data_splits = trainer.prepare_data(features_df, target_column='result')
            training_results = trainer.train_models(data_splits, model_types=['xgboost', 'lightgbm'])
            
            models = list(trainer.models.values())
            
            # Use the same data for evaluation (in real scenario, use separate test set)
            X_test, y_test = data_splits['X_test'], data_splits['y_test']
            X_train, y_train = data_splits['X_train'], data_splits['y_train']
        
        else:
            # Load test data
            if args.data_file:
                test_data = pd.read_csv(args.data_file)
            else:
                test_data = create_sample_test_data()
            
            # Feature engineering for test data
            feature_pipeline = FeatureEngineeringPipeline()
            test_matches_list = test_data.to_dict('records')
            features_df = feature_pipeline.create_feature_matrix(test_matches_list, include_targets=True)
            
            # Prepare test data
            target_col = 'result'
            X_test = features_df.drop(columns=[target_col])
            y_test = features_df[target_col].values
            X_train, y_train = None, None
        
        logger.info(f"Evaluating {len(models)} models on {len(X_test)} test samples")
        
        # Initialize evaluator and selector
        evaluator = ModelEvaluator()
        selector = ModelSelector()
        
        # Evaluate all models
        logger.info("Evaluating individual models...")
        evaluation_results = []
        
        for model in models:
            try:
                result = evaluator.evaluate_model(model, X_test, y_test, X_train, y_train)
                evaluation_results.append(result)
                logger.info(f"Evaluated {model.model_name}: Accuracy = {result['basic_metrics']['accuracy']:.4f}")
            except Exception as e:
                logger.error(f"Failed to evaluate {model.model_name}: {e}")
        
        if not evaluation_results:
            logger.error("No models could be evaluated")
            return 1
        
        # Model selection
        logger.info(f"Selecting best model using criteria: {args.selection_criteria}")
        selection_result = selector.select_best_model(
            models, X_test, y_test, X_train, y_train, args.selection_criteria
        )
        
        # Display results
        print("\n" + "="*80)
        print("MODEL EVALUATION AND SELECTION RESULTS")
        print("="*80)
        
        # Model comparison
        comparison_df = evaluator.compare_models(evaluation_results)
        if not comparison_df.empty:
            print("\nModel Performance Comparison:")
            print("-" * 50)
            print(comparison_df.to_string(index=False, float_format='%.4f'))
        
        # Selection results
        print(f"\nSelected Model: {selection_result['best_model'].model_name}")
        print(f"Selection Criteria: {args.selection_criteria}")
        
        best_metrics = selection_result['best_model_info']['basic_metrics']
        print(f"\nBest Model Performance:")
        for metric, value in best_metrics.items():
            print(f"  {metric}: {value:.4f}")
        
        # Football-specific metrics
        football_metrics = selection_result['best_model_info'].get('football_metrics', {})
        if football_metrics:
            print(f"\nFootball-Specific Metrics:")
            print(f"  Home Win Accuracy: {football_metrics.get('home_win_accuracy', 0):.4f}")
            print(f"  Draw Accuracy: {football_metrics.get('draw_accuracy', 0):.4f}")
            print(f"  Away Win Accuracy: {football_metrics.get('away_win_accuracy', 0):.4f}")
            
            betting_sim = football_metrics.get('betting_simulation', {})
            if betting_sim:
                print(f"  Betting ROI: {betting_sim.get('roi', 0):.4f}")
                print(f"  Betting Win Rate: {betting_sim.get('win_rate', 0):.4f}")
        
        # Cross-validation if requested
        if args.cross_validate and X_train is not None:
            logger.info("Performing cross-validation for model selection...")
            cv_results = selector.cross_validate_selection(models, 
                                                         pd.concat([X_train, X_test]), 
                                                         np.concatenate([y_train, y_test]))
            
            print(f"\nCross-Validation Results:")
            print(f"Overall Winner: {cv_results['overall_winner']}")
            print(f"CV Summary:")
            for model_name, summary in cv_results['cv_summary'].items():
                print(f"  {model_name}: {summary['mean_accuracy']:.4f} ± {summary['std_accuracy']:.4f}")
        
        # Backtesting if requested
        if args.backtest and args.backtest_start and args.backtest_end:
            logger.info("Performing backtesting analysis...")
            
            # Create historical data for backtesting (in real scenario, use actual historical data)
            historical_data = create_sample_test_data()  # This should be replaced with real historical data
            
            best_model = selection_result['best_model']
            backtest_results = evaluator.backtest_model(
                best_model, historical_data, 
                args.backtest_start, args.backtest_end
            )
            
            if 'error' not in backtest_results:
                print(f"\nBacktesting Results ({args.backtest_start} to {args.backtest_end}):")
                overall_metrics = backtest_results.get('overall_metrics', {})
                print(f"  Overall Accuracy: {overall_metrics.get('accuracy', 0):.4f}")
                print(f"  Total Predictions: {overall_metrics.get('total_predictions', 0)}")
                print(f"  Intervals Tested: {len(backtest_results.get('intervals', []))}")
        
        # Save results
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save evaluation results
        import json
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = output_dir / f"evaluation_results_{timestamp}.json"
        
        # Make results JSON serializable
        serializable_results = {
            'evaluation_results': evaluation_results,
            'selection_result': {
                'best_model_name': selection_result['best_model'].model_name,
                'best_model_info': selection_result['best_model_info'],
                'selection_criteria': selection_result['selection_criteria']
            },
            'evaluation_date': datetime.now().isoformat()
        }
        
        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Evaluation results saved to {results_file}")
        
        print(f"\n✅ Model evaluation completed successfully!")
        print(f"📊 Results saved to: {results_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
