#!/usr/bin/env python3
"""Export detailed predictions to CSV with comprehensive match data."""

import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.prediction.detailed_exporter import DetailedPredictionExporter
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/detailed_export.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Export detailed football predictions to CSV')
    
    parser.add_argument(
        '--start-date',
        type=str,
        required=True,
        help='Start date for export (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        required=True,
        help='End date for export (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--output-file',
        type=str,
        help='Output CSV file path (default: data/exports/detailed_predictions_YYYYMMDD.csv)'
    )
    
    parser.add_argument(
        '--include-upcoming',
        action='store_true',
        help='Include upcoming matches with predictions'
    )
    
    parser.add_argument(
        '--leagues',
        nargs='+',
        help='Specific leagues to export (e.g., premier_league la_liga)'
    )
    
    parser.add_argument(
        '--finished-only',
        action='store_true',
        help='Export only finished matches (overrides --include-upcoming)'
    )
    
    parser.add_argument(
        '--with-predictions-only',
        action='store_true',
        help='Export only matches that have predictions'
    )
    
    return parser.parse_args()


def validate_dates(start_date_str: str, end_date_str: str) -> tuple:
    """Validate and parse date arguments."""
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        
        if start_date > end_date:
            raise ValueError("Start date must be before end date")
        
        if end_date > datetime.now() + timedelta(days=30):
            raise ValueError("End date cannot be more than 30 days in the future")
        
        return start_date, end_date
        
    except ValueError as e:
        raise ValueError(f"Invalid date format or range: {e}")


def generate_output_filename(start_date: datetime, end_date: datetime) -> str:
    """Generate default output filename."""
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')
    timestamp = datetime.now().strftime('%H%M%S')
    
    return f"data/exports/detailed_predictions_{start_str}_to_{end_str}_{timestamp}.csv"


def main():
    """Main export function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("export_detailed_predictions")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    logger.info("Starting detailed predictions export...")
    logger.info(f"Configuration: {vars(args)}")
    
    try:
        # Validate dates
        start_date, end_date = validate_dates(args.start_date, args.end_date)
        
        # Generate output filename if not provided
        output_file = args.output_file or generate_output_filename(start_date, end_date)
        
        # Determine include_upcoming flag
        include_upcoming = args.include_upcoming and not args.finished_only
        
        logger.info(f"Export period: {start_date.date()} to {end_date.date()}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"Include upcoming: {include_upcoming}")
        
        # Initialize exporter
        exporter = DetailedPredictionExporter()
        
        # Perform export
        print("\n" + "="*80)
        print("🏈 DETAILED FOOTBALL PREDICTIONS EXPORT")
        print("="*80)
        print(f"📅 Period: {start_date.date()} to {end_date.date()}")
        print(f"📁 Output: {output_file}")
        print(f"🔮 Include Upcoming: {'Yes' if include_upcoming else 'No'}")
        print("="*80)
        print("🔄 Processing matches and generating predictions...")
        
        # Export detailed predictions
        summary = exporter.export_detailed_predictions(
            start_date=start_date,
            end_date=end_date,
            output_file=output_file,
            include_upcoming=include_upcoming
        )
        
        if 'error' in summary:
            print(f"❌ Export failed: {summary['error']}")
            return 1
        
        # Display results
        print("\n✅ Export completed successfully!")
        print("\n📊 EXPORT SUMMARY:")
        print("-" * 40)
        print(f"📁 File: {summary['export_file']}")
        print(f"🏆 Total Matches: {summary['total_matches']}")
        print(f"✅ Finished Matches: {summary['finished_matches']}")
        print(f"🔮 Upcoming Matches: {summary['upcoming_matches']}")
        print(f"📋 Columns Exported: {summary['columns_exported']}")
        print(f"⏰ Export Date: {summary['export_date']}")
        
        # Show column information
        print("\n📋 EXPORTED COLUMNS INCLUDE:")
        print("-" * 40)
        print("🏈 Match Information:")
        print("  • Match ID, Date, League, Teams, Venue, Referee")
        print("  • Match Status, Actual Results")
        
        print("\n⚽ Scoring Details:")
        print("  • Full-time Scores (Home/Away)")
        print("  • Half-time Scores (Home/Away)")
        print("  • Total Goals, Goal Difference")
        print("  • Both Teams Scored, Clean Sheets")
        
        print("\n🔮 Predictions:")
        print("  • Predicted Result (H/D/A)")
        print("  • Win/Draw/Loss Probabilities")
        print("  • Prediction Confidence & Level")
        print("  • Prediction Accuracy (if match finished)")
        
        print("\n📊 Match Statistics:")
        print("  • Possession, Shots, Shots on Target")
        print("  • Corners, Fouls, Offsides")
        
        print("\n🟨🟥 Cards & Events:")
        print("  • Yellow Cards (Home/Away)")
        print("  • Red Cards (Home/Away)")
        print("  • Total Cards")
        print("  • Substitutions")
        
        print("\n📈 Team Performance:")
        print("  • Form Scores, Injury Impact")
        print("  • Head-to-Head Statistics")
        print("  • Season Statistics (Wins, Draws, Losses)")
        print("  • Goals For/Against, Points, Win Rate")
        
        print("\n🎯 Analysis Factors:")
        print("  • Form Advantage")
        print("  • Injury Advantage")
        print("  • Head-to-Head Advantage")
        
        print("="*80)
        print(f"📁 Your detailed predictions CSV is ready: {summary['export_file']}")
        print("="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        print(f"❌ Export failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
