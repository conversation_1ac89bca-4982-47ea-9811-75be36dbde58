#!/usr/bin/env python3
"""Check which leagues are currently active and have ongoing data."""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.collectors.api_football_client import APIFootballClient


def check_active_leagues():
    """Check which leagues are currently active with ongoing matches."""
    
    print("🏈 CHECKING ACTIVE LEAGUES WITH ONGOING DATA")
    print("="*70)
    
    client = APIFootballClient()
    
    # Major leagues to check
    leagues_to_check = [
        {'id': 39, 'name': 'Premier League', 'country': 'England'},
        {'id': 140, 'name': 'La Liga', 'country': 'Spain'},
        {'id': 78, 'name': 'Bundesliga', 'country': 'Germany'},
        {'id': 135, 'name': 'Serie A', 'country': 'Italy'},
        {'id': 61, 'name': 'Ligue 1', 'country': 'France'},
        {'id': 2, 'name': 'Champions League', 'country': 'Europe'},
        {'id': 3, 'name': 'Europa League', 'country': 'Europe'},
        {'id': 88, 'name': 'Eredivisie', 'country': 'Netherlands'},
        {'id': 94, 'name': 'Primeira Liga', 'country': 'Portugal'},
        {'id': 203, 'name': 'Süper Lig', 'country': 'Turkey'},
        {'id': 144, 'name': 'Jupiler Pro League', 'country': 'Belgium'},
        {'id': 179, 'name': 'MLS', 'country': 'USA'},
        {'id': 71, 'name': 'Serie A', 'country': 'Brazil'},
        {'id': 128, 'name': 'Liga Profesional', 'country': 'Argentina'},
    ]
    
    active_leagues = []
    
    print("🔍 CHECKING LEAGUE STATUS AND CURRENT SEASON DATA...")
    print("-" * 70)
    
    for league_info in leagues_to_check:
        league_id = league_info['id']
        league_name = league_info['name']
        country = league_info['country']
        
        print(f"\n📊 {league_name} ({country}) - ID: {league_id}")
        print("-" * 50)
        
        try:
            # Get league information with seasons
            response = client._make_request('/leagues', {'id': league_id})
            
            if response.get('response') and len(response['response']) > 0:
                league_data = response['response'][0]
                seasons = league_data.get('seasons', [])
                
                # Find current season
                current_season = None
                current_season_info = None
                for season in seasons:
                    if season.get('current'):
                        current_season = season.get('year')
                        current_season_info = season
                        break
                
                if current_season:
                    print(f"✅ Current Season: {current_season}")
                    print(f"   Start: {current_season_info.get('start', 'Unknown')}")
                    print(f"   End: {current_season_info.get('end', 'Unknown')}")
                    
                    # Check if season is currently active
                    season_start = current_season_info.get('start')
                    season_end = current_season_info.get('end')
                    now = datetime.now().strftime('%Y-%m-%d')
                    
                    is_active = False
                    if season_start and season_end:
                        is_active = season_start <= now <= season_end
                    
                    print(f"   Status: {'🟢 ACTIVE' if is_active else '🔴 INACTIVE'}")
                    
                    # Check for recent fixtures (last 30 days)
                    date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                    date_to = datetime.now().strftime('%Y-%m-%d')
                    
                    recent_fixtures = client.get_fixtures(
                        league_id=league_id, 
                        season=current_season, 
                        date_from=date_from, 
                        date_to=date_to
                    )
                    
                    print(f"   Recent matches (30 days): {len(recent_fixtures)}")
                    
                    # Check for upcoming fixtures (next 30 days)
                    date_from_future = datetime.now().strftime('%Y-%m-%d')
                    date_to_future = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
                    
                    upcoming_fixtures = client.get_fixtures(
                        league_id=league_id, 
                        season=current_season, 
                        date_from=date_from_future, 
                        date_to=date_to_future
                    )
                    
                    print(f"   Upcoming matches (30 days): {len(upcoming_fixtures)}")
                    
                    # Check teams
                    teams = client.get_teams(league_id, current_season)
                    print(f"   Teams: {len(teams)}")
                    
                    # Check standings
                    try:
                        standings = client.get_standings(league_id, current_season)
                        standings_count = 0
                        if standings and len(standings) > 0:
                            league_standings = standings[0].get('league', {}).get('standings', [[]])
                            if league_standings and len(league_standings[0]) > 0:
                                standings_count = len(league_standings[0])
                        print(f"   Standings: {standings_count} teams")
                    except:
                        print(f"   Standings: Not available")
                    
                    # Determine if league has good data
                    has_data = (len(recent_fixtures) > 0 or len(upcoming_fixtures) > 0) and len(teams) > 0
                    
                    if has_data:
                        active_leagues.append({
                            'id': league_id,
                            'name': league_name,
                            'country': country,
                            'season': current_season,
                            'recent_matches': len(recent_fixtures),
                            'upcoming_matches': len(upcoming_fixtures),
                            'teams': len(teams),
                            'is_season_active': is_active
                        })
                        print(f"   🎯 DATA AVAILABLE: {'YES' if has_data else 'NO'}")
                    else:
                        print(f"   🎯 DATA AVAILABLE: NO")
                        
                    # Show sample recent match if available
                    if recent_fixtures:
                        fixture = recent_fixtures[0]
                        fixture_info = fixture.get('fixture', {})
                        teams_info = fixture.get('teams', {})
                        goals = fixture.get('goals', {})
                        
                        home_team = teams_info.get('home', {}).get('name', 'Home')
                        away_team = teams_info.get('away', {}).get('name', 'Away')
                        match_date = fixture_info.get('date', '')[:10]
                        status = fixture_info.get('status', {}).get('short', 'Unknown')
                        
                        print(f"   📅 Latest match: {match_date} - {home_team} vs {away_team} ({status})")
                        if goals.get('home') is not None:
                            print(f"      Score: {goals['home']}-{goals['away']}")
                
                else:
                    print(f"❌ No current season found")
                    
            else:
                print(f"❌ League not found in API")
                
        except Exception as e:
            print(f"❌ Error checking league: {e}")
    
    # Summary
    print(f"\n🏆 ACTIVE LEAGUES SUMMARY")
    print("="*70)
    print(f"Total leagues checked: {len(leagues_to_check)}")
    print(f"Leagues with data: {len(active_leagues)}")
    
    if active_leagues:
        print(f"\n📋 LEAGUES WITH AVAILABLE DATA:")
        print("-" * 50)
        for league in active_leagues:
            status_icon = "🟢" if league['is_season_active'] else "🟡"
            print(f"{status_icon} {league['name']} ({league['country']}) - Season {league['season']}")
            print(f"   Teams: {league['teams']}, Recent: {league['recent_matches']}, Upcoming: {league['upcoming_matches']}")
        
        print(f"\n🎯 RECOMMENDED LEAGUES FOR DATA COLLECTION:")
        print("-" * 50)
        
        # Sort by data availability
        best_leagues = sorted(active_leagues, 
                            key=lambda x: (x['recent_matches'] + x['upcoming_matches'], x['teams']), 
                            reverse=True)
        
        for i, league in enumerate(best_leagues[:5], 1):
            total_matches = league['recent_matches'] + league['upcoming_matches']
            print(f"{i}. {league['name']} ({league['country']})")
            print(f"   ID: {league['id']}, Season: {league['season']}")
            print(f"   Total matches: {total_matches}, Teams: {league['teams']}")
            print()
    
    else:
        print("❌ No leagues with available data found")
    
    return active_leagues


if __name__ == "__main__":
    active_leagues = check_active_leagues()
    
    print("🚀 NEXT STEPS:")
    print("-" * 30)
    if active_leagues:
        print("1. Use the leagues with available data for predictions")
        print("2. Update config.yaml with correct seasons")
        print("3. Run data collection for active leagues")
        print("4. Train models with real data")
    else:
        print("1. Check API key permissions")
        print("2. Try different date ranges")
        print("3. Check league IDs and seasons")
