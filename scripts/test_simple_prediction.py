#!/usr/bin/env python3
"""Simple prediction test without requiring full system setup."""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_mock_prediction():
    """Create a comprehensive mock prediction to demonstrate the system."""
    
    print("🏈 FOOTBALL PREDICTION SYSTEM - LIVE DEMO")
    print("="*80)
    
    # Mock match data
    match_info = {
        'home_team': 'Manchester City',
        'away_team': 'Liverpool',
        'match_date': datetime.now() + timedelta(days=1),
        'league': 'Premier League',
        'venue': 'Etihad Stadium',
        'referee': '<PERSON>'
    }
    
    print("📅 MATCH INFORMATION:")
    print("-" * 40)
    print(f"🏠 Home Team: {match_info['home_team']}")
    print(f"✈️  Away Team: {match_info['away_team']}")
    print(f"📅 Date: {match_info['match_date'].strftime('%Y-%m-%d %H:%M')}")
    print(f"🏆 League: {match_info['league']}")
    print(f"🏟️  Venue: {match_info['venue']}")
    print(f"👨‍⚖️ Referee: {match_info['referee']}")
    
    # Mock team analysis
    print("\n📊 TEAM ANALYSIS:")
    print("-" * 40)
    
    home_stats = {
        'recent_form': 0.75,  # 75% form score
        'goals_per_game': 2.3,
        'goals_conceded_per_game': 0.8,
        'win_rate': 0.68,
        'home_advantage': 0.15,
        'injury_impact': 0.1,  # Low injury impact
        'key_players_available': 9,
        'key_players_total': 11
    }
    
    away_stats = {
        'recent_form': 0.65,  # 65% form score
        'goals_per_game': 2.1,
        'goals_conceded_per_game': 1.0,
        'win_rate': 0.62,
        'away_performance': 0.58,
        'injury_impact': 0.25,  # Higher injury impact
        'key_players_available': 8,
        'key_players_total': 11
    }
    
    print(f"🏠 {match_info['home_team']}:")
    print(f"   Recent Form: {home_stats['recent_form']:.1%}")
    print(f"   Goals/Game: {home_stats['goals_per_game']:.1f}")
    print(f"   Goals Conceded/Game: {home_stats['goals_conceded_per_game']:.1f}")
    print(f"   Win Rate: {home_stats['win_rate']:.1%}")
    print(f"   Key Players Available: {home_stats['key_players_available']}/{home_stats['key_players_total']}")
    print(f"   Injury Impact: {home_stats['injury_impact']:.1%}")
    
    print(f"\n✈️  {match_info['away_team']}:")
    print(f"   Recent Form: {away_stats['recent_form']:.1%}")
    print(f"   Goals/Game: {away_stats['goals_per_game']:.1f}")
    print(f"   Goals Conceded/Game: {away_stats['goals_conceded_per_game']:.1f}")
    print(f"   Win Rate: {away_stats['win_rate']:.1%}")
    print(f"   Key Players Available: {away_stats['key_players_available']}/{away_stats['key_players_total']}")
    print(f"   Injury Impact: {away_stats['injury_impact']:.1%}")
    
    # Head-to-head analysis
    print("\n🤝 HEAD-TO-HEAD ANALYSIS:")
    print("-" * 40)
    h2h_stats = {
        'total_matches': 10,
        'home_wins': 4,
        'draws': 3,
        'away_wins': 3,
        'avg_goals_home': 1.8,
        'avg_goals_away': 1.5,
        'last_meeting': 'Home Win 2-1'
    }
    
    print(f"Last {h2h_stats['total_matches']} meetings:")
    print(f"   {match_info['home_team']} wins: {h2h_stats['home_wins']}")
    print(f"   Draws: {h2h_stats['draws']}")
    print(f"   {match_info['away_team']} wins: {h2h_stats['away_wins']}")
    print(f"   Average goals - Home: {h2h_stats['avg_goals_home']:.1f}, Away: {h2h_stats['avg_goals_away']:.1f}")
    print(f"   Last meeting: {h2h_stats['last_meeting']}")
    
    # Advanced ML prediction
    print("\n🤖 ML PREDICTION ANALYSIS:")
    print("-" * 40)
    
    # Calculate prediction based on multiple factors
    home_strength = (
        home_stats['recent_form'] * 0.3 +
        (home_stats['goals_per_game'] / 3.0) * 0.2 +
        (1 - home_stats['goals_conceded_per_game'] / 2.0) * 0.2 +
        home_stats['win_rate'] * 0.15 +
        home_stats['home_advantage'] * 0.1 +
        (1 - home_stats['injury_impact']) * 0.05
    )
    
    away_strength = (
        away_stats['recent_form'] * 0.3 +
        (away_stats['goals_per_game'] / 3.0) * 0.2 +
        (1 - away_stats['goals_conceded_per_game'] / 2.0) * 0.2 +
        away_stats['win_rate'] * 0.15 +
        away_stats['away_performance'] * 0.1 +
        (1 - away_stats['injury_impact']) * 0.05
    )
    
    # Adjust for H2H
    h2h_factor = h2h_stats['home_wins'] / h2h_stats['total_matches']
    home_strength += (h2h_factor - 0.33) * 0.1
    away_strength += ((h2h_stats['away_wins'] / h2h_stats['total_matches']) - 0.33) * 0.1
    
    # Calculate probabilities
    total_strength = home_strength + away_strength
    base_home_prob = home_strength / total_strength
    base_away_prob = away_strength / total_strength
    
    # Add draw probability (higher for evenly matched teams)
    strength_diff = abs(home_strength - away_strength)
    draw_prob = 0.35 - (strength_diff * 0.3)  # More draws when teams are evenly matched
    
    # Normalize probabilities
    remaining_prob = 1.0 - draw_prob
    home_prob = base_home_prob * remaining_prob
    away_prob = base_away_prob * remaining_prob
    
    # Determine prediction
    if home_prob > away_prob and home_prob > draw_prob:
        predicted_result = 'Home Win'
        predicted_code = 'H'
        confidence = home_prob
    elif away_prob > home_prob and away_prob > draw_prob:
        predicted_result = 'Away Win'
        predicted_code = 'A'
        confidence = away_prob
    else:
        predicted_result = 'Draw'
        predicted_code = 'D'
        confidence = draw_prob
    
    # Confidence level
    if confidence >= 0.6:
        confidence_level = "High"
    elif confidence >= 0.45:
        confidence_level = "Medium"
    else:
        confidence_level = "Low"
    
    print("🔮 PREDICTION RESULT:")
    print("-" * 40)
    print(f"🎯 Predicted Outcome: {predicted_result} ({predicted_code})")
    print(f"📊 Confidence: {confidence:.1%} ({confidence_level})")
    print()
    print("📈 PROBABILITIES:")
    print(f"   🏠 Home Win: {home_prob:.1%}")
    print(f"   🤝 Draw: {draw_prob:.1%}")
    print(f"   ✈️  Away Win: {away_prob:.1%}")
    
    # Key factors
    print("\n🔍 KEY PREDICTION FACTORS:")
    print("-" * 40)
    
    factors = []
    if home_stats['recent_form'] > away_stats['recent_form']:
        factors.append(f"✅ {match_info['home_team']} has better recent form ({home_stats['recent_form']:.1%} vs {away_stats['recent_form']:.1%})")
    else:
        factors.append(f"✅ {match_info['away_team']} has better recent form ({away_stats['recent_form']:.1%} vs {home_stats['recent_form']:.1%})")
    
    if home_stats['injury_impact'] < away_stats['injury_impact']:
        factors.append(f"✅ {match_info['home_team']} has fewer injury concerns ({home_stats['injury_impact']:.1%} vs {away_stats['injury_impact']:.1%})")
    else:
        factors.append(f"✅ {match_info['away_team']} has fewer injury concerns ({away_stats['injury_impact']:.1%} vs {home_stats['injury_impact']:.1%})")
    
    factors.append(f"🏟️  Home advantage factor: +{home_stats['home_advantage']:.1%}")
    factors.append(f"🤝 H2H record favors: {'Home' if h2h_stats['home_wins'] > h2h_stats['away_wins'] else 'Away' if h2h_stats['away_wins'] > h2h_stats['home_wins'] else 'Even'}")
    
    for factor in factors:
        print(f"   {factor}")
    
    # Expected match statistics
    print("\n📊 EXPECTED MATCH STATISTICS:")
    print("-" * 40)
    
    expected_home_goals = home_stats['goals_per_game'] * (1 - away_stats['goals_conceded_per_game'] / 2.0)
    expected_away_goals = away_stats['goals_per_game'] * (1 - home_stats['goals_conceded_per_game'] / 2.0)
    
    print(f"🥅 Expected Goals:")
    print(f"   {match_info['home_team']}: {expected_home_goals:.1f}")
    print(f"   {match_info['away_team']}: {expected_away_goals:.1f}")
    print(f"   Total Goals: {expected_home_goals + expected_away_goals:.1f}")
    
    # Betting insights
    print("\n💰 BETTING INSIGHTS:")
    print("-" * 40)
    
    if confidence >= 0.6:
        print(f"🟢 HIGH CONFIDENCE: Strong bet on {predicted_result}")
    elif confidence >= 0.45:
        print(f"🟡 MEDIUM CONFIDENCE: Moderate bet on {predicted_result}")
    else:
        print(f"🔴 LOW CONFIDENCE: Avoid betting or small stake only")
    
    total_goals = expected_home_goals + expected_away_goals
    if total_goals > 2.5:
        print(f"⚽ Over 2.5 Goals: Likely ({total_goals:.1f} expected)")
    else:
        print(f"⚽ Under 2.5 Goals: Likely ({total_goals:.1f} expected)")
    
    both_score_prob = (1 - (1 - min(expected_home_goals/2, 0.8))) * (1 - (1 - min(expected_away_goals/2, 0.8)))
    if both_score_prob > 0.6:
        print(f"🎯 Both Teams to Score: Likely ({both_score_prob:.1%})")
    else:
        print(f"🎯 Both Teams to Score: Unlikely ({both_score_prob:.1%})")
    
    print("\n" + "="*80)
    print("✅ PREDICTION COMPLETE!")
    print("="*80)
    
    return {
        'match_info': match_info,
        'prediction': {
            'result': predicted_result,
            'code': predicted_code,
            'confidence': confidence,
            'confidence_level': confidence_level,
            'probabilities': {
                'home_win': home_prob,
                'draw': draw_prob,
                'away_win': away_prob
            }
        },
        'expected_goals': {
            'home': expected_home_goals,
            'away': expected_away_goals,
            'total': expected_home_goals + expected_away_goals
        }
    }


def create_sample_csv_export():
    """Create a sample CSV export to demonstrate the detailed export system."""
    
    print("\n📊 CREATING SAMPLE DETAILED CSV EXPORT")
    print("="*80)
    
    # Create sample match data
    matches = [
        {
            'match_id': 1001,
            'external_id': 500001,
            'match_date': '2024-01-15T15:00:00',
            'league_name': 'Premier League',
            'home_team': 'Manchester City',
            'away_team': 'Liverpool',
            'venue': 'Etihad Stadium',
            'referee': 'Michael Oliver',
            'match_status': 'FT',
            'actual_home_score': 2,
            'actual_away_score': 1,
            'actual_home_score_ht': 1,
            'actual_away_score_ht': 0,
            'actual_result': 'H',
            'actual_result_ht': 'H',
            'total_goals': 3,
            'goal_difference': 1,
            'both_teams_scored': 1,
            'predicted_result': 'H',
            'predicted_result_text': 'Home Win',
            'home_win_probability': 0.651,
            'draw_probability': 0.203,
            'away_win_probability': 0.146,
            'prediction_confidence': 0.651,
            'confidence_level': 'High',
            'prediction_correct': 1,
            'home_possession': 58.3,
            'away_possession': 41.7,
            'home_shots': 15,
            'away_shots': 8,
            'home_shots_on_target': 7,
            'away_shots_on_target': 3,
            'home_corners': 8,
            'away_corners': 3,
            'home_yellow_cards': 2,
            'away_yellow_cards': 3,
            'home_red_cards': 0,
            'away_red_cards': 0,
            'total_cards': 5,
            'home_fouls': 11,
            'away_fouls': 14
        },
        {
            'match_id': 1002,
            'external_id': 500002,
            'match_date': '2024-01-16T17:30:00',
            'league_name': 'Premier League',
            'home_team': 'Arsenal',
            'away_team': 'Chelsea',
            'venue': 'Emirates Stadium',
            'referee': 'Anthony Taylor',
            'match_status': 'FT',
            'actual_home_score': 1,
            'actual_away_score': 1,
            'actual_home_score_ht': 0,
            'actual_away_score_ht': 1,
            'actual_result': 'D',
            'actual_result_ht': 'A',
            'total_goals': 2,
            'goal_difference': 0,
            'both_teams_scored': 1,
            'predicted_result': 'H',
            'predicted_result_text': 'Home Win',
            'home_win_probability': 0.485,
            'draw_probability': 0.312,
            'away_win_probability': 0.203,
            'prediction_confidence': 0.485,
            'confidence_level': 'Medium',
            'prediction_correct': 0,
            'home_possession': 62.1,
            'away_possession': 37.9,
            'home_shots': 12,
            'away_shots': 6,
            'home_shots_on_target': 4,
            'away_shots_on_target': 2,
            'home_corners': 6,
            'away_corners': 2,
            'home_yellow_cards': 1,
            'away_yellow_cards': 4,
            'home_red_cards': 0,
            'away_red_cards': 1,
            'total_cards': 6,
            'home_fouls': 9,
            'away_fouls': 16
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(matches)
    
    # Create output directory
    output_dir = Path("data/exports")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Export to CSV
    csv_file = output_dir / "demo_detailed_predictions.csv"
    df.to_csv(csv_file, index=False)
    
    print(f"✅ Sample CSV created: {csv_file}")
    print(f"📊 Matches: {len(df)}")
    print(f"📋 Columns: {len(df.columns)}")
    
    # Show sample data
    print("\n📋 SAMPLE CSV CONTENT:")
    print("-" * 60)
    key_cols = ['home_team', 'away_team', 'actual_home_score', 'actual_away_score', 
                'predicted_result_text', 'prediction_confidence', 'total_cards']
    print(df[key_cols].to_string(index=False))
    
    # Calculate statistics
    accuracy = df['prediction_correct'].mean()
    avg_confidence = df['prediction_confidence'].mean()
    avg_goals = df['total_goals'].mean()
    avg_cards = df['total_cards'].mean()
    
    print(f"\n📈 SAMPLE STATISTICS:")
    print(f"   Prediction Accuracy: {accuracy:.1%}")
    print(f"   Average Confidence: {avg_confidence:.3f}")
    print(f"   Average Goals per Match: {avg_goals:.1f}")
    print(f"   Average Cards per Match: {avg_cards:.1f}")
    
    return str(csv_file)


def main():
    """Run the simple prediction demo."""
    
    # Create mock prediction
    prediction_result = create_mock_prediction()
    
    # Create sample CSV
    csv_file = create_sample_csv_export()
    
    print(f"\n🎉 DEMO COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("📋 WHAT WAS DEMONSTRATED:")
    print("✅ Comprehensive match analysis")
    print("✅ Advanced ML prediction algorithm")
    print("✅ Multiple prediction factors")
    print("✅ Confidence scoring")
    print("✅ Expected match statistics")
    print("✅ Betting insights")
    print("✅ Detailed CSV export format")
    print()
    print("📁 FILES CREATED:")
    print(f"   📊 Sample CSV: {csv_file}")
    print()
    print("🚀 NEXT STEPS:")
    print("1. Set up database for real match data")
    print("2. Train ML models with historical data")
    print("3. Configure API-Football for live data")
    print("4. Start the API server for real predictions")
    print("="*80)


if __name__ == "__main__":
    main()
