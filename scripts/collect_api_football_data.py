#!/usr/bin/env python3
"""Comprehensive data collection script using API-Football."""

import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.api_football_orchestrator import APIFootballOrchestrator
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/api_football_collection.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect football data using API-Football')
    
    parser.add_argument(
        '--leagues',
        nargs='+',
        help='Specific leagues to collect (e.g., premier_league la_liga)'
    )
    
    parser.add_argument(
        '--collection-type',
        choices=['full', 'recent', 'upcoming', 'injuries', 'match-details'],
        default='recent',
        help='Type of data collection to perform'
    )
    
    parser.add_argument(
        '--days-back',
        type=int,
        default=30,
        help='Number of days back to collect historical data'
    )
    
    parser.add_argument(
        '--days-ahead',
        type=int,
        default=7,
        help='Number of days ahead to collect upcoming matches'
    )
    
    parser.add_argument(
        '--fixture-id',
        type=int,
        help='Specific fixture ID for match details collection'
    )
    
    parser.add_argument(
        '--include-historical',
        action='store_true',
        help='Include historical data in collection'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without saving to database'
    )
    
    parser.add_argument(
        '--check-api-status',
        action='store_true',
        help='Check API status and usage before collection'
    )
    
    return parser.parse_args()


def check_api_configuration():
    """Check if API-Football is properly configured."""
    logger = logging.getLogger("config_check")
    
    data_sources = config._config.get('data_sources', {})
    api_football_config = data_sources.get('api_football', {})
    
    if not api_football_config.get('enabled', False):
        logger.error("API-Football is not enabled in configuration")
        return False
    
    if not api_football_config.get('api_key'):
        logger.error("API-Football API key is not configured")
        return False
    
    logger.info("API-Football configuration is valid")
    return True


def display_api_status(orchestrator: APIFootballOrchestrator):
    """Display API status and usage information."""
    logger = logging.getLogger("api_status")
    
    try:
        status_info = orchestrator.get_api_status()
        
        if 'error' in status_info:
            logger.error(f"Failed to get API status: {status_info['error']}")
            return
        
        print("\n" + "="*60)
        print("API-FOOTBALL STATUS")
        print("="*60)
        
        requests_remaining = status_info.get('requests_remaining', 0)
        requests_made = status_info.get('requests_made_today', 0)
        
        print(f"Requests made today: {requests_made}")
        print(f"Requests remaining: {requests_remaining}")
        
        if requests_remaining < 10:
            print("⚠️  WARNING: Low API requests remaining!")
        elif requests_remaining < 50:
            print("⚠️  CAUTION: API requests running low")
        else:
            print("✅ API requests available")
        
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error checking API status: {e}")


def collect_full_data(orchestrator: APIFootballOrchestrator, args):
    """Collect comprehensive data for all configured leagues."""
    logger = logging.getLogger("full_collection")
    
    logger.info("Starting full data collection...")
    
    results = orchestrator.collect_all_data(
        leagues=args.leagues,
        include_historical=args.include_historical,
        days_back=args.days_back
    )
    
    print("\n" + "="*60)
    print("FULL DATA COLLECTION RESULTS")
    print("="*60)
    print(f"Leagues collected: {results['leagues_collected']}")
    print(f"Teams collected: {results['teams_collected']}")
    print(f"Matches collected: {results['matches_collected']}")
    print(f"Players collected: {results['players_collected']}")
    print(f"Injuries collected: {results['injuries_collected']}")
    print(f"Statistics collected: {results['statistics_collected']}")
    
    if results['errors']:
        print(f"\nErrors encountered: {len(results['errors'])}")
        for error in results['errors']:
            print(f"  - {error}")
    
    print("="*60)


def collect_recent_data(orchestrator: APIFootballOrchestrator, args):
    """Collect recent match data."""
    logger = logging.getLogger("recent_collection")
    
    logger.info(f"Collecting recent data for last {args.days_back} days...")
    
    results = orchestrator.collect_all_data(
        leagues=args.leagues,
        include_historical=True,
        days_back=args.days_back
    )
    
    print("\n" + "="*60)
    print("RECENT DATA COLLECTION RESULTS")
    print("="*60)
    print(f"Leagues processed: {results['leagues_collected']}")
    print(f"Recent matches collected: {results['matches_collected']}")
    print(f"Match statistics collected: {results['statistics_collected']}")
    
    if results['errors']:
        print(f"\nErrors: {len(results['errors'])}")
        for error in results['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
    
    print("="*60)


def collect_upcoming_matches(orchestrator: APIFootballOrchestrator, args):
    """Collect upcoming matches for prediction."""
    logger = logging.getLogger("upcoming_collection")
    
    logger.info(f"Collecting upcoming matches for next {args.days_ahead} days...")
    
    results = orchestrator.collect_upcoming_matches(days_ahead=args.days_ahead)
    
    print("\n" + "="*60)
    print("UPCOMING MATCHES COLLECTION RESULTS")
    print("="*60)
    print(f"Upcoming matches collected: {results['matches_collected']}")
    
    if results['errors']:
        print(f"\nErrors: {len(results['errors'])}")
        for error in results['errors']:
            print(f"  - {error}")
    
    print("="*60)


def collect_injury_data(orchestrator: APIFootballOrchestrator, args):
    """Collect current injury data."""
    logger = logging.getLogger("injury_collection")
    
    logger.info("Updating injury status across all leagues...")
    
    results = orchestrator.update_injury_status()
    
    print("\n" + "="*60)
    print("INJURY DATA COLLECTION RESULTS")
    print("="*60)
    print(f"Injuries updated: {results['injuries_updated']}")
    
    if results['errors']:
        print(f"\nErrors: {len(results['errors'])}")
        for error in results['errors']:
            print(f"  - {error}")
    
    print("="*60)


def collect_match_details(orchestrator: APIFootballOrchestrator, args):
    """Collect detailed data for a specific match."""
    logger = logging.getLogger("match_details")
    
    if not args.fixture_id:
        logger.error("Fixture ID is required for match details collection")
        return
    
    logger.info(f"Collecting detailed data for fixture {args.fixture_id}...")
    
    try:
        results = orchestrator.collect_match_details(args.fixture_id)
        
        print("\n" + "="*60)
        print(f"MATCH DETAILS COLLECTION - FIXTURE {args.fixture_id}")
        print("="*60)
        print(f"Basic info collected: {'✅' if results['basic_info'] else '❌'}")
        print(f"Statistics collected: {results['statistics']} teams")
        print(f"Events collected: {results['events']} events")
        print(f"Lineups collected: {results['lineups']} teams")
        print(f"Player stats collected: {results['player_stats']} teams")
        print(f"Predictions available: {'✅' if results['predictions'] else '❌'}")
        print(f"Odds collected: {results['odds']} bookmakers")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error collecting match details: {e}")


def main():
    """Main data collection function."""
    # Setup
    setup_logging()
    logger = logging.getLogger("collect_api_football_data")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    args = parse_arguments()
    
    logger.info("Starting API-Football data collection...")
    logger.info(f"Configuration: {vars(args)}")
    
    # Check configuration
    if not check_api_configuration():
        logger.error("API-Football configuration is invalid")
        return 1
    
    try:
        # Initialize orchestrator
        orchestrator = APIFootballOrchestrator()
        
        # Check API status if requested
        if args.check_api_status:
            display_api_status(orchestrator)
        
        # Perform dry run check
        if args.dry_run:
            logger.info("DRY RUN MODE - No data will be saved to database")
            print("🔍 DRY RUN MODE ENABLED - No data will be saved")
        
        # Execute collection based on type
        if args.collection_type == 'full':
            collect_full_data(orchestrator, args)
        
        elif args.collection_type == 'recent':
            collect_recent_data(orchestrator, args)
        
        elif args.collection_type == 'upcoming':
            collect_upcoming_matches(orchestrator, args)
        
        elif args.collection_type == 'injuries':
            collect_injury_data(orchestrator, args)
        
        elif args.collection_type == 'match-details':
            collect_match_details(orchestrator, args)
        
        # Final API status check
        if args.check_api_status:
            print("\nFinal API Status:")
            display_api_status(orchestrator)
        
        print(f"\n✅ Data collection completed successfully!")
        logger.info("Data collection completed successfully")
        
        return 0
        
    except Exception as e:
        logger.error(f"Data collection failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
