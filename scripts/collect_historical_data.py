#!/usr/bin/env python3
"""Collect historical data from past seasons for model training."""

import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.collectors.api_football_collector import APIFootballCollector
from src.utils.database import DatabaseManager
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/historical_collection.log')
        ]
    )


def collect_historical_season_data(league_id: int, season: int, league_name: str):
    """Collect comprehensive historical data for a specific league and season."""
    
    logger = logging.getLogger("historical_collection")
    collector = APIFootballCollector()
    
    print(f"\n🏆 COLLECTING {league_name.upper()} {season} SEASON DATA")
    print("="*70)
    
    try:
        # 1. Collect Teams
        print("⚽ Collecting teams...")
        teams = collector.collect_teams(league_id, season)
        print(f"✅ Collected {len(teams)} teams")

        # Save teams to database
        if teams:
            from src.data.processors.data_processor import DataProcessor
            processor = DataProcessor()
            saved_teams = processor.process_teams(teams)
            print(f"💾 Saved {len(saved_teams)} teams to database")
        
        if not teams:
            print(f"❌ No teams found for {league_name} {season}")
            return False
        
        # Show sample teams
        print("📋 Sample teams:")
        for team in teams[:5]:
            print(f"   - {team['name']} (ID: {team['external_id']})")
        
        # 2. Collect All Matches for the Season
        print(f"\n🏟️  Collecting all matches for {season} season...")
        
        # Get season dates
        season_start = f"{season}-08-01"  # Approximate start
        season_end = f"{season + 1}-06-01"  # Approximate end
        
        matches = collector.collect_matches(league_id, season, season_start, season_end)
        print(f"✅ Collected {len(matches)} matches")

        # Save matches to database
        if matches:
            saved_matches = processor.process_matches(matches)
            print(f"💾 Saved {len(saved_matches)} matches to database")
        
        if matches:
            # Show sample matches
            print("📋 Sample matches:")
            finished_matches = [m for m in matches if m.get('status') == 'FT']
            for match in finished_matches[:3]:
                home_team = match.get('home_team_name', 'Home')
                away_team = match.get('away_team_name', 'Away')
                match_date = match.get('match_date', 'Unknown')[:10]
                home_score = match.get('home_score', 'N/A')
                away_score = match.get('away_score', 'N/A')
                
                print(f"   📅 {match_date}: {home_team} {home_score}-{away_score} {away_team}")
            
            print(f"   📊 Finished matches: {len(finished_matches)}")
        
        # 3. Collect Injuries
        print(f"\n🏥 Collecting injury data...")
        injuries = collector.collect_injuries(league_id)
        print(f"✅ Collected {len(injuries)} injury records")
        
        # 4. Collect Match Statistics for finished matches
        print(f"\n📊 Collecting detailed match statistics...")
        stats_collected = 0
        
        finished_matches = [m for m in matches if m.get('status') == 'FT']
        for i, match in enumerate(finished_matches[:10], 1):  # Limit to first 10 for API quota
            match_id = match.get('external_id')
            if match_id:
                try:
                    stats = collector.collect_match_statistics(match_id)
                    if stats:
                        stats_collected += 1
                    
                    if i % 5 == 0:
                        print(f"   Processed {i}/{min(10, len(finished_matches))} matches...")
                        
                except Exception as e:
                    logger.warning(f"Failed to collect stats for match {match_id}: {e}")
        
        print(f"✅ Collected statistics for {stats_collected} matches")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to collect historical data for {league_name} {season}: {e}")
        print(f"❌ Error: {e}")
        return False


def main():
    """Main historical data collection function."""
    
    parser = argparse.ArgumentParser(description='Collect historical football data for model training')
    parser.add_argument('--seasons', nargs='+', type=int, default=[2024, 2023, 2022], 
                       help='Seasons to collect (default: 2024 2023 2022)')
    parser.add_argument('--leagues', nargs='+', 
                       choices=['premier_league', 'la_liga', 'bundesliga', 'serie_a', 'ligue_1'],
                       default=['premier_league'], 
                       help='Leagues to collect (default: premier_league)')
    parser.add_argument('--check-api-status', action='store_true',
                       help='Check API status before starting')
    
    args = parser.parse_args()
    
    # Setup
    setup_logging()
    logger = logging.getLogger("historical_collection")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    print("🏈 HISTORICAL DATA COLLECTION FOR MODEL TRAINING")
    print("="*80)
    print(f"📅 Seasons: {args.seasons}")
    print(f"🏆 Leagues: {args.leagues}")
    print("="*80)
    
    # Check API status
    if args.check_api_status:
        try:
            from src.data.collectors.api_football_client import APIFootballClient
            client = APIFootballClient()
            status = client.get_api_status()
            
            print("📊 API STATUS:")
            print(f"   Account: {status['account']['firstname']} {status['account']['lastname']}")
            print(f"   Plan: {status['subscription']['plan']}")
            print(f"   Requests today: {status['requests']['current']}/{status['requests']['limit_day']}")
            remaining = status['requests']['limit_day'] - status['requests']['current']
            print(f"   Remaining: {remaining}")
            
            if remaining < 50:
                print("⚠️  Warning: Low API requests remaining. Consider running tomorrow.")
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return 1
            
        except Exception as e:
            print(f"❌ API status check failed: {e}")
            return 1
    
    # Get league configurations
    try:
        leagues_config = config.get_leagues()
    except Exception as e:
        print(f"❌ Failed to load league configurations: {e}")
        return 1
    
    # Collect data for each league and season
    total_success = 0
    total_attempts = 0
    
    for league_name in args.leagues:
        if league_name not in leagues_config:
            print(f"❌ League '{league_name}' not found in configuration")
            continue
        
        league_config = leagues_config[league_name]
        league_id = league_config['id']
        
        for season in args.seasons:
            total_attempts += 1
            
            print(f"\n{'='*80}")
            print(f"🎯 COLLECTING: {league_config['name']} {season}")
            print(f"{'='*80}")
            
            success = collect_historical_season_data(
                league_id=league_id,
                season=season,
                league_name=league_config['name']
            )
            
            if success:
                total_success += 1
                print(f"✅ Successfully collected {league_config['name']} {season} data")
            else:
                print(f"❌ Failed to collect {league_config['name']} {season} data")
            
            # Small delay between collections
            import time
            time.sleep(2)
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏆 HISTORICAL DATA COLLECTION SUMMARY")
    print(f"{'='*80}")
    print(f"📊 Total attempts: {total_attempts}")
    print(f"✅ Successful collections: {total_success}")
    print(f"❌ Failed collections: {total_attempts - total_success}")
    print(f"📈 Success rate: {(total_success/total_attempts)*100:.1f}%" if total_attempts > 0 else "N/A")
    
    if total_success > 0:
        print(f"\n🚀 NEXT STEPS:")
        print("1. ✅ Historical data collected successfully")
        print("2. 🤖 Train ML models with collected data:")
        print("   python scripts/train_models.py --data-source database --min-samples 50")
        print("3. 📊 Evaluate model performance:")
        print("   python scripts/evaluate_models.py")
        print("4. 🔮 Start making predictions!")
        
        # Check database content
        try:
            with DatabaseManager().get_session() as session:
                from src.utils.database.models import Match, Team
                
                match_count = session.query(Match).count()
                team_count = session.query(Team).count()
                
                print(f"\n📊 DATABASE SUMMARY:")
                print(f"   Teams: {team_count}")
                print(f"   Matches: {match_count}")
                
        except Exception as e:
            logger.warning(f"Failed to check database content: {e}")
    
    else:
        print(f"\n❌ No data collected successfully")
        print("💡 TROUBLESHOOTING:")
        print("1. Check API key permissions")
        print("2. Verify league IDs and seasons")
        print("3. Check API quota limits")
        print("4. Try with fewer seasons or leagues")
    
    return 0 if total_success > 0 else 1


if __name__ == "__main__":
    sys.exit(main())
