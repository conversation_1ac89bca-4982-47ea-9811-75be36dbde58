#!/usr/bin/env python3
"""Data collection script for the football prediction system."""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.data.collection_orchestrator import DataCollectionOrchestrator
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/data_collection.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect football data')
    
    parser.add_argument(
        '--leagues',
        type=str,
        help='Comma-separated list of leagues to collect (e.g., "premier_league,la_liga")'
    )
    
    parser.add_argument(
        '--season',
        type=str,
        help='Season to collect (e.g., "2023-24")'
    )
    
    parser.add_argument(
        '--no-injuries',
        action='store_true',
        help='Skip injury data collection'
    )
    
    parser.add_argument(
        '--save-to-db',
        action='store_true',
        help='Save collected data to database'
    )
    
    parser.add_argument(
        '--max-workers',
        type=int,
        default=3,
        help='Maximum number of concurrent workers'
    )
    
    parser.add_argument(
        '--recent-matches',
        type=int,
        help='Collect recent matches from last N days'
    )
    
    parser.add_argument(
        '--upcoming-matches',
        type=int,
        help='Collect upcoming matches for next N days'
    )
    
    return parser.parse_args()


def main():
    """Main data collection function."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    setup_logging()
    logger = logging.getLogger("data_collection")
    
    args = parse_arguments()
    
    logger.info("Starting data collection...")
    
    try:
        # Initialize orchestrator
        orchestrator = DataCollectionOrchestrator()
        
        # Parse leagues if provided
        leagues = None
        if args.leagues:
            leagues = [league.strip() for league in args.leagues.split(',')]
        
        # Collect data based on arguments
        if args.recent_matches:
            logger.info(f"Collecting recent matches from last {args.recent_matches} days")
            matches = orchestrator.collect_recent_matches(args.recent_matches)
            logger.info(f"Collected {len(matches)} recent matches")
            
        elif args.upcoming_matches:
            logger.info(f"Collecting upcoming matches for next {args.upcoming_matches} days")
            matches = orchestrator.collect_upcoming_matches(args.upcoming_matches)
            logger.info(f"Collected {len(matches)} upcoming matches")
            
        else:
            # Full data collection
            results = orchestrator.collect_all_data(
                leagues=leagues,
                season=args.season,
                include_injuries=not args.no_injuries,
                max_workers=args.max_workers
            )
            
            # Print summary
            logger.info("Data collection summary:")
            logger.info(f"  Leagues: {len(results['leagues'])}")
            logger.info(f"  Teams: {len(results['teams'])}")
            logger.info(f"  Matches: {len(results['matches'])}")
            logger.info(f"  Players: {len(results['players'])}")
            logger.info(f"  Injuries: {len(results['injuries'])}")
            logger.info(f"  Standings: {len(results['standings'])}")
            logger.info(f"  Errors: {len(results['errors'])}")
            logger.info(f"  Collection time: {results['collection_time']:.2f} seconds")
            
            # Print errors if any
            if results['errors']:
                logger.warning("Errors encountered:")
                for error in results['errors']:
                    logger.warning(f"  - {error}")
            
            # Save to database if requested
            if args.save_to_db:
                logger.info("Saving data to database...")
                saved_counts = orchestrator.save_collected_data(results)
                logger.info(f"Saved to database: {saved_counts}")
        
        logger.info("Data collection completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Data collection failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
