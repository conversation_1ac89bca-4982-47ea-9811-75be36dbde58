#!/usr/bin/env python3
"""Test script to verify the football prediction system components."""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.database import DatabaseManager
from src.data.collection_orchestrator import DataCollectionOrchestrator
from src.features.engineering.feature_pipeline import FeatureEngineeringPipeline
from src.utils.config import config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )


def test_database():
    """Test database functionality."""
    print("\n=== Testing Database ===")
    
    try:
        db_manager = DatabaseManager()
        
        # Test connection
        if db_manager.test_connection():
            print("✓ Database connection successful")
        else:
            print("✗ Database connection failed")
            return False
        
        # Test table creation
        db_manager.create_tables()
        print("✓ Database tables created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False


def test_data_collection():
    """Test data collection functionality."""
    print("\n=== Testing Data Collection ===")
    
    try:
        orchestrator = DataCollectionOrchestrator()
        print("✓ Data collection orchestrator initialized")
        
        # Test with empty data (to avoid API calls in testing)
        sample_data = {
            'leagues': [],
            'teams': [],
            'matches': [],
            'players': [],
            'injuries': []
        }
        
        print("✓ Data collection components working")
        return True
        
    except Exception as e:
        print(f"✗ Data collection test failed: {e}")
        return False


def test_feature_engineering():
    """Test feature engineering functionality."""
    print("\n=== Testing Feature Engineering ===")
    
    try:
        pipeline = FeatureEngineeringPipeline()
        print("✓ Feature engineering pipeline initialized")
        
        # Create sample match data
        sample_matches = [
            {
                'id': 1,
                'external_id': 1001,
                'home_team_id': 1,
                'away_team_id': 2,
                'match_date': datetime.now(),
                'league_id': 1,
                'season': '2023-24',
                'status': 'FINISHED',
                'home_score': 2,
                'away_score': 1
            },
            {
                'id': 2,
                'external_id': 1002,
                'home_team_id': 3,
                'away_team_id': 4,
                'match_date': datetime.now(),
                'league_id': 1,
                'season': '2023-24',
                'status': 'SCHEDULED'
            }
        ]
        
        # Test feature extraction
        features_df = pipeline.extract_features(sample_matches, parallel=False)
        print(f"✓ Feature extraction successful - Shape: {features_df.shape}")
        
        # Test feature matrix creation
        feature_matrix = pipeline.create_feature_matrix(sample_matches)
        print(f"✓ Feature matrix creation successful - Shape: {feature_matrix.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Feature engineering test failed: {e}")
        return False


def test_configuration():
    """Test configuration management."""
    print("\n=== Testing Configuration ===")
    
    try:
        # Test config loading
        db_config = config.get_database_config()
        print(f"✓ Database config loaded: {db_config.type}")
        
        api_config = config.get_api_config()
        print(f"✓ API config loaded: {api_config.host}:{api_config.port}")
        
        leagues = config.get_leagues()
        print(f"✓ Leagues config loaded: {len(leagues)} leagues")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Football Prediction ML System - Component Tests")
    print("=" * 50)
    
    setup_logging()
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    tests = [
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("Data Collection", test_data_collection),
        ("Feature Engineering", test_feature_engineering)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
