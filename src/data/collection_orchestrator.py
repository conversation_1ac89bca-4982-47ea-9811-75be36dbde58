"""Data collection orchestrator for coordinating multiple data sources."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from .collectors import FootballDataAPICollector, InjuryDataCollector
from src.utils.config import config
from src.utils.database import DatabaseManager


class DataCollectionOrchestrator:
    """Orchestrates data collection from multiple sources."""
    
    def __init__(self):
        """Initialize the data collection orchestrator."""
        self.logger = logging.getLogger("data_collection")
        
        # Initialize collectors
        self.collectors = {
            'football_data_api': FootballDataAPICollector(),
            'injury_collector': InjuryDataCollector()
        }
        
        # Initialize database manager
        self.db_manager = DatabaseManager()
        
        # Get configuration
        self.leagues_config = config.get_leagues()
        
    def collect_all_data(self, leagues: Optional[List[str]] = None, 
                        season: Optional[str] = None,
                        include_injuries: bool = True,
                        max_workers: int = 3) -> Dict[str, Any]:
        """Collect all data for specified leagues and season.
        
        Args:
            leagues: List of league names to collect (None for all configured)
            season: Season to collect (None for current season)
            include_injuries: Whether to collect injury data
            max_workers: Maximum number of concurrent workers
            
        Returns:
            Dictionary with collection results
        """
        start_time = time.time()
        
        # Use configured leagues if none specified
        if leagues is None:
            leagues = list(self.leagues_config.keys())
        
        # Use current season if none specified
        if season is None:
            season = self._get_current_season()
        
        self.logger.info(f"Starting data collection for leagues: {leagues}, season: {season}")
        
        results = {
            'leagues': [],
            'teams': [],
            'matches': [],
            'players': [],
            'injuries': [],
            'standings': [],
            'errors': [],
            'collection_time': None
        }
        
        try:
            # Collect leagues first
            self.logger.info("Collecting league data...")
            leagues_data = self._collect_leagues()
            results['leagues'] = leagues_data
            
            # Collect data for each league
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_league = {}
                
                for league_name in leagues:
                    if league_name in self.leagues_config:
                        league_config = self.leagues_config[league_name]
                        future = executor.submit(
                            self._collect_league_data,
                            league_name,
                            league_config,
                            season
                        )
                        future_to_league[future] = league_name
                
                # Process completed futures
                for future in as_completed(future_to_league):
                    league_name = future_to_league[future]
                    try:
                        league_results = future.result()
                        
                        # Merge results
                        results['teams'].extend(league_results.get('teams', []))
                        results['matches'].extend(league_results.get('matches', []))
                        results['players'].extend(league_results.get('players', []))
                        results['standings'].extend(league_results.get('standings', []))
                        
                    except Exception as e:
                        error_msg = f"Error collecting data for {league_name}: {e}"
                        self.logger.error(error_msg)
                        results['errors'].append(error_msg)
            
            # Collect injury data if requested
            if include_injuries:
                self.logger.info("Collecting injury data...")
                injury_results = self._collect_injury_data(results['teams'])
                results['injuries'] = injury_results
            
            # Calculate collection time
            results['collection_time'] = time.time() - start_time
            
            self.logger.info(f"Data collection completed in {results['collection_time']:.2f} seconds")
            self.logger.info(f"Collected: {len(results['teams'])} teams, "
                           f"{len(results['matches'])} matches, "
                           f"{len(results['players'])} players, "
                           f"{len(results['injuries'])} injuries")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Data collection failed: {e}")
            results['errors'].append(str(e))
            return results
    
    def _collect_leagues(self) -> List[Dict[str, Any]]:
        """Collect league data from Football Data API.
        
        Returns:
            List of league dictionaries
        """
        try:
            return self.collectors['football_data_api'].collect_leagues()
        except Exception as e:
            self.logger.error(f"Failed to collect leagues: {e}")
            return []
    
    def _collect_league_data(self, league_name: str, league_config: Dict[str, Any], 
                           season: str) -> Dict[str, Any]:
        """Collect all data for a specific league.
        
        Args:
            league_name: Name of the league
            league_config: League configuration
            season: Season to collect
            
        Returns:
            Dictionary with league data
        """
        results = {
            'teams': [],
            'matches': [],
            'players': [],
            'standings': []
        }
        
        league_id = league_config.get('id')
        if not league_id:
            self.logger.warning(f"No league ID configured for {league_name}")
            return results
        
        collector = self.collectors['football_data_api']
        
        try:
            # Collect teams
            self.logger.info(f"Collecting teams for {league_name}")
            teams = collector.collect_teams(str(league_id), season)
            results['teams'] = teams
            
            # Collect matches
            self.logger.info(f"Collecting matches for {league_name}")
            matches = collector.collect_matches(str(league_id), season)
            results['matches'] = matches
            
            # Collect standings
            self.logger.info(f"Collecting standings for {league_name}")
            standings = collector.collect_standings(str(league_id), season)
            results['standings'] = standings
            
            # Collect players for each team
            self.logger.info(f"Collecting players for {league_name}")
            all_players = []
            for team in teams:
                team_id = team.get('external_id')
                if team_id:
                    players = collector.collect_players(team_id, season)
                    all_players.extend(players)
                    
                    # Add small delay between team requests
                    time.sleep(0.5)
            
            results['players'] = all_players
            
        except Exception as e:
            self.logger.error(f"Error collecting data for {league_name}: {e}")
        
        return results
    
    def _collect_injury_data(self, teams: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Collect injury data for all teams.
        
        Args:
            teams: List of team dictionaries
            
        Returns:
            List of injury dictionaries
        """
        injury_collector = self.collectors['injury_collector']
        all_injuries = []
        
        # Extract unique team names
        team_names = list(set(team['name'] for team in teams))
        
        try:
            # Collect injuries for all teams
            team_injuries = injury_collector.collect_all_team_injuries(team_names)
            
            # Flatten the results
            for team_name, injuries in team_injuries.items():
                all_injuries.extend(injuries)
            
        except Exception as e:
            self.logger.error(f"Error collecting injury data: {e}")
        
        return all_injuries
    
    def collect_recent_matches(self, days_back: int = 7) -> List[Dict[str, Any]]:
        """Collect recent matches from the last N days.
        
        Args:
            days_back: Number of days to look back
            
        Returns:
            List of recent match dictionaries
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        all_matches = []
        collector = self.collectors['football_data_api']
        
        for league_name, league_config in self.leagues_config.items():
            league_id = league_config.get('id')
            if league_id:
                try:
                    matches = collector.collect_matches(
                        str(league_id),
                        self._get_current_season(),
                        date_from=start_date,
                        date_to=end_date
                    )
                    all_matches.extend(matches)
                except Exception as e:
                    self.logger.error(f"Error collecting recent matches for {league_name}: {e}")
        
        return all_matches
    
    def collect_upcoming_matches(self, days_ahead: int = 7) -> List[Dict[str, Any]]:
        """Collect upcoming matches for the next N days.
        
        Args:
            days_ahead: Number of days to look ahead
            
        Returns:
            List of upcoming match dictionaries
        """
        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)
        
        all_matches = []
        collector = self.collectors['football_data_api']
        
        for league_name, league_config in self.leagues_config.items():
            league_id = league_config.get('id')
            if league_id:
                try:
                    matches = collector.collect_matches(
                        str(league_id),
                        self._get_current_season(),
                        date_from=start_date,
                        date_to=end_date
                    )
                    all_matches.extend(matches)
                except Exception as e:
                    self.logger.error(f"Error collecting upcoming matches for {league_name}: {e}")
        
        return all_matches
    
    def _get_current_season(self) -> str:
        """Get current season string.
        
        Returns:
            Current season (e.g., "2023-24")
        """
        now = datetime.now()
        
        # Football seasons typically start in August
        if now.month >= 8:
            start_year = now.year
            end_year = now.year + 1
        else:
            start_year = now.year - 1
            end_year = now.year
        
        return f"{start_year}-{str(end_year)[-2:]}"
    
    def save_collected_data(self, data: Dict[str, Any]) -> Dict[str, int]:
        """Save collected data to database.
        
        Args:
            data: Collected data dictionary
            
        Returns:
            Dictionary with counts of saved records
        """
        saved_counts = {
            'leagues': 0,
            'teams': 0,
            'matches': 0,
            'players': 0,
            'injuries': 0,
            'standings': 0
        }
        
        try:
            # Save each data type
            if data.get('leagues'):
                saved_counts['leagues'] = self.db_manager.save_leagues(data['leagues'])
            
            if data.get('teams'):
                saved_counts['teams'] = self.db_manager.save_teams(data['teams'])
            
            if data.get('matches'):
                saved_counts['matches'] = self.db_manager.save_matches(data['matches'])
            
            if data.get('players'):
                saved_counts['players'] = self.db_manager.save_players(data['players'])
            
            if data.get('injuries'):
                saved_counts['injuries'] = self.db_manager.save_injuries(data['injuries'])
            
            if data.get('standings'):
                saved_counts['standings'] = self.db_manager.save_standings(data['standings'])
            
            self.logger.info(f"Saved data to database: {saved_counts}")
            
        except Exception as e:
            self.logger.error(f"Error saving data to database: {e}")
        
        return saved_counts
