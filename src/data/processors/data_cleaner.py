"""Data cleaning and preprocessing utilities."""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer


class DataCleaner:
    """Handles data cleaning and preprocessing operations."""
    
    def __init__(self):
        """Initialize data cleaner."""
        self.logger = logging.getLogger("data_cleaner")
        self.scalers = {}
        self.imputers = {}
    
    def clean_matches_data(self, matches_df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess matches data.
        
        Args:
            matches_df: Raw matches DataFrame
            
        Returns:
            Cleaned matches DataFrame
        """
        df = matches_df.copy()
        
        # Convert date columns
        if 'match_date' in df.columns:
            df['match_date'] = pd.to_datetime(df['match_date'], errors='coerce')
        
        # Clean score columns
        score_columns = ['home_score', 'away_score', 'home_score_ht', 'away_score_ht']
        for col in score_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                # Set negative scores to NaN
                df.loc[df[col] < 0, col] = np.nan
                # Set unrealistic high scores to NaN
                df.loc[df[col] > 20, col] = np.nan
        
        # Clean team IDs
        id_columns = ['home_team_id', 'away_team_id', 'league_id']
        for col in id_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove matches where home and away teams are the same
        if 'home_team_id' in df.columns and 'away_team_id' in df.columns:
            same_team_mask = df['home_team_id'] == df['away_team_id']
            if same_team_mask.any():
                self.logger.warning(f"Removing {same_team_mask.sum()} matches where home and away teams are the same")
                df = df[~same_team_mask]
        
        # Remove duplicate matches
        duplicate_cols = ['home_team_id', 'away_team_id', 'match_date']
        if all(col in df.columns for col in duplicate_cols):
            before_count = len(df)
            df = df.drop_duplicates(subset=duplicate_cols, keep='first')
            removed = before_count - len(df)
            if removed > 0:
                self.logger.info(f"Removed {removed} duplicate matches")
        
        # Clean status column
        if 'status' in df.columns:
            df['status'] = df['status'].fillna('UNKNOWN')
            # Standardize status values
            status_mapping = {
                'FT': 'FINISHED',
                'FULL_TIME': 'FINISHED',
                'SCHEDULED': 'SCHEDULED',
                'TIMED': 'SCHEDULED',
                'POSTPONED': 'POSTPONED',
                'CANCELLED': 'CANCELLED',
                'SUSPENDED': 'SUSPENDED'
            }
            df['status'] = df['status'].replace(status_mapping)
        
        self.logger.info(f"Cleaned matches data: {len(df)} matches remaining")
        return df
    
    def clean_teams_data(self, teams_df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess teams data.
        
        Args:
            teams_df: Raw teams DataFrame
            
        Returns:
            Cleaned teams DataFrame
        """
        df = teams_df.copy()
        
        # Clean team names
        if 'name' in df.columns:
            df['name'] = df['name'].str.strip()
            # Remove teams with empty names
            df = df[df['name'].notna() & (df['name'] != '')]
        
        # Clean external IDs
        if 'external_id' in df.columns:
            df['external_id'] = pd.to_numeric(df['external_id'], errors='coerce')
            # Remove teams without valid external IDs
            df = df[df['external_id'].notna()]
        
        # Clean founded year
        if 'founded' in df.columns:
            df['founded'] = pd.to_numeric(df['founded'], errors='coerce')
            # Set unrealistic founding years to NaN
            current_year = datetime.now().year
            df.loc[(df['founded'] < 1850) | (df['founded'] > current_year), 'founded'] = np.nan
        
        # Clean venue capacity
        if 'venue_capacity' in df.columns:
            df['venue_capacity'] = pd.to_numeric(df['venue_capacity'], errors='coerce')
            # Set unrealistic capacities to NaN
            df.loc[(df['venue_capacity'] < 0) | (df['venue_capacity'] > 200000), 'venue_capacity'] = np.nan
        
        # Remove duplicate teams
        if 'external_id' in df.columns:
            before_count = len(df)
            df = df.drop_duplicates(subset=['external_id'], keep='first')
            removed = before_count - len(df)
            if removed > 0:
                self.logger.info(f"Removed {removed} duplicate teams")
        
        self.logger.info(f"Cleaned teams data: {len(df)} teams remaining")
        return df
    
    def clean_players_data(self, players_df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess players data.
        
        Args:
            players_df: Raw players DataFrame
            
        Returns:
            Cleaned players DataFrame
        """
        df = players_df.copy()
        
        # Clean player names
        if 'name' in df.columns:
            df['name'] = df['name'].str.strip()
            # Remove players with empty names
            df = df[df['name'].notna() & (df['name'] != '')]
        
        # Clean external IDs
        if 'external_id' in df.columns:
            df['external_id'] = pd.to_numeric(df['external_id'], errors='coerce')
            # Remove players without valid external IDs
            df = df[df['external_id'].notna()]
        
        # Clean age
        if 'age' in df.columns:
            df['age'] = pd.to_numeric(df['age'], errors='coerce')
            # Set unrealistic ages to NaN
            df.loc[(df['age'] < 15) | (df['age'] > 50), 'age'] = np.nan
        
        # Clean height and weight
        if 'height' in df.columns:
            df['height'] = pd.to_numeric(df['height'], errors='coerce')
            # Set unrealistic heights to NaN (assuming cm)
            df.loc[(df['height'] < 150) | (df['height'] > 220), 'height'] = np.nan
        
        if 'weight' in df.columns:
            df['weight'] = pd.to_numeric(df['weight'], errors='coerce')
            # Set unrealistic weights to NaN (assuming kg)
            df.loc[(df['weight'] < 50) | (df['weight'] > 150), 'weight'] = np.nan
        
        # Clean positions
        if 'position' in df.columns:
            df['position'] = df['position'].str.upper().str.strip()
            # Standardize position names
            position_mapping = {
                'GOALKEEPER': 'GK',
                'CENTRE-BACK': 'CB',
                'CENTER-BACK': 'CB',
                'LEFT-BACK': 'LB',
                'RIGHT-BACK': 'RB',
                'DEFENSIVE MIDFIELD': 'CDM',
                'CENTRAL MIDFIELD': 'CM',
                'ATTACKING MIDFIELD': 'CAM',
                'LEFT MIDFIELD': 'LM',
                'RIGHT MIDFIELD': 'RM',
                'LEFT WINGER': 'LW',
                'RIGHT WINGER': 'RW',
                'CENTRE-FORWARD': 'CF',
                'CENTER-FORWARD': 'CF',
                'STRIKER': 'ST'
            }
            df['position'] = df['position'].replace(position_mapping)
        
        # Remove duplicate players
        if 'external_id' in df.columns:
            before_count = len(df)
            df = df.drop_duplicates(subset=['external_id'], keep='first')
            removed = before_count - len(df)
            if removed > 0:
                self.logger.info(f"Removed {removed} duplicate players")
        
        self.logger.info(f"Cleaned players data: {len(df)} players remaining")
        return df
    
    def handle_missing_values(self, df: pd.DataFrame, strategy: str = 'median', 
                            categorical_strategy: str = 'most_frequent') -> pd.DataFrame:
        """Handle missing values in DataFrame.
        
        Args:
            df: DataFrame with missing values
            strategy: Strategy for numeric columns ('mean', 'median', 'most_frequent', 'constant', 'knn')
            categorical_strategy: Strategy for categorical columns ('most_frequent', 'constant')
            
        Returns:
            DataFrame with missing values handled
        """
        df_cleaned = df.copy()
        
        # Separate numeric and categorical columns
        numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
        categorical_columns = df_cleaned.select_dtypes(include=['object', 'category']).columns
        
        # Handle numeric columns
        if len(numeric_columns) > 0:
            if strategy == 'knn':
                imputer = KNNImputer(n_neighbors=5)
                df_cleaned[numeric_columns] = imputer.fit_transform(df_cleaned[numeric_columns])
            else:
                imputer = SimpleImputer(strategy=strategy)
                df_cleaned[numeric_columns] = imputer.fit_transform(df_cleaned[numeric_columns])
        
        # Handle categorical columns
        if len(categorical_columns) > 0:
            imputer = SimpleImputer(strategy=categorical_strategy, fill_value='Unknown')
            df_cleaned[categorical_columns] = imputer.fit_transform(df_cleaned[categorical_columns])
        
        return df_cleaned
    
    def remove_outliers(self, df: pd.DataFrame, method: str = 'iqr', 
                       threshold: float = 1.5) -> pd.DataFrame:
        """Remove outliers from numeric columns.
        
        Args:
            df: Input DataFrame
            method: Outlier detection method ('iqr', 'zscore', 'isolation_forest')
            threshold: Threshold for outlier detection
            
        Returns:
            DataFrame with outliers removed
        """
        df_cleaned = df.copy()
        numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
        
        if method == 'iqr':
            for col in numeric_columns:
                Q1 = df_cleaned[col].quantile(0.25)
                Q3 = df_cleaned[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR
                
                outlier_mask = (df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)
                df_cleaned = df_cleaned[~outlier_mask]
        
        elif method == 'zscore':
            from scipy import stats
            for col in numeric_columns:
                z_scores = np.abs(stats.zscore(df_cleaned[col].dropna()))
                outlier_mask = z_scores > threshold
                df_cleaned = df_cleaned[~outlier_mask]
        
        return df_cleaned
    
    def scale_features(self, df: pd.DataFrame, method: str = 'standard', 
                      fit: bool = True) -> pd.DataFrame:
        """Scale numeric features.
        
        Args:
            df: Input DataFrame
            method: Scaling method ('standard', 'minmax', 'robust')
            fit: Whether to fit the scaler (True for training, False for inference)
            
        Returns:
            DataFrame with scaled features
        """
        df_scaled = df.copy()
        numeric_columns = df_scaled.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) == 0:
            return df_scaled
        
        # Choose scaler
        if method == 'standard':
            scaler_class = StandardScaler
        elif method == 'minmax':
            scaler_class = MinMaxScaler
        elif method == 'robust':
            scaler_class = RobustScaler
        else:
            raise ValueError(f"Unknown scaling method: {method}")
        
        # Fit or use existing scaler
        if fit or method not in self.scalers:
            self.scalers[method] = scaler_class()
            df_scaled[numeric_columns] = self.scalers[method].fit_transform(df_scaled[numeric_columns])
        else:
            df_scaled[numeric_columns] = self.scalers[method].transform(df_scaled[numeric_columns])
        
        return df_scaled
    
    def encode_categorical_features(self, df: pd.DataFrame, 
                                  method: str = 'onehot') -> pd.DataFrame:
        """Encode categorical features.
        
        Args:
            df: Input DataFrame
            method: Encoding method ('onehot', 'label', 'target')
            
        Returns:
            DataFrame with encoded categorical features
        """
        df_encoded = df.copy()
        categorical_columns = df_encoded.select_dtypes(include=['object', 'category']).columns
        
        if len(categorical_columns) == 0:
            return df_encoded
        
        if method == 'onehot':
            df_encoded = pd.get_dummies(df_encoded, columns=categorical_columns, prefix=categorical_columns)
        elif method == 'label':
            from sklearn.preprocessing import LabelEncoder
            for col in categorical_columns:
                le = LabelEncoder()
                df_encoded[col] = le.fit_transform(df_encoded[col].astype(str))
        
        return df_encoded
