"""Data validation and quality checks for football prediction system."""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Result of data validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    metrics: Dict[str, Any]


class DataValidator:
    """Validates data quality and consistency."""
    
    def __init__(self):
        """Initialize data validator."""
        self.logger = logging.getLogger("data_validator")
    
    def validate_matches_data(self, matches_df: pd.DataFrame) -> ValidationResult:
        """Validate matches data.
        
        Args:
            matches_df: DataFrame with match data
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        metrics = {}
        
        # Check required columns
        required_columns = ['home_team_id', 'away_team_id', 'match_date']
        missing_columns = [col for col in required_columns if col not in matches_df.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check data types
        if 'match_date' in matches_df.columns:
            try:
                pd.to_datetime(matches_df['match_date'])
            except:
                errors.append("Invalid date format in match_date column")
        
        # Check for duplicate matches
        if len(required_columns) <= len(matches_df.columns):
            duplicate_cols = ['home_team_id', 'away_team_id', 'match_date']
            duplicates = matches_df.duplicated(subset=duplicate_cols).sum()
            if duplicates > 0:
                warnings.append(f"Found {duplicates} duplicate matches")
            metrics['duplicate_matches'] = duplicates
        
        # Check for impossible scores
        if 'home_score' in matches_df.columns and 'away_score' in matches_df.columns:
            invalid_scores = matches_df[
                (matches_df['home_score'] < 0) | 
                (matches_df['away_score'] < 0) |
                (matches_df['home_score'] > 20) |
                (matches_df['away_score'] > 20)
            ]
            if len(invalid_scores) > 0:
                warnings.append(f"Found {len(invalid_scores)} matches with suspicious scores")
            metrics['suspicious_scores'] = len(invalid_scores)
        
        # Check date ranges
        if 'match_date' in matches_df.columns:
            try:
                dates = pd.to_datetime(matches_df['match_date'])
                future_matches = (dates > datetime.now() + timedelta(days=365)).sum()
                old_matches = (dates < datetime.now() - timedelta(days=365*10)).sum()
                
                if future_matches > 0:
                    warnings.append(f"Found {future_matches} matches more than 1 year in the future")
                if old_matches > 0:
                    warnings.append(f"Found {old_matches} matches more than 10 years old")
                
                metrics['future_matches'] = future_matches
                metrics['old_matches'] = old_matches
            except:
                pass
        
        # Calculate completeness metrics
        metrics['total_matches'] = len(matches_df)
        metrics['missing_data_percentage'] = (matches_df.isnull().sum().sum() / 
                                            (len(matches_df) * len(matches_df.columns))) * 100
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )
    
    def validate_teams_data(self, teams_df: pd.DataFrame) -> ValidationResult:
        """Validate teams data.
        
        Args:
            teams_df: DataFrame with team data
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        metrics = {}
        
        # Check required columns
        required_columns = ['external_id', 'name']
        missing_columns = [col for col in required_columns if col not in teams_df.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check for duplicate team IDs
        if 'external_id' in teams_df.columns:
            duplicates = teams_df['external_id'].duplicated().sum()
            if duplicates > 0:
                errors.append(f"Found {duplicates} duplicate team IDs")
            metrics['duplicate_team_ids'] = duplicates
        
        # Check for duplicate team names
        if 'name' in teams_df.columns:
            duplicate_names = teams_df['name'].duplicated().sum()
            if duplicate_names > 0:
                warnings.append(f"Found {duplicate_names} duplicate team names")
            metrics['duplicate_team_names'] = duplicate_names
        
        # Check team name lengths
        if 'name' in teams_df.columns:
            short_names = (teams_df['name'].str.len() < 2).sum()
            long_names = (teams_df['name'].str.len() > 50).sum()
            
            if short_names > 0:
                warnings.append(f"Found {short_names} teams with very short names")
            if long_names > 0:
                warnings.append(f"Found {long_names} teams with very long names")
            
            metrics['short_names'] = short_names
            metrics['long_names'] = long_names
        
        metrics['total_teams'] = len(teams_df)
        metrics['missing_data_percentage'] = (teams_df.isnull().sum().sum() / 
                                            (len(teams_df) * len(teams_df.columns))) * 100
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )
    
    def validate_players_data(self, players_df: pd.DataFrame) -> ValidationResult:
        """Validate players data.
        
        Args:
            players_df: DataFrame with player data
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        metrics = {}
        
        # Check required columns
        required_columns = ['external_id', 'name', 'team_id']
        missing_columns = [col for col in required_columns if col not in players_df.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check for duplicate player IDs
        if 'external_id' in players_df.columns:
            duplicates = players_df['external_id'].duplicated().sum()
            if duplicates > 0:
                errors.append(f"Found {duplicates} duplicate player IDs")
            metrics['duplicate_player_ids'] = duplicates
        
        # Check age ranges
        if 'age' in players_df.columns:
            young_players = (players_df['age'] < 16).sum()
            old_players = (players_df['age'] > 45).sum()
            
            if young_players > 0:
                warnings.append(f"Found {young_players} players under 16 years old")
            if old_players > 0:
                warnings.append(f"Found {old_players} players over 45 years old")
            
            metrics['young_players'] = young_players
            metrics['old_players'] = old_players
        
        # Check position validity
        if 'position' in players_df.columns:
            valid_positions = {'GK', 'CB', 'LB', 'RB', 'LWB', 'RWB', 'CM', 'CDM', 'CAM', 
                             'LM', 'RM', 'ST', 'CF', 'LW', 'RW'}
            invalid_positions = (~players_df['position'].isin(valid_positions)).sum()
            
            if invalid_positions > 0:
                warnings.append(f"Found {invalid_positions} players with invalid positions")
            metrics['invalid_positions'] = invalid_positions
        
        metrics['total_players'] = len(players_df)
        metrics['missing_data_percentage'] = (players_df.isnull().sum().sum() / 
                                            (len(players_df) * len(players_df.columns))) * 100
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )
    
    def validate_feature_matrix(self, features_df: pd.DataFrame) -> ValidationResult:
        """Validate feature matrix for ML training.
        
        Args:
            features_df: DataFrame with extracted features
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        metrics = {}
        
        # Check for infinite values
        inf_values = np.isinf(features_df.select_dtypes(include=[np.number])).sum().sum()
        if inf_values > 0:
            errors.append(f"Found {inf_values} infinite values in feature matrix")
        metrics['infinite_values'] = inf_values
        
        # Check for NaN values
        nan_values = features_df.isnull().sum().sum()
        if nan_values > 0:
            warnings.append(f"Found {nan_values} missing values in feature matrix")
        metrics['missing_values'] = nan_values
        
        # Check feature variance
        numeric_features = features_df.select_dtypes(include=[np.number])
        zero_variance_features = (numeric_features.var() == 0).sum()
        if zero_variance_features > 0:
            warnings.append(f"Found {zero_variance_features} features with zero variance")
        metrics['zero_variance_features'] = zero_variance_features
        
        # Check for highly correlated features
        if len(numeric_features.columns) > 1:
            corr_matrix = numeric_features.corr().abs()
            high_corr_pairs = (corr_matrix > 0.95).sum().sum() - len(corr_matrix)
            if high_corr_pairs > 0:
                warnings.append(f"Found {high_corr_pairs} highly correlated feature pairs")
            metrics['high_correlation_pairs'] = high_corr_pairs
        
        # Check feature ranges
        extreme_values = 0
        for col in numeric_features.columns:
            q99 = numeric_features[col].quantile(0.99)
            q01 = numeric_features[col].quantile(0.01)
            extreme_count = ((numeric_features[col] > q99 * 10) | 
                           (numeric_features[col] < q01 * 10)).sum()
            extreme_values += extreme_count
        
        if extreme_values > 0:
            warnings.append(f"Found {extreme_values} extreme feature values")
        metrics['extreme_values'] = extreme_values
        
        metrics['total_features'] = len(features_df.columns)
        metrics['total_samples'] = len(features_df)
        metrics['numeric_features'] = len(numeric_features.columns)
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )
    
    def generate_data_quality_report(self, validation_results: Dict[str, ValidationResult]) -> str:
        """Generate a comprehensive data quality report.
        
        Args:
            validation_results: Dictionary of validation results by data type
            
        Returns:
            Formatted data quality report
        """
        report = ["Data Quality Report", "=" * 50, ""]
        
        overall_valid = True
        total_errors = 0
        total_warnings = 0
        
        for data_type, result in validation_results.items():
            report.append(f"{data_type.upper()} DATA:")
            report.append("-" * 20)
            
            if result.is_valid:
                report.append("✓ VALID")
            else:
                report.append("✗ INVALID")
                overall_valid = False
            
            if result.errors:
                report.append("Errors:")
                for error in result.errors:
                    report.append(f"  - {error}")
                total_errors += len(result.errors)
            
            if result.warnings:
                report.append("Warnings:")
                for warning in result.warnings:
                    report.append(f"  - {warning}")
                total_warnings += len(result.warnings)
            
            if result.metrics:
                report.append("Metrics:")
                for metric, value in result.metrics.items():
                    report.append(f"  - {metric}: {value}")
            
            report.append("")
        
        # Summary
        report.extend([
            "SUMMARY:",
            "-" * 20,
            f"Overall Status: {'VALID' if overall_valid else 'INVALID'}",
            f"Total Errors: {total_errors}",
            f"Total Warnings: {total_warnings}",
            ""
        ])
        
        return "\n".join(report)
