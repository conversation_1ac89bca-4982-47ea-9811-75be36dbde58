"""Data processor for saving collected data to database."""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from src.utils.database import DatabaseManager
from src.utils.database.models import Team, Match, MatchStatistics, Injury


class DataProcessor:
    """Process and save collected football data to database."""
    
    def __init__(self):
        """Initialize data processor."""
        self.logger = logging.getLogger("data_processor")
        self.db_manager = DatabaseManager()
    
    def process_teams(self, teams_data: List[Dict[str, Any]]) -> List[Team]:
        """Process and save teams data to database.

        Args:
            teams_data: List of team data dictionaries

        Returns:
            List of saved Team objects
        """
        saved_teams = []

        with self.db_manager.get_session() as session:
            for team_data in teams_data:
                try:
                    # Map API data to database fields
                    mapped_data = {
                        'external_id': team_data.get('external_id'),
                        'name': team_data.get('name'),
                        'short_name': team_data.get('short_name'),
                        'logo_url': team_data.get('logo'),  # Map 'logo' to 'logo_url'
                        'founded': team_data.get('founded'),
                        'venue_name': team_data.get('venue_name'),
                        'venue_capacity': team_data.get('venue_capacity'),
                        'league_id': team_data.get('league_id')
                    }

                    # Remove None values
                    mapped_data = {k: v for k, v in mapped_data.items() if v is not None}

                    # Check if team already exists
                    existing_team = session.query(Team).filter(
                        Team.external_id == mapped_data.get('external_id')
                    ).first()

                    if existing_team:
                        # Update existing team
                        for key, value in mapped_data.items():
                            if hasattr(existing_team, key):
                                setattr(existing_team, key, value)
                        team = existing_team
                    else:
                        # Create new team
                        team = Team(**mapped_data)
                        session.add(team)

                    saved_teams.append(team)

                except Exception as e:
                    self.logger.error(f"Error processing team {team_data.get('name', 'Unknown')}: {e}")
                    continue

            session.commit()
            self.logger.info(f"Processed {len(saved_teams)} teams")

        return saved_teams
    
    def process_matches(self, matches_data: List[Dict[str, Any]]) -> List[Match]:
        """Process and save matches data to database.

        Args:
            matches_data: List of match data dictionaries

        Returns:
            List of saved Match objects
        """
        saved_matches = []

        with self.db_manager.get_session() as session:
            for match_data in matches_data:
                try:
                    # Convert date string to datetime if needed
                    match_date = match_data.get('match_date')
                    if isinstance(match_date, str):
                        try:
                            match_date = datetime.fromisoformat(match_date.replace('Z', '+00:00'))
                        except:
                            try:
                                match_date = datetime.strptime(match_date[:19], '%Y-%m-%d %H:%M:%S')
                            except:
                                match_date = None

                    # Map API data to database fields
                    mapped_data = {
                        'external_id': match_data.get('external_id'),
                        'league_id': match_data.get('league_id'),
                        'season': match_data.get('season'),
                        'matchday': match_data.get('matchday'),
                        'round': match_data.get('round'),
                        'home_team_id': match_data.get('home_team_id'),
                        'away_team_id': match_data.get('away_team_id'),
                        'match_date': match_date,
                        'timestamp': match_data.get('timestamp'),
                        'timezone': match_data.get('timezone'),
                        'status': match_data.get('status'),
                        'status_long': match_data.get('status_long'),
                        'elapsed': match_data.get('elapsed'),
                        'home_score': match_data.get('home_score'),
                        'away_score': match_data.get('away_score'),
                        'home_score_ht': match_data.get('home_score_ht'),
                        'away_score_ht': match_data.get('away_score_ht'),
                        'home_score_ft': match_data.get('home_score_ft'),
                        'away_score_ft': match_data.get('away_score_ft'),
                        'referee': match_data.get('referee'),
                        'venue': match_data.get('venue'),
                        'venue_city': match_data.get('venue_city')
                    }

                    # Remove None values
                    mapped_data = {k: v for k, v in mapped_data.items() if v is not None}

                    # Check if match already exists
                    existing_match = session.query(Match).filter(
                        Match.external_id == mapped_data.get('external_id')
                    ).first()

                    if existing_match:
                        # Update existing match
                        for key, value in mapped_data.items():
                            if hasattr(existing_match, key):
                                setattr(existing_match, key, value)
                        match = existing_match
                    else:
                        # Create new match
                        match = Match(**mapped_data)
                        session.add(match)

                    saved_matches.append(match)

                except Exception as e:
                    self.logger.error(f"Error processing match {match_data.get('external_id', 'Unknown')}: {e}")
                    continue

            session.commit()
            self.logger.info(f"Processed {len(saved_matches)} matches")

        return saved_matches
    
    def process_match_statistics(self, stats_data: List[Dict[str, Any]]) -> List[MatchStatistics]:
        """Process and save match statistics to database.
        
        Args:
            stats_data: List of match statistics dictionaries
            
        Returns:
            List of saved MatchStatistics objects
        """
        saved_stats = []
        
        with self.db_manager.get_session() as session:
            for stat_data in stats_data:
                try:
                    # Check if statistics already exist
                    existing_stats = session.query(MatchStatistics).filter(
                        MatchStatistics.match_id == stat_data.get('match_id')
                    ).first()
                    
                    if existing_stats:
                        # Update existing statistics
                        for key, value in stat_data.items():
                            if hasattr(existing_stats, key) and value is not None:
                                setattr(existing_stats, key, value)
                        stats = existing_stats
                    else:
                        # Create new statistics
                        stats = MatchStatistics(**stat_data)
                        session.add(stats)
                    
                    saved_stats.append(stats)
                    
                except Exception as e:
                    self.logger.error(f"Error processing statistics for match {stat_data.get('match_id', 'Unknown')}: {e}")
                    continue
            
            session.commit()
            self.logger.info(f"Processed {len(saved_stats)} match statistics")
        
        return saved_stats
    
    def process_injuries(self, injuries_data: List[Dict[str, Any]]) -> List[Injury]:
        """Process and save injuries data to database.
        
        Args:
            injuries_data: List of injury data dictionaries
            
        Returns:
            List of saved Injury objects
        """
        saved_injuries = []
        
        with self.db_manager.get_session() as session:
            for injury_data in injuries_data:
                try:
                    # Check if injury already exists
                    existing_injury = session.query(Injury).filter(
                        Injury.player_id == injury_data.get('player_id'),
                        Injury.team_id == injury_data.get('team_id'),
                        Injury.injury_date == injury_data.get('injury_date')
                    ).first()
                    
                    if existing_injury:
                        # Update existing injury
                        for key, value in injury_data.items():
                            if hasattr(existing_injury, key) and value is not None:
                                setattr(existing_injury, key, value)
                        injury = existing_injury
                    else:
                        # Create new injury
                        injury = Injury(**injury_data)
                        session.add(injury)
                    
                    saved_injuries.append(injury)
                    
                except Exception as e:
                    self.logger.error(f"Error processing injury for player {injury_data.get('player_id', 'Unknown')}: {e}")
                    continue
            
            session.commit()
            self.logger.info(f"Processed {len(saved_injuries)} injuries")
        
        return saved_injuries
    
    def get_database_summary(self) -> Dict[str, int]:
        """Get summary of data in database.
        
        Returns:
            Dictionary with counts of each data type
        """
        summary = {}
        
        with self.db_manager.get_session() as session:
            summary['teams'] = session.query(Team).count()
            summary['matches'] = session.query(Match).count()
            summary['finished_matches'] = session.query(Match).filter(Match.status == 'FT').count()
            summary['match_statistics'] = session.query(MatchStatistics).count()
            summary['injuries'] = session.query(Injury).count()
        
        return summary
