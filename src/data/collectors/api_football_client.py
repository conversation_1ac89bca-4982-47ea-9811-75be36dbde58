"""API-Football client for comprehensive football data collection."""

import logging
import time
from typing import Dict, List, Any, Optional
import requests
from datetime import datetime, timedelta
import json

from src.utils.config import config


class APIFootballClient:
    """Client for API-Football (api-football.com) data collection."""
    
    def __init__(self):
        """Initialize API-Football client."""
        self.logger = logging.getLogger("api_football_client")
        
        # Get configuration
        try:
            data_sources = config._config.get('data_sources', {})
            api_config = data_sources.get('api_football', {})
        except AttributeError:
            data_sources = {}
            api_config = {}
        
        self.base_url = api_config.get('base_url', 'https://v3.football.api-sports.io')
        self.api_key = api_config.get('api_key')
        self.rate_limit = api_config.get('rate_limit', 100)
        self.timeout = api_config.get('timeout', 30)
        self.retry_attempts = api_config.get('retry_attempts', 3)
        self.retry_delay = api_config.get('retry_delay', 1)
        
        if not self.api_key:
            raise ValueError("API-Football API key is required")
        
        # Request tracking for rate limiting
        self.requests_made = 0
        self.last_request_time = None
        self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Headers for API requests
        self.headers = {
            'X-RapidAPI-Key': self.api_key,
            'X-RapidAPI-Host': 'v3.football.api-sports.io'
        }
        
        self.logger.info("API-Football client initialized")
    
    def _make_request(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make API request with rate limiting and error handling.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            API response data
        """
        # Check rate limit
        self._check_rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        for attempt in range(self.retry_attempts):
            try:
                self.logger.debug(f"Making request to {endpoint} (attempt {attempt + 1})")
                
                response = requests.get(
                    url, 
                    headers=self.headers, 
                    params=params or {}, 
                    timeout=self.timeout
                )
                
                # Track request
                self.requests_made += 1
                self.last_request_time = datetime.now()
                
                # Check response status
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check API response structure
                    if 'response' in data:
                        return data
                    else:
                        self.logger.warning(f"Unexpected response structure: {data}")
                        return data
                
                elif response.status_code == 429:
                    # Rate limit exceeded
                    self.logger.warning("Rate limit exceeded, waiting...")
                    time.sleep(60)  # Wait 1 minute
                    continue
                
                elif response.status_code == 403:
                    self.logger.error("API key invalid or subscription expired")
                    raise Exception("API authentication failed")
                
                else:
                    self.logger.warning(f"API request failed with status {response.status_code}: {response.text}")
                    if attempt < self.retry_attempts - 1:
                        time.sleep(self.retry_delay * (attempt + 1))
                        continue
                    else:
                        raise Exception(f"API request failed: {response.status_code}")
                
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request exception on attempt {attempt + 1}: {e}")
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise
        
        raise Exception(f"Failed to make request after {self.retry_attempts} attempts")
    
    def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        now = datetime.now()
        
        # Reset daily counter if needed
        if now >= self.daily_reset_time + timedelta(days=1):
            self.requests_made = 0
            self.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Check if we've exceeded daily limit
        if self.requests_made >= self.rate_limit:
            wait_time = (self.daily_reset_time + timedelta(days=1) - now).total_seconds()
            if wait_time > 0:
                self.logger.warning(f"Daily rate limit exceeded. Waiting {wait_time:.0f} seconds until reset.")
                time.sleep(min(wait_time, 3600))  # Wait max 1 hour
    
    def get_leagues(self, country: Optional[str] = None, season: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get available leagues.
        
        Args:
            country: Filter by country
            season: Filter by season
            
        Returns:
            List of league data
        """
        params = {}
        if country:
            params['country'] = country
        if season:
            params['season'] = season
        
        response = self._make_request('leagues', params)
        return response.get('response', [])
    
    def get_teams(self, league_id: int, season: int) -> List[Dict[str, Any]]:
        """Get teams in a league for a season.
        
        Args:
            league_id: League ID
            season: Season year
            
        Returns:
            List of team data
        """
        params = {
            'league': league_id,
            'season': season
        }
        
        response = self._make_request('teams', params)
        return response.get('response', [])
    
    def get_fixtures(self, league_id: Optional[int] = None, season: Optional[int] = None,
                    team_id: Optional[int] = None, date_from: Optional[str] = None,
                    date_to: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get fixtures/matches.
        
        Args:
            league_id: League ID
            season: Season year
            team_id: Team ID
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            status: Match status (NS, 1H, HT, 2H, ET, P, FT, AET, PEN, BT, SUSP, INT, PST, CANC, ABD, AWD, WO)
            
        Returns:
            List of fixture data
        """
        params = {}
        if league_id:
            params['league'] = league_id
        if season:
            params['season'] = season
        if team_id:
            params['team'] = team_id
        if date_from:
            params['from'] = date_from
        if date_to:
            params['to'] = date_to
        if status:
            params['status'] = status
        
        response = self._make_request('fixtures', params)
        return response.get('response', [])
    
    def get_fixture_statistics(self, fixture_id: int) -> List[Dict[str, Any]]:
        """Get detailed statistics for a fixture.
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            List of team statistics for the fixture
        """
        params = {'fixture': fixture_id}
        
        response = self._make_request('fixtures/statistics', params)
        return response.get('response', [])
    
    def get_fixture_events(self, fixture_id: int) -> List[Dict[str, Any]]:
        """Get events (goals, cards, substitutions) for a fixture.
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            List of match events
        """
        params = {'fixture': fixture_id}
        
        response = self._make_request('fixtures/events', params)
        return response.get('response', [])
    
    def get_fixture_lineups(self, fixture_id: int) -> List[Dict[str, Any]]:
        """Get lineups for a fixture.
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            List of team lineups
        """
        params = {'fixture': fixture_id}
        
        response = self._make_request('fixtures/lineups', params)
        return response.get('response', [])
    
    def get_fixture_players(self, fixture_id: int) -> List[Dict[str, Any]]:
        """Get player statistics for a fixture.
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            List of player statistics
        """
        params = {'fixture': fixture_id}
        
        response = self._make_request('fixtures/players', params)
        return response.get('response', [])
    
    def get_players(self, team_id: Optional[int] = None, league_id: Optional[int] = None,
                   season: Optional[int] = None, search: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get players.
        
        Args:
            team_id: Team ID
            league_id: League ID
            season: Season year
            search: Player name search
            
        Returns:
            List of player data
        """
        params = {}
        if team_id:
            params['team'] = team_id
        if league_id:
            params['league'] = league_id
        if season:
            params['season'] = season
        if search:
            params['search'] = search
        
        response = self._make_request('players', params)
        return response.get('response', [])
    
    def get_injuries(self, league_id: Optional[int] = None, season: Optional[int] = None,
                    team_id: Optional[int] = None, player_id: Optional[int] = None,
                    date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get injury information.
        
        Args:
            league_id: League ID
            season: Season year
            team_id: Team ID
            player_id: Player ID
            date: Date (YYYY-MM-DD)
            
        Returns:
            List of injury data
        """
        params = {}
        if league_id:
            params['league'] = league_id
        if season:
            params['season'] = season
        if team_id:
            params['team'] = team_id
        if player_id:
            params['player'] = player_id
        if date:
            params['date'] = date
        
        response = self._make_request('injuries', params)
        return response.get('response', [])
    
    def get_team_statistics(self, team_id: int, league_id: int, season: int) -> Dict[str, Any]:
        """Get team statistics for a season.
        
        Args:
            team_id: Team ID
            league_id: League ID
            season: Season year
            
        Returns:
            Team statistics data
        """
        params = {
            'team': team_id,
            'league': league_id,
            'season': season
        }
        
        response = self._make_request('teams/statistics', params)
        return response.get('response', {})
    
    def get_standings(self, league_id: int, season: int) -> List[Dict[str, Any]]:
        """Get league standings.
        
        Args:
            league_id: League ID
            season: Season year
            
        Returns:
            List of standings data
        """
        params = {
            'league': league_id,
            'season': season
        }
        
        response = self._make_request('standings', params)
        return response.get('response', [])
    
    def get_head_to_head(self, team1_id: int, team2_id: int, 
                        last: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get head-to-head matches between two teams.
        
        Args:
            team1_id: First team ID
            team2_id: Second team ID
            last: Number of last matches to retrieve
            
        Returns:
            List of head-to-head matches
        """
        params = {
            'h2h': f"{team1_id}-{team2_id}"
        }
        if last:
            params['last'] = last
        
        response = self._make_request('fixtures/headtohead', params)
        return response.get('response', [])
    
    def get_predictions(self, fixture_id: int) -> Dict[str, Any]:
        """Get AI predictions for a fixture (if available).
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            Prediction data
        """
        params = {'fixture': fixture_id}
        
        response = self._make_request('predictions', params)
        return response.get('response', [])
    
    def get_odds(self, fixture_id: int, bookmaker_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get betting odds for a fixture.
        
        Args:
            fixture_id: Fixture ID
            bookmaker_id: Specific bookmaker ID
            
        Returns:
            List of odds data
        """
        params = {'fixture': fixture_id}
        if bookmaker_id:
            params['bookmaker'] = bookmaker_id
        
        response = self._make_request('odds', params)
        return response.get('response', [])
    
    def get_api_status(self) -> Dict[str, Any]:
        """Get API status and usage information.
        
        Returns:
            API status data
        """
        response = self._make_request('status')
        return response.get('response', {})
    
    def get_requests_remaining(self) -> int:
        """Get remaining API requests for today.
        
        Returns:
            Number of remaining requests
        """
        return max(0, self.rate_limit - self.requests_made)

    def get_comprehensive_match_data(self, fixture_id: int) -> Dict[str, Any]:
        """Get comprehensive data for a single match including all available information.

        Args:
            fixture_id: Fixture ID

        Returns:
            Comprehensive match data
        """
        self.logger.info(f"Collecting comprehensive data for fixture {fixture_id}")

        comprehensive_data = {
            'fixture_id': fixture_id,
            'basic_info': {},
            'statistics': [],
            'events': [],
            'lineups': [],
            'player_stats': [],
            'predictions': {},
            'odds': []
        }

        try:
            # Get basic fixture info
            fixtures = self.get_fixtures()
            fixture_info = next((f for f in fixtures if f['fixture']['id'] == fixture_id), None)
            if fixture_info:
                comprehensive_data['basic_info'] = fixture_info

            # Get match statistics
            comprehensive_data['statistics'] = self.get_fixture_statistics(fixture_id)

            # Get match events
            comprehensive_data['events'] = self.get_fixture_events(fixture_id)

            # Get lineups
            comprehensive_data['lineups'] = self.get_fixture_lineups(fixture_id)

            # Get player statistics
            comprehensive_data['player_stats'] = self.get_fixture_players(fixture_id)

            # Get predictions (if available)
            try:
                predictions = self.get_predictions(fixture_id)
                if predictions:
                    comprehensive_data['predictions'] = predictions[0] if isinstance(predictions, list) else predictions
            except Exception as e:
                self.logger.debug(f"No predictions available for fixture {fixture_id}: {e}")

            # Get odds (if available)
            try:
                comprehensive_data['odds'] = self.get_odds(fixture_id)
            except Exception as e:
                self.logger.debug(f"No odds available for fixture {fixture_id}: {e}")

            self.logger.info(f"Successfully collected comprehensive data for fixture {fixture_id}")

        except Exception as e:
            self.logger.error(f"Error collecting comprehensive data for fixture {fixture_id}: {e}")
            raise

        return comprehensive_data
