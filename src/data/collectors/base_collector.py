"""Base data collector class for the football prediction system."""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from src.utils.config import config


class BaseDataCollector(ABC):
    """Abstract base class for data collectors."""
    
    def __init__(self, name: str, rate_limit: int = 10):
        """Initialize base collector.
        
        Args:
            name: Name of the collector
            rate_limit: Maximum requests per minute
        """
        self.name = name
        self.rate_limit = rate_limit
        self.last_request_time = 0
        self.logger = logging.getLogger(f"collector.{name}")
        
        # Setup HTTP session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _rate_limit_wait(self):
        """Implement rate limiting."""
        if self.rate_limit <= 0:
            return
            
        time_since_last = time.time() - self.last_request_time
        min_interval = 60.0 / self.rate_limit  # seconds between requests
        
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.debug(f"Rate limiting: waiting {wait_time:.2f} seconds")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, url: str, headers: Optional[Dict] = None, 
                     params: Optional[Dict] = None, timeout: int = 30) -> requests.Response:
        """Make HTTP request with rate limiting and error handling.
        
        Args:
            url: Request URL
            headers: Request headers
            params: Request parameters
            timeout: Request timeout in seconds
            
        Returns:
            Response object
            
        Raises:
            requests.RequestException: If request fails
        """
        self._rate_limit_wait()
        
        try:
            response = self.session.get(
                url, 
                headers=headers, 
                params=params, 
                timeout=timeout
            )
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed for {url}: {e}")
            raise
    
    @abstractmethod
    def collect_leagues(self) -> List[Dict[str, Any]]:
        """Collect league/competition data.
        
        Returns:
            List of league dictionaries
        """
        pass
    
    @abstractmethod
    def collect_teams(self, league_id: int, season: str) -> List[Dict[str, Any]]:
        """Collect team data for a specific league and season.
        
        Args:
            league_id: League identifier
            season: Season string (e.g., "2023-24")
            
        Returns:
            List of team dictionaries
        """
        pass
    
    @abstractmethod
    def collect_matches(self, league_id: int, season: str, 
                       date_from: Optional[datetime] = None,
                       date_to: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Collect match data for a specific league and season.
        
        Args:
            league_id: League identifier
            season: Season string
            date_from: Start date for match collection
            date_to: End date for match collection
            
        Returns:
            List of match dictionaries
        """
        pass
    
    @abstractmethod
    def collect_players(self, team_id: int, season: str) -> List[Dict[str, Any]]:
        """Collect player data for a specific team and season.
        
        Args:
            team_id: Team identifier
            season: Season string
            
        Returns:
            List of player dictionaries
        """
        pass
    
    def collect_injuries(self, team_id: int) -> List[Dict[str, Any]]:
        """Collect injury data for a specific team.
        
        Args:
            team_id: Team identifier
            
        Returns:
            List of injury dictionaries
        """
        # Default implementation - can be overridden
        self.logger.warning(f"Injury collection not implemented for {self.name}")
        return []
    
    def collect_match_statistics(self, match_id: int) -> Optional[Dict[str, Any]]:
        """Collect detailed statistics for a specific match.
        
        Args:
            match_id: Match identifier
            
        Returns:
            Match statistics dictionary or None
        """
        # Default implementation - can be overridden
        self.logger.warning(f"Match statistics collection not implemented for {self.name}")
        return None
    
    def validate_data(self, data: Dict[str, Any], required_fields: List[str]) -> bool:
        """Validate that data contains required fields.
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            
        Returns:
            True if all required fields are present
        """
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        
        if missing_fields:
            self.logger.warning(f"Missing required fields: {missing_fields}")
            return False
        
        return True
    
    def normalize_team_name(self, name: str) -> str:
        """Normalize team name for consistency.
        
        Args:
            name: Original team name
            
        Returns:
            Normalized team name
        """
        # Basic normalization - can be extended
        return name.strip().title()
    
    def parse_date(self, date_str: str, format_str: str = "%Y-%m-%d") -> Optional[datetime]:
        """Parse date string to datetime object.
        
        Args:
            date_str: Date string
            format_str: Expected date format
            
        Returns:
            Datetime object or None if parsing fails
        """
        try:
            return datetime.strptime(date_str, format_str)
        except (ValueError, TypeError) as e:
            self.logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None
    
    def get_current_season(self) -> str:
        """Get current season string based on current date.
        
        Returns:
            Season string (e.g., "2023-24")
        """
        now = datetime.now()
        
        # Football seasons typically start in August
        if now.month >= 8:
            start_year = now.year
            end_year = now.year + 1
        else:
            start_year = now.year - 1
            end_year = now.year
        
        return f"{start_year}-{str(end_year)[-2:]}"
    
    def get_season_date_range(self, season: str) -> tuple[datetime, datetime]:
        """Get date range for a season.
        
        Args:
            season: Season string (e.g., "2023-24")
            
        Returns:
            Tuple of (start_date, end_date)
        """
        start_year = int(season.split('-')[0])
        end_year = int(f"20{season.split('-')[1]}")
        
        start_date = datetime(start_year, 8, 1)  # August 1st
        end_date = datetime(end_year, 7, 31)    # July 31st
        
        return start_date, end_date
