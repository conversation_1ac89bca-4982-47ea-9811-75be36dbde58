"""Injury data collector using web scraping from multiple sources."""

import re
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import requests

from .base_collector import BaseDataCollector
from src.utils.config import config


class InjuryDataCollector(BaseDataCollector):
    """Collector for player injury data from various sources."""
    
    def __init__(self):
        """Initialize injury data collector."""
        super().__init__(name="injury_collector", rate_limit=30)  # More conservative rate limit
        
        # Sources for injury data
        self.sources = {
            'transfermarkt': {
                'base_url': 'https://www.transfermarkt.com',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            'physioroom': {
                'base_url': 'https://www.physioroom.com',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        }
        
        # Team name mappings for different sources
        self.team_mappings = self._load_team_mappings()
    
    def _load_team_mappings(self) -> Dict[str, Dict[str, str]]:
        """Load team name mappings for different sources."""
        # This would typically be loaded from a configuration file
        return {
            'transfermarkt': {
                'Manchester United': 'manchester-united',
                'Manchester City': 'manchester-city',
                'Liverpool': 'fc-liverpool',
                'Chelsea': 'fc-chelsea',
                'Arsenal': 'fc-arsenal',
                'Tottenham': 'tottenham-hotspur',
                # Add more mappings as needed
            },
            'physioroom': {
                'Manchester United': 'manchester-united',
                'Manchester City': 'manchester-city',
                'Liverpool': 'liverpool',
                'Chelsea': 'chelsea',
                'Arsenal': 'arsenal',
                'Tottenham': 'tottenham',
                # Add more mappings as needed
            }
        }
    
    def collect_leagues(self) -> List[Dict[str, Any]]:
        """Not applicable for injury collector."""
        return []
    
    def collect_teams(self, league_id: int, season: str) -> List[Dict[str, Any]]:
        """Not applicable for injury collector."""
        return []
    
    def collect_matches(self, league_id: int, season: str,
                       date_from: Optional[datetime] = None,
                       date_to: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Not applicable for injury collector."""
        return []
    
    def collect_players(self, team_id: int, season: str) -> List[Dict[str, Any]]:
        """Not applicable for injury collector."""
        return []
    
    def collect_injuries(self, team_name: str, source: str = 'physioroom') -> List[Dict[str, Any]]:
        """Collect injury data for a specific team.
        
        Args:
            team_name: Team name
            source: Data source ('physioroom' or 'transfermarkt')
            
        Returns:
            List of injury dictionaries
        """
        if source == 'physioroom':
            return self._collect_physioroom_injuries(team_name)
        elif source == 'transfermarkt':
            return self._collect_transfermarkt_injuries(team_name)
        else:
            self.logger.error(f"Unknown injury data source: {source}")
            return []
    
    def _collect_physioroom_injuries(self, team_name: str) -> List[Dict[str, Any]]:
        """Collect injuries from Physioroom.com.
        
        Args:
            team_name: Team name
            
        Returns:
            List of injury dictionaries
        """
        # Map team name to URL format
        team_slug = self.team_mappings.get('physioroom', {}).get(team_name)
        if not team_slug:
            self.logger.warning(f"No team mapping found for {team_name} in Physioroom")
            return []
        
        url = f"{self.sources['physioroom']['base_url']}/news/{team_slug}_injury_news.php"
        headers = {'User-Agent': self.sources['physioroom']['user_agent']}
        
        try:
            response = self._make_request(url, headers=headers)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            injuries = []
            
            # Find injury table (structure may vary)
            injury_tables = soup.find_all('table', class_='injury_table')
            
            for table in injury_tables:
                rows = table.find_all('tr')[1:]  # Skip header row
                
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 4:
                        player_name = cells[0].get_text(strip=True)
                        injury_type = cells[1].get_text(strip=True)
                        injury_date_str = cells[2].get_text(strip=True)
                        return_date_str = cells[3].get_text(strip=True)
                        
                        # Parse dates
                        injury_date = self._parse_injury_date(injury_date_str)
                        return_date = self._parse_return_date(return_date_str)
                        
                        injury_data = {
                            'player_name': player_name,
                            'injury_type': injury_type,
                            'injury_date': injury_date,
                            'expected_return_date': return_date,
                            'status': 'Active' if return_date and return_date > datetime.now() else 'Unknown',
                            'severity': self._classify_injury_severity(injury_type, return_date),
                            'source': 'physioroom',
                            'team_name': team_name,
                            'collected_at': datetime.now()
                        }
                        injuries.append(injury_data)
            
            self.logger.info(f"Collected {len(injuries)} injuries for {team_name} from Physioroom")
            return injuries
            
        except Exception as e:
            self.logger.error(f"Failed to collect Physioroom injuries for {team_name}: {e}")
            return []
    
    def _collect_transfermarkt_injuries(self, team_name: str) -> List[Dict[str, Any]]:
        """Collect injuries from Transfermarkt.
        
        Args:
            team_name: Team name
            
        Returns:
            List of injury dictionaries
        """
        # Map team name to URL format
        team_slug = self.team_mappings.get('transfermarkt', {}).get(team_name)
        if not team_slug:
            self.logger.warning(f"No team mapping found for {team_name} in Transfermarkt")
            return []
        
        url = f"{self.sources['transfermarkt']['base_url']}/{team_slug}/verletztenspieler/verein/11"
        headers = {'User-Agent': self.sources['transfermarkt']['user_agent']}
        
        try:
            response = self._make_request(url, headers=headers)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            injuries = []
            
            # Find injury information (Transfermarkt structure)
            injury_rows = soup.find_all('tr', class_=['odd', 'even'])
            
            for row in injury_rows:
                cells = row.find_all('td')
                if len(cells) >= 5:
                    # Extract player information
                    player_cell = cells[0]
                    player_name = player_cell.find('a').get_text(strip=True) if player_cell.find('a') else ''
                    
                    # Extract injury information
                    injury_cell = cells[2]
                    injury_type = injury_cell.get_text(strip=True)
                    
                    # Extract dates
                    injury_date_str = cells[3].get_text(strip=True)
                    return_date_str = cells[4].get_text(strip=True)
                    
                    injury_date = self._parse_injury_date(injury_date_str)
                    return_date = self._parse_return_date(return_date_str)
                    
                    injury_data = {
                        'player_name': player_name,
                        'injury_type': injury_type,
                        'injury_date': injury_date,
                        'expected_return_date': return_date,
                        'status': 'Active' if return_date and return_date > datetime.now() else 'Unknown',
                        'severity': self._classify_injury_severity(injury_type, return_date),
                        'source': 'transfermarkt',
                        'team_name': team_name,
                        'collected_at': datetime.now()
                    }
                    injuries.append(injury_data)
            
            self.logger.info(f"Collected {len(injuries)} injuries for {team_name} from Transfermarkt")
            return injuries
            
        except Exception as e:
            self.logger.error(f"Failed to collect Transfermarkt injuries for {team_name}: {e}")
            return []
    
    def _parse_injury_date(self, date_str: str) -> Optional[datetime]:
        """Parse injury date from various formats.
        
        Args:
            date_str: Date string
            
        Returns:
            Parsed datetime or None
        """
        if not date_str or date_str.lower() in ['unknown', '-', '']:
            return None
        
        # Try different date formats
        formats = [
            '%d/%m/%Y',
            '%d.%m.%Y',
            '%Y-%m-%d',
            '%d %b %Y',
            '%d %B %Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        # Try relative dates like "2 weeks ago"
        relative_match = re.search(r'(\d+)\s+(day|week|month)s?\s+ago', date_str.lower())
        if relative_match:
            amount = int(relative_match.group(1))
            unit = relative_match.group(2)
            
            if unit == 'day':
                return datetime.now() - timedelta(days=amount)
            elif unit == 'week':
                return datetime.now() - timedelta(weeks=amount)
            elif unit == 'month':
                return datetime.now() - timedelta(days=amount * 30)
        
        self.logger.warning(f"Could not parse injury date: {date_str}")
        return None
    
    def _parse_return_date(self, date_str: str) -> Optional[datetime]:
        """Parse expected return date from various formats.
        
        Args:
            date_str: Date string
            
        Returns:
            Parsed datetime or None
        """
        if not date_str or date_str.lower() in ['unknown', '-', '', 'doubtful']:
            return None
        
        # Handle "End of season", "2-3 weeks", etc.
        if 'end of season' in date_str.lower():
            # Assume end of May for European seasons
            current_year = datetime.now().year
            if datetime.now().month < 6:
                return datetime(current_year, 5, 31)
            else:
                return datetime(current_year + 1, 5, 31)
        
        # Handle duration like "2-3 weeks"
        duration_match = re.search(r'(\d+)[-–]?(\d+)?\s+(day|week|month)s?', date_str.lower())
        if duration_match:
            min_amount = int(duration_match.group(1))
            max_amount = int(duration_match.group(2)) if duration_match.group(2) else min_amount
            unit = duration_match.group(3)
            
            # Use the maximum duration for conservative estimate
            if unit == 'day':
                return datetime.now() + timedelta(days=max_amount)
            elif unit == 'week':
                return datetime.now() + timedelta(weeks=max_amount)
            elif unit == 'month':
                return datetime.now() + timedelta(days=max_amount * 30)
        
        # Try standard date parsing
        return self._parse_injury_date(date_str)
    
    def _classify_injury_severity(self, injury_type: str, return_date: Optional[datetime]) -> str:
        """Classify injury severity based on type and expected return date.
        
        Args:
            injury_type: Type of injury
            return_date: Expected return date
            
        Returns:
            Severity classification ('Minor', 'Moderate', 'Severe')
        """
        if not return_date:
            return 'Unknown'
        
        days_out = (return_date - datetime.now()).days
        
        # Classify by duration
        if days_out <= 7:
            return 'Minor'
        elif days_out <= 30:
            return 'Moderate'
        else:
            return 'Severe'
    
    def collect_all_team_injuries(self, team_names: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """Collect injuries for multiple teams.
        
        Args:
            team_names: List of team names
            
        Returns:
            Dictionary mapping team names to injury lists
        """
        all_injuries = {}
        
        for team_name in team_names:
            self.logger.info(f"Collecting injuries for {team_name}")
            
            # Try both sources
            physioroom_injuries = self.collect_injuries(team_name, 'physioroom')
            transfermarkt_injuries = self.collect_injuries(team_name, 'transfermarkt')
            
            # Combine and deduplicate
            combined_injuries = self._deduplicate_injuries(
                physioroom_injuries + transfermarkt_injuries
            )
            
            all_injuries[team_name] = combined_injuries
        
        return all_injuries
    
    def _deduplicate_injuries(self, injuries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate injuries based on player name and injury type.
        
        Args:
            injuries: List of injury dictionaries
            
        Returns:
            Deduplicated list of injuries
        """
        seen = set()
        deduplicated = []
        
        for injury in injuries:
            key = (injury['player_name'].lower(), injury['injury_type'].lower())
            if key not in seen:
                seen.add(key)
                deduplicated.append(injury)
        
        return deduplicated
