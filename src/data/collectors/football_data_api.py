"""Football Data API collector for match data, teams, and statistics."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .base_collector import BaseDataCollector
from src.utils.config import config


class FootballDataAPICollector(BaseDataCollector):
    """Collector for Football Data API (football-data.org)."""
    
    def __init__(self):
        """Initialize Football Data API collector."""
        api_config = config.get('data_sources.football_data_api', {})
        super().__init__(
            name="football_data_api",
            rate_limit=api_config.get('rate_limit', 10)
        )
        
        self.base_url = api_config.get('base_url', 'https://api.football-data.org/v4')
        self.api_key = api_config.get('api_key')
        
        if not self.api_key:
            self.logger.warning("No API key configured for Football Data API")
        
        # Set up headers
        self.headers = {
            'X-Auth-Token': self.api_key,
            'Content-Type': 'application/json'
        }
        
        # League ID mapping
        self.league_mapping = {
            'premier_league': 'PL',
            'la_liga': 'PD',
            'bundesliga': 'BL1',
            'serie_a': 'SA',
            'ligue_1': 'FL1',
            'champions_league': 'CL',
            'europa_league': 'EL'
        }
    
    def collect_leagues(self) -> List[Dict[str, Any]]:
        """Collect available competitions/leagues.
        
        Returns:
            List of league dictionaries
        """
        url = f"{self.base_url}/competitions"
        
        try:
            response = self._make_request(url, headers=self.headers)
            data = response.json()
            
            leagues = []
            for competition in data.get('competitions', []):
                league_data = {
                    'external_id': competition['id'],
                    'name': competition['name'],
                    'code': competition['code'],
                    'country': competition.get('area', {}).get('name', ''),
                    'type': competition.get('type', ''),
                    'current_season': competition.get('currentSeason', {})
                }
                leagues.append(league_data)
            
            self.logger.info(f"Collected {len(leagues)} leagues")
            return leagues
            
        except Exception as e:
            self.logger.error(f"Failed to collect leagues: {e}")
            return []
    
    def collect_teams(self, league_code: str, season: str) -> List[Dict[str, Any]]:
        """Collect teams for a specific league and season.
        
        Args:
            league_code: League code (e.g., 'PL' for Premier League)
            season: Season year (e.g., '2023')
            
        Returns:
            List of team dictionaries
        """
        url = f"{self.base_url}/competitions/{league_code}/teams"
        params = {'season': season}
        
        try:
            response = self._make_request(url, headers=self.headers, params=params)
            data = response.json()
            
            teams = []
            for team in data.get('teams', []):
                team_data = {
                    'external_id': team['id'],
                    'name': team['name'],
                    'short_name': team.get('shortName', ''),
                    'tla': team.get('tla', ''),  # Three Letter Abbreviation
                    'logo_url': team.get('crest', ''),
                    'founded': team.get('founded'),
                    'venue_name': team.get('venue', ''),
                    'website': team.get('website', ''),
                    'country': team.get('area', {}).get('name', ''),
                    'colors': team.get('clubColors', ''),
                    'league_code': league_code
                }
                teams.append(team_data)
            
            self.logger.info(f"Collected {len(teams)} teams for {league_code} {season}")
            return teams
            
        except Exception as e:
            self.logger.error(f"Failed to collect teams for {league_code}: {e}")
            return []
    
    def collect_matches(self, league_code: str, season: str,
                       date_from: Optional[datetime] = None,
                       date_to: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Collect matches for a specific league and season.
        
        Args:
            league_code: League code
            season: Season year
            date_from: Start date for match collection
            date_to: End date for match collection
            
        Returns:
            List of match dictionaries
        """
        url = f"{self.base_url}/competitions/{league_code}/matches"
        params = {'season': season}
        
        if date_from:
            params['dateFrom'] = date_from.strftime('%Y-%m-%d')
        if date_to:
            params['dateTo'] = date_to.strftime('%Y-%m-%d')
        
        try:
            response = self._make_request(url, headers=self.headers, params=params)
            data = response.json()
            
            matches = []
            for match in data.get('matches', []):
                # Parse match date
                match_date = self.parse_date(
                    match['utcDate'], 
                    "%Y-%m-%dT%H:%M:%SZ"
                )
                
                match_data = {
                    'external_id': match['id'],
                    'league_code': league_code,
                    'season': season,
                    'matchday': match.get('matchday'),
                    'home_team_id': match['homeTeam']['id'],
                    'away_team_id': match['awayTeam']['id'],
                    'home_team_name': match['homeTeam']['name'],
                    'away_team_name': match['awayTeam']['name'],
                    'match_date': match_date,
                    'status': match['status'],
                    'stage': match.get('stage', ''),
                    'group': match.get('group'),
                    'referee': match.get('referees', [{}])[0].get('name') if match.get('referees') else None,
                    'venue': match.get('venue'),
                    'home_score': None,
                    'away_score': None,
                    'home_score_ht': None,
                    'away_score_ht': None
                }
                
                # Add scores if match is finished
                if match['status'] == 'FINISHED' and match.get('score'):
                    score = match['score']
                    if score.get('fullTime'):
                        match_data['home_score'] = score['fullTime'].get('home')
                        match_data['away_score'] = score['fullTime'].get('away')
                    if score.get('halfTime'):
                        match_data['home_score_ht'] = score['halfTime'].get('home')
                        match_data['away_score_ht'] = score['halfTime'].get('away')
                
                matches.append(match_data)
            
            self.logger.info(f"Collected {len(matches)} matches for {league_code} {season}")
            return matches
            
        except Exception as e:
            self.logger.error(f"Failed to collect matches for {league_code}: {e}")
            return []
    
    def collect_players(self, team_id: int, season: str) -> List[Dict[str, Any]]:
        """Collect players for a specific team.
        
        Args:
            team_id: Team identifier
            season: Season year
            
        Returns:
            List of player dictionaries
        """
        url = f"{self.base_url}/teams/{team_id}"
        
        try:
            response = self._make_request(url, headers=self.headers)
            data = response.json()
            
            players = []
            for player in data.get('squad', []):
                # Parse date of birth
                date_of_birth = None
                if player.get('dateOfBirth'):
                    date_of_birth = self.parse_date(player['dateOfBirth'])
                
                player_data = {
                    'external_id': player['id'],
                    'name': player['name'],
                    'position': player.get('position'),
                    'date_of_birth': date_of_birth,
                    'nationality': player.get('nationality'),
                    'shirt_number': player.get('shirtNumber'),
                    'team_id': team_id,
                    'season': season
                }
                players.append(player_data)
            
            self.logger.info(f"Collected {len(players)} players for team {team_id}")
            return players
            
        except Exception as e:
            self.logger.error(f"Failed to collect players for team {team_id}: {e}")
            return []
    
    def collect_standings(self, league_code: str, season: str) -> List[Dict[str, Any]]:
        """Collect league standings.
        
        Args:
            league_code: League code
            season: Season year
            
        Returns:
            List of team standings
        """
        url = f"{self.base_url}/competitions/{league_code}/standings"
        params = {'season': season}
        
        try:
            response = self._make_request(url, headers=self.headers, params=params)
            data = response.json()
            
            standings = []
            for standing_type in data.get('standings', []):
                if standing_type['type'] == 'TOTAL':
                    for team_standing in standing_type['table']:
                        standing_data = {
                            'team_id': team_standing['team']['id'],
                            'team_name': team_standing['team']['name'],
                            'position': team_standing['position'],
                            'points': team_standing['points'],
                            'played_games': team_standing['playedGames'],
                            'won': team_standing['won'],
                            'draw': team_standing['draw'],
                            'lost': team_standing['lost'],
                            'goals_for': team_standing['goalsFor'],
                            'goals_against': team_standing['goalsAgainst'],
                            'goal_difference': team_standing['goalDifference'],
                            'form': team_standing.get('form'),
                            'league_code': league_code,
                            'season': season
                        }
                        standings.append(standing_data)
            
            self.logger.info(f"Collected standings for {league_code} {season}")
            return standings
            
        except Exception as e:
            self.logger.error(f"Failed to collect standings for {league_code}: {e}")
            return []
    
    def collect_match_details(self, match_id: int) -> Optional[Dict[str, Any]]:
        """Collect detailed information for a specific match.
        
        Args:
            match_id: Match identifier
            
        Returns:
            Detailed match data or None
        """
        url = f"{self.base_url}/matches/{match_id}"
        
        try:
            response = self._make_request(url, headers=self.headers)
            data = response.json()
            
            match_details = {
                'match_id': match_id,
                'attendance': data.get('attendance'),
                'referee': data.get('referees', [{}])[0].get('name') if data.get('referees') else None,
                'venue': data.get('venue'),
                'weather': data.get('weather'),  # Not typically available in this API
                'goals': data.get('goals', []),
                'bookings': data.get('bookings', []),
                'substitutions': data.get('substitutions', [])
            }
            
            return match_details
            
        except Exception as e:
            self.logger.error(f"Failed to collect match details for {match_id}: {e}")
            return None
