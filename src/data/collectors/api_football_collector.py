"""Comprehensive API-Football data collector."""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import time

from .api_football_client import APIFootballClient
from .base_collector import BaseDataCollector
from src.utils.config import config


class APIFootballCollector(BaseDataCollector):
    """Comprehensive data collector using API-Football."""
    
    def __init__(self):
        """Initialize API-Football collector."""
        super().__init__("api_football")
        self.client = APIFootballClient()
        
        # Get league configurations
        self.leagues_config = config.get_leagues()
        
        # Current season (adjust based on your needs)
        self.current_season = datetime.now().year
        if datetime.now().month < 8:  # Before August, use previous year
            self.current_season -= 1
    
    def collect_leagues(self) -> List[Dict[str, Any]]:
        """Collect league information.
        
        Returns:
            List of league data
        """
        self.logger.info("Collecting league information from API-Football")
        
        leagues_data = []
        
        try:
            # Get all leagues for current season
            all_leagues = self.client.get_leagues(season=self.current_season)
            
            # Filter to configured leagues
            configured_league_ids = [league['id'] for league in self.leagues_config.values()]
            
            for league_data in all_leagues:
                league_info = league_data.get('league', {})
                league_id = league_info.get('id')
                
                if league_id in configured_league_ids:
                    processed_league = {
                        'external_id': league_id,
                        'name': league_info.get('name'),
                        'country': league_data.get('country', {}).get('name'),
                        'logo': league_info.get('logo'),
                        'type': league_info.get('type'),
                        'current_season': self.current_season,
                        'api_source': 'api_football'
                    }
                    leagues_data.append(processed_league)
            
            self.logger.info(f"Collected {len(leagues_data)} leagues")
            
        except Exception as e:
            self.logger.error(f"Error collecting leagues: {e}")
            raise
        
        return leagues_data
    
    def collect_teams(self, league_id: int, season: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect teams for a specific league.
        
        Args:
            league_id: League ID
            season: Season year (defaults to current season)
            
        Returns:
            List of team data
        """
        season = season or self.current_season
        self.logger.info(f"Collecting teams for league {league_id}, season {season}")
        
        teams_data = []
        
        try:
            teams_response = self.client.get_teams(league_id, season)
            
            for team_data in teams_response:
                team_info = team_data.get('team', {})
                venue_info = team_data.get('venue', {})
                
                processed_team = {
                    'external_id': team_info.get('id'),
                    'name': team_info.get('name'),
                    'short_name': team_info.get('code'),
                    'logo': team_info.get('logo'),
                    'founded': team_info.get('founded'),
                    'national': team_info.get('national', False),
                    'country': team_info.get('country'),
                    'league_id': league_id,
                    'venue_name': venue_info.get('name'),
                    'venue_address': venue_info.get('address'),
                    'venue_city': venue_info.get('city'),
                    'venue_capacity': venue_info.get('capacity'),
                    'venue_surface': venue_info.get('surface'),
                    'venue_image': venue_info.get('image'),
                    'api_source': 'api_football'
                }
                teams_data.append(processed_team)
            
            self.logger.info(f"Collected {len(teams_data)} teams for league {league_id}")
            
        except Exception as e:
            self.logger.error(f"Error collecting teams for league {league_id}: {e}")
            raise
        
        return teams_data
    
    def collect_matches(self, league_id: int, season: Optional[int] = None,
                       date_from: Optional[str] = None, date_to: Optional[str] = None,
                       team_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect matches for a league/team.
        
        Args:
            league_id: League ID
            season: Season year
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            team_id: Specific team ID
            
        Returns:
            List of match data
        """
        season = season or self.current_season
        self.logger.info(f"Collecting matches for league {league_id}, season {season}")
        
        matches_data = []
        
        try:
            fixtures = self.client.get_fixtures(
                league_id=league_id,
                season=season,
                team_id=team_id,
                date_from=date_from,
                date_to=date_to
            )
            
            for fixture in fixtures:
                fixture_info = fixture.get('fixture', {})
                league_info = fixture.get('league', {})
                teams_info = fixture.get('teams', {})
                goals_info = fixture.get('goals', {})
                score_info = fixture.get('score', {})
                
                processed_match = {
                    'external_id': fixture_info.get('id'),
                    'match_date': fixture_info.get('date'),
                    'timestamp': fixture_info.get('timestamp'),
                    'timezone': fixture_info.get('timezone'),
                    'status': fixture_info.get('status', {}).get('short'),
                    'status_long': fixture_info.get('status', {}).get('long'),
                    'elapsed': fixture_info.get('status', {}).get('elapsed'),
                    'round': league_info.get('round'),
                    'season': league_info.get('season'),
                    'league_id': league_info.get('id'),
                    'home_team_id': teams_info.get('home', {}).get('id'),
                    'away_team_id': teams_info.get('away', {}).get('id'),
                    'home_team_name': teams_info.get('home', {}).get('name'),
                    'away_team_name': teams_info.get('away', {}).get('name'),
                    'venue': fixture_info.get('venue', {}).get('name'),
                    'venue_city': fixture_info.get('venue', {}).get('city'),
                    'referee': fixture_info.get('referee'),
                    'home_score': goals_info.get('home'),
                    'away_score': goals_info.get('away'),
                    'home_score_ht': score_info.get('halftime', {}).get('home'),
                    'away_score_ht': score_info.get('halftime', {}).get('away'),
                    'home_score_ft': score_info.get('fulltime', {}).get('home'),
                    'away_score_ft': score_info.get('fulltime', {}).get('away'),
                    'home_score_et': score_info.get('extratime', {}).get('home'),
                    'away_score_et': score_info.get('extratime', {}).get('away'),
                    'home_score_pen': score_info.get('penalty', {}).get('home'),
                    'away_score_pen': score_info.get('penalty', {}).get('away'),
                    'api_source': 'api_football'
                }
                matches_data.append(processed_match)
            
            self.logger.info(f"Collected {len(matches_data)} matches for league {league_id}")
            
        except Exception as e:
            self.logger.error(f"Error collecting matches for league {league_id}: {e}")
            raise
        
        return matches_data
    
    def collect_players(self, team_id: int, league_id: int, season: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect players for a specific team.
        
        Args:
            team_id: Team ID
            league_id: League ID
            season: Season year
            
        Returns:
            List of player data
        """
        season = season or self.current_season
        self.logger.info(f"Collecting players for team {team_id}")
        
        players_data = []
        
        try:
            players_response = self.client.get_players(
                team_id=team_id,
                league_id=league_id,
                season=season
            )
            
            for player_data in players_response:
                player_info = player_data.get('player', {})
                statistics = player_data.get('statistics', [])
                
                # Get the most relevant statistics (usually the first one for the league)
                main_stats = statistics[0] if statistics else {}
                team_info = main_stats.get('team', {})
                games_info = main_stats.get('games', {})
                
                processed_player = {
                    'external_id': player_info.get('id'),
                    'name': player_info.get('name'),
                    'firstname': player_info.get('firstname'),
                    'lastname': player_info.get('lastname'),
                    'age': player_info.get('age'),
                    'birth_date': player_info.get('birth', {}).get('date'),
                    'birth_place': player_info.get('birth', {}).get('place'),
                    'birth_country': player_info.get('birth', {}).get('country'),
                    'nationality': player_info.get('nationality'),
                    'height': player_info.get('height'),
                    'weight': player_info.get('weight'),
                    'injured': player_info.get('injured', False),
                    'photo': player_info.get('photo'),
                    'team_id': team_info.get('id'),
                    'position': games_info.get('position'),
                    'rating': games_info.get('rating'),
                    'captain': games_info.get('captain', False),
                    'api_source': 'api_football'
                }
                players_data.append(processed_player)
            
            self.logger.info(f"Collected {len(players_data)} players for team {team_id}")
            
        except Exception as e:
            self.logger.error(f"Error collecting players for team {team_id}: {e}")
            raise
        
        return players_data
    
    def collect_injuries(self, league_id: int, season: Optional[int] = None,
                        team_id: Optional[int] = None, date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Collect injury information.
        
        Args:
            league_id: League ID
            season: Season year
            team_id: Specific team ID
            date: Specific date (YYYY-MM-DD)
            
        Returns:
            List of injury data
        """
        season = season or self.current_season
        self.logger.info(f"Collecting injuries for league {league_id}")
        
        injuries_data = []
        
        try:
            injuries_response = self.client.get_injuries(
                league_id=league_id,
                season=season,
                team_id=team_id,
                date=date
            )
            
            for injury_data in injuries_response:
                player_info = injury_data.get('player', {})
                team_info = injury_data.get('team', {})
                fixture_info = injury_data.get('fixture', {})
                league_info = injury_data.get('league', {})
                
                processed_injury = {
                    'player_id': player_info.get('id'),
                    'player_name': player_info.get('name'),
                    'player_photo': player_info.get('photo'),
                    'team_id': team_info.get('id'),
                    'team_name': team_info.get('name'),
                    'team_logo': team_info.get('logo'),
                    'fixture_id': fixture_info.get('id'),
                    'league_id': league_info.get('id'),
                    'season': league_info.get('season'),
                    'injury_type': injury_data.get('player', {}).get('type'),
                    'injury_reason': injury_data.get('player', {}).get('reason'),
                    'api_source': 'api_football'
                }
                injuries_data.append(processed_injury)
            
            self.logger.info(f"Collected {len(injuries_data)} injuries for league {league_id}")
            
        except Exception as e:
            self.logger.error(f"Error collecting injuries for league {league_id}: {e}")
            raise
        
        return injuries_data
    
    def collect_match_statistics(self, fixture_id: int) -> Dict[str, Any]:
        """Collect detailed match statistics.
        
        Args:
            fixture_id: Fixture ID
            
        Returns:
            Match statistics data
        """
        self.logger.info(f"Collecting statistics for fixture {fixture_id}")
        
        try:
            statistics = self.client.get_fixture_statistics(fixture_id)
            events = self.client.get_fixture_events(fixture_id)
            lineups = self.client.get_fixture_lineups(fixture_id)
            player_stats = self.client.get_fixture_players(fixture_id)
            
            return {
                'fixture_id': fixture_id,
                'statistics': statistics,
                'events': events,
                'lineups': lineups,
                'player_stats': player_stats,
                'api_source': 'api_football'
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting statistics for fixture {fixture_id}: {e}")
            raise
    
    def collect_team_statistics(self, team_id: int, league_id: int, season: Optional[int] = None) -> Dict[str, Any]:
        """Collect team statistics for a season.
        
        Args:
            team_id: Team ID
            league_id: League ID
            season: Season year
            
        Returns:
            Team statistics data
        """
        season = season or self.current_season
        self.logger.info(f"Collecting statistics for team {team_id}")
        
        try:
            team_stats = self.client.get_team_statistics(team_id, league_id, season)
            return {
                'team_id': team_id,
                'league_id': league_id,
                'season': season,
                'statistics': team_stats,
                'api_source': 'api_football'
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting statistics for team {team_id}: {e}")
            raise
    
    def collect_head_to_head(self, team1_id: int, team2_id: int, last: int = 10) -> List[Dict[str, Any]]:
        """Collect head-to-head matches between two teams.
        
        Args:
            team1_id: First team ID
            team2_id: Second team ID
            last: Number of recent matches
            
        Returns:
            List of head-to-head matches
        """
        self.logger.info(f"Collecting H2H between teams {team1_id} and {team2_id}")
        
        try:
            h2h_matches = self.client.get_head_to_head(team1_id, team2_id, last)
            
            processed_matches = []
            for match in h2h_matches:
                # Process similar to collect_matches
                fixture_info = match.get('fixture', {})
                teams_info = match.get('teams', {})
                goals_info = match.get('goals', {})
                
                processed_match = {
                    'external_id': fixture_info.get('id'),
                    'match_date': fixture_info.get('date'),
                    'home_team_id': teams_info.get('home', {}).get('id'),
                    'away_team_id': teams_info.get('away', {}).get('id'),
                    'home_score': goals_info.get('home'),
                    'away_score': goals_info.get('away'),
                    'status': fixture_info.get('status', {}).get('short'),
                    'api_source': 'api_football'
                }
                processed_matches.append(processed_match)
            
            self.logger.info(f"Collected {len(processed_matches)} H2H matches")
            return processed_matches
            
        except Exception as e:
            self.logger.error(f"Error collecting H2H for teams {team1_id}-{team2_id}: {e}")
            raise
