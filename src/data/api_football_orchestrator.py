"""Enhanced data collection orchestrator using API-Football."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import time

from .collectors.api_football_collector import APIFootballCollector
from src.utils.database import DatabaseManager
from src.utils.config import config


class APIFootballOrchestrator:
    """Orchestrates comprehensive data collection using API-Football."""
    
    def __init__(self):
        """Initialize API-Football orchestrator."""
        self.logger = logging.getLogger("api_football_orchestrator")
        self.collector = APIFootballCollector()
        self.db_manager = DatabaseManager()
        
        # Get configuration
        self.leagues_config = config.get_leagues()
        self.current_season = datetime.now().year
        if datetime.now().month < 8:  # Before August, use previous year
            self.current_season -= 1
    
    def collect_all_data(self, leagues: Optional[List[str]] = None, 
                        include_historical: bool = True,
                        days_back: int = 30) -> Dict[str, Any]:
        """Collect comprehensive data for specified leagues.
        
        Args:
            leagues: List of league names to collect (None for all configured)
            include_historical: Whether to collect historical data
            days_back: Number of days back to collect historical data
            
        Returns:
            Collection results summary
        """
        self.logger.info("Starting comprehensive data collection with API-Football")
        
        results = {
            'leagues_collected': 0,
            'teams_collected': 0,
            'matches_collected': 0,
            'players_collected': 0,
            'injuries_collected': 0,
            'statistics_collected': 0,
            'errors': []
        }
        
        try:
            # Determine which leagues to collect
            target_leagues = leagues or list(self.leagues_config.keys())
            
            for league_name in target_leagues:
                if league_name not in self.leagues_config:
                    self.logger.warning(f"League {league_name} not configured, skipping")
                    continue
                
                league_config = self.leagues_config[league_name]
                league_id = league_config['id']
                
                try:
                    self.logger.info(f"Collecting data for {league_name} (ID: {league_id})")
                    
                    # Collect league data
                    league_results = self._collect_league_data(
                        league_id, league_name, include_historical, days_back
                    )
                    
                    # Update results
                    results['leagues_collected'] += 1
                    results['teams_collected'] += league_results.get('teams', 0)
                    results['matches_collected'] += league_results.get('matches', 0)
                    results['players_collected'] += league_results.get('players', 0)
                    results['injuries_collected'] += league_results.get('injuries', 0)
                    results['statistics_collected'] += league_results.get('statistics', 0)
                    
                    self.logger.info(f"Completed data collection for {league_name}")
                    
                    # Rate limiting - wait between leagues
                    time.sleep(2)
                    
                except Exception as e:
                    error_msg = f"Error collecting data for {league_name}: {e}"
                    self.logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            self.logger.info(f"Data collection completed. Summary: {results}")
            
        except Exception as e:
            error_msg = f"Critical error in data collection: {e}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def _collect_league_data(self, league_id: int, league_name: str,
                           include_historical: bool, days_back: int) -> Dict[str, int]:
        """Collect all data for a specific league.
        
        Args:
            league_id: League ID
            league_name: League name
            include_historical: Whether to collect historical data
            days_back: Days back for historical data
            
        Returns:
            Collection counts
        """
        results = {'teams': 0, 'matches': 0, 'players': 0, 'injuries': 0, 'statistics': 0}
        
        # 1. Collect and save league info
        leagues_data = self.collector.collect_leagues()
        league_data = next((l for l in leagues_data if l['external_id'] == league_id), None)
        if league_data:
            self.db_manager.save_leagues([league_data])
        
        # 2. Collect and save teams
        teams_data = self.collector.collect_teams(league_id, self.current_season)
        if teams_data:
            saved_teams = self.db_manager.save_teams(teams_data)
            results['teams'] = saved_teams
            self.logger.info(f"Saved {saved_teams} teams for {league_name}")
        
        # 3. Collect and save matches
        date_from = None
        date_to = None
        
        if include_historical:
            date_from = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            date_to = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')  # Include upcoming matches
        
        matches_data = self.collector.collect_matches(
            league_id, self.current_season, date_from, date_to
        )
        
        if matches_data:
            saved_matches = self.db_manager.save_matches(matches_data)
            results['matches'] = saved_matches
            self.logger.info(f"Saved {saved_matches} matches for {league_name}")
        
        # 4. Collect players for each team
        for team_data in teams_data:
            team_id = team_data['external_id']
            try:
                players_data = self.collector.collect_players(team_id, league_id, self.current_season)
                if players_data:
                    saved_players = self.db_manager.save_players(players_data)
                    results['players'] += saved_players
                
                # Rate limiting between teams
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error collecting players for team {team_id}: {e}")
        
        # 5. Collect injuries
        try:
            injuries_data = self.collector.collect_injuries(league_id, self.current_season)
            if injuries_data:
                # Process and save injuries (you'll need to implement save_injuries in DatabaseManager)
                results['injuries'] = len(injuries_data)
                self.logger.info(f"Collected {len(injuries_data)} injuries for {league_name}")
        except Exception as e:
            self.logger.error(f"Error collecting injuries for {league_name}: {e}")
        
        # 6. Collect detailed match statistics for recent matches
        recent_matches = [m for m in matches_data if m.get('status') == 'FT'][:10]  # Last 10 finished matches
        
        for match_data in recent_matches:
            fixture_id = match_data['external_id']
            try:
                match_stats = self.collector.collect_match_statistics(fixture_id)
                if match_stats:
                    # Process and save match statistics
                    results['statistics'] += 1
                
                # Rate limiting between match statistics
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error collecting statistics for match {fixture_id}: {e}")
        
        return results
    
    def collect_upcoming_matches(self, days_ahead: int = 7) -> Dict[str, Any]:
        """Collect upcoming matches for prediction.
        
        Args:
            days_ahead: Number of days ahead to collect
            
        Returns:
            Collection results
        """
        self.logger.info(f"Collecting upcoming matches for next {days_ahead} days")
        
        results = {'matches_collected': 0, 'errors': []}
        
        date_from = datetime.now().strftime('%Y-%m-%d')
        date_to = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
        
        for league_name, league_config in self.leagues_config.items():
            league_id = league_config['id']
            
            try:
                matches_data = self.collector.collect_matches(
                    league_id, self.current_season, date_from, date_to, status='NS'  # Not Started
                )
                
                if matches_data:
                    saved_matches = self.db_manager.save_matches(matches_data)
                    results['matches_collected'] += saved_matches
                    self.logger.info(f"Collected {saved_matches} upcoming matches for {league_name}")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                error_msg = f"Error collecting upcoming matches for {league_name}: {e}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
        
        return results
    
    def collect_match_details(self, fixture_id: int) -> Dict[str, Any]:
        """Collect comprehensive details for a specific match.
        
        Args:
            fixture_id: API-Football fixture ID
            
        Returns:
            Comprehensive match data
        """
        self.logger.info(f"Collecting comprehensive details for fixture {fixture_id}")
        
        try:
            # Get comprehensive match data
            match_details = self.collector.client.get_comprehensive_match_data(fixture_id)
            
            # Process and save different types of data
            results = {
                'basic_info': bool(match_details.get('basic_info')),
                'statistics': len(match_details.get('statistics', [])),
                'events': len(match_details.get('events', [])),
                'lineups': len(match_details.get('lineups', [])),
                'player_stats': len(match_details.get('player_stats', [])),
                'predictions': bool(match_details.get('predictions')),
                'odds': len(match_details.get('odds', []))
            }
            
            # Save to database (implement specific save methods as needed)
            # self.db_manager.save_match_events(match_details.get('events', []))
            # self.db_manager.save_match_lineups(match_details.get('lineups', []))
            
            self.logger.info(f"Collected comprehensive data for fixture {fixture_id}: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error collecting details for fixture {fixture_id}: {e}")
            raise
    
    def update_injury_status(self) -> Dict[str, Any]:
        """Update current injury status for all leagues.
        
        Returns:
            Update results
        """
        self.logger.info("Updating injury status across all leagues")
        
        results = {'injuries_updated': 0, 'errors': []}
        
        for league_name, league_config in self.leagues_config.items():
            league_id = league_config['id']
            
            try:
                # Get current injuries
                injuries_data = self.collector.collect_injuries(
                    league_id, self.current_season, date=datetime.now().strftime('%Y-%m-%d')
                )
                
                if injuries_data:
                    # Process and update injury status
                    results['injuries_updated'] += len(injuries_data)
                    self.logger.info(f"Updated {len(injuries_data)} injuries for {league_name}")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                error_msg = f"Error updating injuries for {league_name}: {e}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
        
        return results
    
    def get_api_status(self) -> Dict[str, Any]:
        """Get API status and usage information.
        
        Returns:
            API status information
        """
        try:
            status = self.collector.client.get_api_status()
            requests_remaining = self.collector.client.get_requests_remaining()
            
            return {
                'api_status': status,
                'requests_remaining': requests_remaining,
                'requests_made_today': self.collector.client.requests_made
            }
        except Exception as e:
            self.logger.error(f"Error getting API status: {e}")
            return {'error': str(e)}
