"""Ensemble model implementation for football predictions."""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
from sklearn.ensemble import VotingClassifier, BaggingClassifier, AdaBoostClassifier
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

from src.models.base.base_model import BaseFootballModel


class EnsembleFootballModel(BaseFootballModel):
    """Ensemble model combining multiple algorithms for football match predictions."""
    
    def __init__(self, model_name: str = "ensemble_football", model_config: Optional[Dict[str, Any]] = None):
        """Initialize Ensemble model.
        
        Args:
            model_name: Name of the model
            model_config: Model configuration dictionary
        """
        super().__init__(model_name, "classification")
        self.logger = logging.getLogger(f"model.{self.model_name}")
        
        # Default configuration
        self.default_config = {
            'ensemble_type': 'voting',  # 'voting', 'bagging', 'boosting', 'stacking'
            'voting_type': 'soft',  # 'hard' or 'soft'
            'n_estimators': 10,  # For bagging/boosting
            'random_state': 42,
            'n_jobs': -1,
            'use_scaling': True  # Whether to scale features
        }
        
        # Update with provided config
        self.config = self.default_config.copy()
        if model_config:
            self.config.update(model_config)
        
        # Initialize components
        self.model = None
        self.base_models = {}
        self.scaler = StandardScaler() if self.config['use_scaling'] else None
        self.feature_importance = None
        self.is_trained = False
        self.individual_predictions = {}
    
    def _create_base_models(self) -> Dict[str, Any]:
        """Create base models for ensemble.
        
        Returns:
            Dictionary of base models
        """
        base_models = {
            'rf': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=self.config['random_state'],
                n_jobs=self.config['n_jobs']
            ),
            'et': ExtraTreesClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=self.config['random_state'],
                n_jobs=self.config['n_jobs']
            ),
            'lr': LogisticRegression(
                random_state=self.config['random_state'],
                max_iter=1000,
                n_jobs=self.config['n_jobs']
            ),
            'svc': SVC(
                probability=True,  # Needed for soft voting
                random_state=self.config['random_state'],
                kernel='rbf',
                C=1.0
            ),
            'nb': GaussianNB()
        }
        
        return base_models
    
    def _create_model(self, **kwargs) -> Any:
        """Create ensemble model.
        
        Returns:
            Ensemble model instance
        """
        base_models = self._create_base_models()
        self.base_models = base_models
        
        ensemble_type = self.config['ensemble_type']
        
        if ensemble_type == 'voting':
            # Voting Classifier
            estimators = [(name, model) for name, model in base_models.items()]
            return VotingClassifier(
                estimators=estimators,
                voting=self.config['voting_type'],
                n_jobs=self.config['n_jobs']
            )
        
        elif ensemble_type == 'bagging':
            # Bagging with Random Forest as base
            return BaggingClassifier(
                base_estimator=base_models['rf'],
                n_estimators=self.config['n_estimators'],
                random_state=self.config['random_state'],
                n_jobs=self.config['n_jobs']
            )
        
        elif ensemble_type == 'boosting':
            # AdaBoost
            return AdaBoostClassifier(
                base_estimator=base_models['lr'],
                n_estimators=self.config['n_estimators'],
                random_state=self.config['random_state']
            )
        
        else:
            # Default to voting
            estimators = [(name, model) for name, model in base_models.items()]
            return VotingClassifier(
                estimators=estimators,
                voting='soft',
                n_jobs=self.config['n_jobs']
            )
    
    def _fit_model(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Fit the ensemble model.
        
        Args:
            X: Training features
            y: Training targets
        """
        if self.model is None:
            self.model = self._create_model()
        
        # Scale features if needed
        if self.scaler is not None:
            X_processed = self.scaler.fit_transform(X)
        else:
            X_processed = X.values
        
        # Train the ensemble
        self.model.fit(X_processed, y)
        self.is_trained = True
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.
        
        Args:
            X: Features for prediction
            
        Returns:
            Prediction probabilities
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        # Scale features if needed
        if self.scaler is not None:
            X_processed = self.scaler.transform(X)
        else:
            X_processed = X.values
        
        return self.model.predict_proba(X_processed)
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series,
              X_val: Optional[pd.DataFrame] = None, 
              y_val: Optional[pd.Series] = None) -> Dict[str, Any]:
        """Train the ensemble model.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Training results dictionary
        """
        self.logger.info(f"Training {self.model_name} model...")
        
        try:
            # Initialize and train model
            self.model = self._create_model()
            
            # Scale features if needed
            if self.scaler is not None:
                X_train_processed = self.scaler.fit_transform(X_train)
            else:
                X_train_processed = X_train.values
            
            # Train ensemble
            self.model.fit(X_train_processed, y_train)

            # Store training data for feature importance calculation
            self._training_data = (X_train, y_train)
            
            # Training predictions
            train_pred = self.predict(X_train)
            train_accuracy = accuracy_score(y_train, train_pred)
            
            results = {
                'model_name': self.model_name,
                'train_accuracy': train_accuracy,
                'n_features': len(X_train.columns),
                'n_samples': len(X_train),
                'ensemble_type': self.config['ensemble_type'],
                'n_base_models': len(self.base_models)
            }
            
            # Validation predictions if validation data provided
            if X_val is not None and y_val is not None:
                val_pred = self.predict(X_val)
                val_accuracy = accuracy_score(y_val, val_pred)
                results['val_accuracy'] = val_accuracy
                
                self.logger.info(f"Validation accuracy: {val_accuracy:.4f}")
            
            self.is_trained = True
            self.logger.info(f"Training completed. Train accuracy: {train_accuracy:.4f}")
            self.logger.info(f"Ensemble type: {self.config['ensemble_type']} with {len(self.base_models)} base models")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            raise
    
    def _calculate_feature_importance(self) -> None:
        """Override base method to calculate feature importance."""
        if not hasattr(self, '_training_data') or self._training_data is None:
            return

        X, y = self._training_data
        self._calculate_ensemble_feature_importance(X, y)

    def _calculate_ensemble_feature_importance(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Calculate feature importance from ensemble.
        
        Args:
            X: Training features
            y: Training targets
        """
        try:
            # Try to get feature importance from tree-based models in ensemble
            if hasattr(self.model, 'feature_importances_'):
                # Direct feature importance (for tree-based ensembles)
                importances = self.model.feature_importances_
            elif hasattr(self.model, 'estimators_'):
                # Average importance from individual estimators
                importances = np.zeros(len(X.columns))
                n_estimators = 0
                
                for estimator in self.model.estimators_:
                    if hasattr(estimator, 'feature_importances_'):
                        importances += estimator.feature_importances_
                        n_estimators += 1
                
                if n_estimators > 0:
                    importances /= n_estimators
                else:
                    importances = np.ones(len(X.columns)) / len(X.columns)
            else:
                # Fallback: use permutation importance
                from sklearn.inspection import permutation_importance
                
                X_processed = self.scaler.transform(X) if self.scaler else X.values
                perm_importance = permutation_importance(
                    self.model, X_processed, y, 
                    n_repeats=3, random_state=42, n_jobs=-1
                )
                importances = perm_importance.importances_mean
            
            # Store feature importance
            self.feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': importances
            }).sort_values('importance', ascending=False)
            
        except Exception as e:
            self.logger.warning(f"Could not calculate feature importance: {e}")
            # Fallback: equal importance
            self.feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': np.ones(len(X.columns)) / len(X.columns)
            })
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predicted classes
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        # Scale features if needed
        if self.scaler is not None:
            X_processed = self.scaler.transform(X)
        else:
            X_processed = X.values
        
        return self.model.predict(X_processed)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.
        
        Args:
            X: Features for prediction
            
        Returns:
            Prediction probabilities
        """
        return self._predict_proba(X)
    
    def evaluate(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """Evaluate model performance.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        # Predictions
        y_pred = self.predict(X_test)
        y_proba = self.predict_proba(X_test)
        
        # Metrics
        accuracy = accuracy_score(y_test, y_pred)
        
        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Confusion matrix
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        results = {
            'accuracy': accuracy,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'n_test_samples': len(X_test),
            'ensemble_type': self.config['ensemble_type'],
            'n_base_models': len(self.base_models)
        }
        
        self.logger.info(f"Test accuracy: {accuracy:.4f}")
        
        return results
    
    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """Get feature importance.
        
        Args:
            top_n: Number of top features to return
            
        Returns:
            DataFrame with feature importance
        """
        if self.feature_importance is None:
            raise ValueError("Model must be trained to get feature importance")
        
        return self.feature_importance.head(top_n)
    
    def save_model(self, filepath: str) -> None:
        """Save model to file.
        
        Args:
            filepath: Path to save model
        """
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        import joblib
        
        model_data = {
            'model': self.model,
            'base_models': self.base_models,
            'scaler': self.scaler,
            'config': self.config,
            'feature_importance': self.feature_importance,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained
        }
        
        joblib.dump(model_data, filepath)
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load model from file.
        
        Args:
            filepath: Path to load model from
        """
        import joblib
        
        model_data = joblib.load(filepath)
        
        self.model = model_data['model']
        self.base_models = model_data.get('base_models', {})
        self.scaler = model_data.get('scaler')
        self.config = model_data['config']
        self.feature_importance = model_data.get('feature_importance')
        self.model_name = model_data.get('model_name', self.model_name)
        self.model_type = model_data.get('model_type', self.model_type)
        self.is_trained = model_data.get('is_trained', True)
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information.
        
        Returns:
            Dictionary with model information
        """
        info = {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'config': self.config,
            'ensemble_type': self.config['ensemble_type'],
            'n_base_models': len(self.base_models)
        }
        
        if self.base_models:
            info['base_models'] = list(self.base_models.keys())
        
        return info
