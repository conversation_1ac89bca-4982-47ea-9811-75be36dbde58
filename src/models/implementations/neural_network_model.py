"""Neural Network model implementation for football predictions."""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

from src.models.base.base_model import BaseFootballModel


class NeuralNetworkFootballModel(BaseFootballModel):
    """Neural Network model for football match predictions."""
    
    def __init__(self, model_name: str = "neural_network_football", model_config: Optional[Dict[str, Any]] = None):
        """Initialize Neural Network model.
        
        Args:
            model_name: Name of the model
            model_config: Model configuration dictionary
        """
        super().__init__(model_name, "classification")
        self.logger = logging.getLogger(f"model.{self.model_name}")
        
        # Default configuration optimized for football prediction
        self.default_config = {
            'hidden_layer_sizes': (128, 64, 32),  # 3 hidden layers with decreasing neurons
            'activation': 'relu',
            'solver': 'adam',
            'alpha': 0.001,  # L2 regularization
            'batch_size': 'auto',
            'learning_rate': 'adaptive',
            'learning_rate_init': 0.001,
            'max_iter': 500,
            'shuffle': True,
            'random_state': 42,
            'tol': 1e-4,
            'validation_fraction': 0.1,
            'beta_1': 0.9,
            'beta_2': 0.999,
            'epsilon': 1e-8,
            'n_iter_no_change': 10,
            'early_stopping': True
        }
        
        # Update with provided config
        self.config = self.default_config.copy()
        if model_config:
            self.config.update(model_config)
        
        # Initialize components
        self.model = None
        self.scaler = StandardScaler()  # Neural networks need feature scaling
        self.feature_importance = None
        self.is_trained = False
        self.training_history = {}
    
    def _create_model(self, **kwargs) -> MLPClassifier:
        """Create Neural Network model.
        
        Returns:
            MLPClassifier instance
        """
        return MLPClassifier(**self.config)
    
    def _fit_model(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Fit the Neural Network model.
        
        Args:
            X: Training features
            y: Training targets
        """
        if self.model is None:
            self.model = self._create_model()
        
        # Scale features for neural network
        X_scaled = self.scaler.fit_transform(X)
        
        # Train the model
        self.model.fit(X_scaled, y)
        self.is_trained = True
        
        # Store training history
        self.training_history = {
            'n_iter': self.model.n_iter_,
            'loss': self.model.loss_,
            'n_layers': self.model.n_layers_,
            'n_outputs': self.model.n_outputs_
        }
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.
        
        Args:
            X: Features for prediction
            
        Returns:
            Prediction probabilities
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        # Scale features using the same scaler from training
        X_scaled = self.scaler.transform(X)
        return self.model.predict_proba(X_scaled)
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series,
              X_val: Optional[pd.DataFrame] = None, 
              y_val: Optional[pd.Series] = None) -> Dict[str, Any]:
        """Train the Neural Network model.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Training results dictionary
        """
        self.logger.info(f"Training {self.model_name} model...")
        
        try:
            # Initialize model
            self.model = self._create_model()
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # Train model
            self.model.fit(X_train_scaled, y_train)

            # Store training data for feature importance calculation
            self._training_data = (X_train, y_train)
            
            # Training predictions
            train_pred = self.predict(X_train)
            train_accuracy = accuracy_score(y_train, train_pred)
            
            results = {
                'model_name': self.model_name,
                'train_accuracy': train_accuracy,
                'n_features': len(X_train.columns),
                'n_samples': len(X_train),
                'n_iterations': self.model.n_iter_,
                'final_loss': self.model.loss_
            }
            
            # Validation predictions if validation data provided
            if X_val is not None and y_val is not None:
                val_pred = self.predict(X_val)
                val_accuracy = accuracy_score(y_val, val_pred)
                results['val_accuracy'] = val_accuracy
                
                self.logger.info(f"Validation accuracy: {val_accuracy:.4f}")
            
            self.is_trained = True
            self.logger.info(f"Training completed. Train accuracy: {train_accuracy:.4f}")
            self.logger.info(f"Converged in {self.model.n_iter_} iterations with loss: {self.model.loss_:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            raise
    
    def _calculate_feature_importance(self) -> None:
        """Override base method to calculate feature importance."""
        if not hasattr(self, '_training_data') or self._training_data is None:
            return

        X, y = self._training_data
        self._calculate_neural_feature_importance(X, y)

    def _calculate_neural_feature_importance(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Calculate feature importance using permutation importance.
        
        Args:
            X: Training features
            y: Training targets
        """
        try:
            from sklearn.inspection import permutation_importance
            
            # Calculate permutation importance
            X_scaled = self.scaler.transform(X)
            perm_importance = permutation_importance(
                self.model, X_scaled, y, 
                n_repeats=5, random_state=42, n_jobs=-1
            )
            
            # Store feature importance
            self.feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': perm_importance.importances_mean
            }).sort_values('importance', ascending=False)
            
        except Exception as e:
            self.logger.warning(f"Could not calculate feature importance: {e}")
            # Fallback: use absolute weights from first hidden layer
            try:
                weights = np.abs(self.model.coefs_[0]).mean(axis=1)
                self.feature_importance = pd.DataFrame({
                    'feature': X.columns,
                    'importance': weights / weights.sum()
                }).sort_values('importance', ascending=False)
            except:
                self.feature_importance = pd.DataFrame({
                    'feature': X.columns,
                    'importance': np.ones(len(X.columns)) / len(X.columns)
                })
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predicted classes
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.
        
        Args:
            X: Features for prediction
            
        Returns:
            Prediction probabilities
        """
        return self._predict_proba(X)
    
    def evaluate(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """Evaluate model performance.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        # Predictions
        y_pred = self.predict(X_test)
        y_proba = self.predict_proba(X_test)
        
        # Metrics
        accuracy = accuracy_score(y_test, y_pred)
        
        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Confusion matrix
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        results = {
            'accuracy': accuracy,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'n_test_samples': len(X_test),
            'training_iterations': self.model.n_iter_,
            'final_loss': self.model.loss_
        }
        
        self.logger.info(f"Test accuracy: {accuracy:.4f}")
        
        return results
    
    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """Get feature importance.
        
        Args:
            top_n: Number of top features to return
            
        Returns:
            DataFrame with feature importance
        """
        if self.feature_importance is None:
            raise ValueError("Model must be trained to get feature importance")
        
        return self.feature_importance.head(top_n)
    
    def save_model(self, filepath: str) -> None:
        """Save model to file.
        
        Args:
            filepath: Path to save model
        """
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        import joblib
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'config': self.config,
            'feature_importance': self.feature_importance,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'training_history': self.training_history
        }
        
        joblib.dump(model_data, filepath)
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load model from file.
        
        Args:
            filepath: Path to load model from
        """
        import joblib
        
        model_data = joblib.load(filepath)
        
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.config = model_data['config']
        self.feature_importance = model_data.get('feature_importance')
        self.model_name = model_data.get('model_name', self.model_name)
        self.model_type = model_data.get('model_type', self.model_type)
        self.is_trained = model_data.get('is_trained', True)
        self.training_history = model_data.get('training_history', {})
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information.
        
        Returns:
            Dictionary with model information
        """
        info = {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'config': self.config
        }
        
        if self.is_trained and self.model is not None:
            info.update({
                'hidden_layer_sizes': self.model.hidden_layer_sizes,
                'n_features_in': getattr(self.model, 'n_features_in_', None),
                'n_outputs': getattr(self.model, 'n_outputs_', None),
                'n_layers': getattr(self.model, 'n_layers_', None),
                'n_iter': getattr(self.model, 'n_iter_', None),
                'loss': getattr(self.model, 'loss_', None)
            })
        
        return info
