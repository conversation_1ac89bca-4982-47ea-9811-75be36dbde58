"""Random Forest model implementation for football predictions."""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

from src.models.base.base_model import BaseFootballModel


class RandomForestFootballModel(BaseFootballModel):
    """Random Forest model for football match predictions."""

    def __init__(self, model_name: str = "random_forest_football", model_config: Optional[Dict[str, Any]] = None):
        """Initialize Random Forest model.

        Args:
            model_name: Name of the model
            model_config: Model configuration dictionary
        """
        super().__init__(model_name, "classification")
        self.logger = logging.getLogger(f"model.{self.model_name}")
        
        # Default configuration
        self.default_config = {
            'n_estimators': 100,
            'max_depth': 10,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'max_features': 'sqrt',
            'random_state': 42,
            'n_jobs': -1,
            'class_weight': 'balanced'
        }
        
        # Update with provided config
        self.config = self.default_config.copy()
        if model_config:
            self.config.update(model_config)
        
        # Initialize model
        self.model = None
        self.feature_importance = None
        self.is_trained = False

    def _create_model(self, **kwargs) -> RandomForestClassifier:
        """Create Random Forest model.

        Returns:
            RandomForestClassifier instance
        """
        return RandomForestClassifier(**self.config)

    def _fit_model(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Fit the Random Forest model.

        Args:
            X: Training features
            y: Training targets
        """
        if self.model is None:
            self.model = self._create_model()

        self.model.fit(X, y)
        self.is_trained = True

    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.

        Args:
            X: Features for prediction

        Returns:
            Prediction probabilities
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")

        return self.model.predict_proba(X)
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series,
              X_val: Optional[pd.DataFrame] = None, 
              y_val: Optional[pd.Series] = None) -> Dict[str, Any]:
        """Train the Random Forest model.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Training results dictionary
        """
        self.logger.info(f"Training {self.model_name} model...")
        
        try:
            # Initialize model
            self.model = RandomForestClassifier(**self.config)
            
            # Train model
            self.model.fit(X_train, y_train)
            
            # Get feature importance
            self.feature_importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            # Training predictions
            train_pred = self.model.predict(X_train)
            train_accuracy = accuracy_score(y_train, train_pred)
            
            results = {
                'model_name': self.model_name,
                'train_accuracy': train_accuracy,
                'n_features': len(X_train.columns),
                'n_samples': len(X_train)
            }
            
            # Validation predictions if validation data provided
            if X_val is not None and y_val is not None:
                val_pred = self.model.predict(X_val)
                val_accuracy = accuracy_score(y_val, val_pred)
                results['val_accuracy'] = val_accuracy
                
                self.logger.info(f"Validation accuracy: {val_accuracy:.4f}")
            
            self.is_trained = True
            self.logger.info(f"Training completed. Train accuracy: {train_accuracy:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            raise
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predicted classes
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        return self.model.predict(X)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities.
        
        Args:
            X: Features for prediction
            
        Returns:
            Prediction probabilities
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        return self.model.predict_proba(X)
    
    def evaluate(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """Evaluate model performance.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        # Predictions
        y_pred = self.predict(X_test)
        y_proba = self.predict_proba(X_test)
        
        # Metrics
        accuracy = accuracy_score(y_test, y_pred)
        
        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Confusion matrix
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        results = {
            'accuracy': accuracy,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'n_test_samples': len(X_test)
        }
        
        self.logger.info(f"Test accuracy: {accuracy:.4f}")
        
        return results
    
    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """Get feature importance.

        Args:
            top_n: Number of top features to return

        Returns:
            DataFrame with feature importance
        """
        if self.feature_importance is None:
            raise ValueError("Model must be trained to get feature importance")

        if isinstance(self.feature_importance, dict):
            # Convert dict to DataFrame if needed
            importance_df = pd.DataFrame(list(self.feature_importance.items()),
                                       columns=['feature', 'importance'])
            importance_df = importance_df.sort_values('importance', ascending=False)
            return importance_df.head(top_n)
        else:
            return self.feature_importance.head(top_n)
    
    def save_model(self, filepath: str) -> None:
        """Save model to file.
        
        Args:
            filepath: Path to save model
        """
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        import joblib
        
        model_data = {
            'model': self.model,
            'config': self.config,
            'feature_importance': self.feature_importance,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained
        }
        
        joblib.dump(model_data, filepath)
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load model from file.
        
        Args:
            filepath: Path to load model from
        """
        import joblib
        
        model_data = joblib.load(filepath)
        
        self.model = model_data['model']
        self.config = model_data['config']
        self.feature_importance = model_data.get('feature_importance')
        self.model_name = model_data.get('model_name', self.model_name)
        self.model_type = model_data.get('model_type', self.model_type)
        self.is_trained = model_data.get('is_trained', True)
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information.
        
        Returns:
            Dictionary with model information
        """
        info = {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'config': self.config
        }
        
        if self.is_trained and self.model is not None:
            info.update({
                'n_estimators': self.model.n_estimators,
                'max_depth': self.model.max_depth,
                'n_features_in': getattr(self.model, 'n_features_in_', None),
                'n_classes': getattr(self.model, 'n_classes_', None)
            })
        
        return info
