"""Neural Network model for football prediction using TensorFlow/Keras."""

import numpy as np
import pandas as pd
from typing import Dict, Any, Union, Optional, List
import logging

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, callbacks
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    tf = None
    keras = None
    layers = None
    callbacks = None

from ..base.base_model import BaseFootballModel


class NeuralNetworkFootballModel(BaseFootballModel):
    """Neural Network model for football match prediction."""
    
    def __init__(self, model_name: str = "neural_network", model_type: str = "classification"):
        """Initialize Neural Network model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model ('classification' or 'regression')
        """
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for Neural Network model")
        
        super().__init__(model_name, model_type)
        self.scaler = StandardScaler()
        self.label_encoder = None
        self.training_history = {}
        
        # Default architecture parameters
        self.default_params = {
            'hidden_layers': [128, 64, 32],
            'dropout_rate': 0.3,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'validation_split': 0.2,
            'early_stopping_patience': 15,
            'reduce_lr_patience': 10
        }
        
        # Get model-specific config
        nn_config = self.model_config.get('model_types', {}).get('neural_network', {})
        if nn_config.get('enabled', True):
            self.default_params.update(nn_config.get('params', {}))
    
    def _create_model(self, **kwargs) -> keras.Model:
        """Create neural network model.
        
        Returns:
            Initialized Keras model
        """
        # Merge default params with provided kwargs
        params = {**self.default_params, **kwargs}
        self.hyperparameters = params
        
        # Model architecture
        hidden_layers = params['hidden_layers']
        dropout_rate = params['dropout_rate']
        
        # Input layer (will be set during training based on feature count)
        inputs = keras.Input(shape=(None,), name='features')
        
        # Hidden layers
        x = inputs
        for i, units in enumerate(hidden_layers):
            x = layers.Dense(
                units, 
                activation='relu', 
                name=f'dense_{i+1}',
                kernel_regularizer=keras.regularizers.l2(0.001)
            )(x)
            x = layers.Dropout(dropout_rate, name=f'dropout_{i+1}')(x)
            x = layers.BatchNormalization(name=f'batch_norm_{i+1}')(x)
        
        # Output layer
        if self.model_type == 'classification':
            # Determine number of classes (will be set during training)
            outputs = layers.Dense(3, activation='softmax', name='predictions')(x)  # Default to 3 classes
        else:
            outputs = layers.Dense(1, activation='linear', name='predictions')(x)
        
        model = keras.Model(inputs=inputs, outputs=outputs, name=self.model_name)
        
        # Compile model
        optimizer = keras.optimizers.Adam(learning_rate=params['learning_rate'])
        
        if self.model_type == 'classification':
            model.compile(
                optimizer=optimizer,
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy', 'sparse_categorical_crossentropy']
            )
        else:
            model.compile(
                optimizer=optimizer,
                loss='mse',
                metrics=['mae', 'mse']
            )
        
        return model
    
    def _fit_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Fit neural network model.
        
        Args:
            X: Feature matrix
            y: Target variable(s)
        """
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Handle target encoding for classification
        if self.model_type == 'classification':
            if isinstance(y, pd.Series):
                y_encoded = self._encode_targets(y)
                n_classes = len(np.unique(y_encoded))
            else:
                y_encoded = y
                n_classes = len(np.unique(y))
        else:
            y_encoded = y
            n_classes = 1
        
        # Recreate model with correct input shape and output classes
        input_shape = X_scaled.shape[1]
        self.model = self._create_model_with_shape(input_shape, n_classes)
        
        # Setup callbacks
        callbacks_list = self._setup_callbacks()
        
        # Train the model
        history = self.model.fit(
            X_scaled, y_encoded,
            batch_size=self.hyperparameters['batch_size'],
            epochs=self.hyperparameters['epochs'],
            validation_split=self.hyperparameters['validation_split'],
            callbacks=callbacks_list,
            verbose=1
        )
        
        # Store training history
        self.training_history.update({
            'loss': history.history['loss'],
            'val_loss': history.history.get('val_loss', []),
            'accuracy': history.history.get('accuracy', []),
            'val_accuracy': history.history.get('val_accuracy', [])
        })
        
        self.logger.info(f"Neural network trained for {len(history.history['loss'])} epochs")
    
    def _create_model_with_shape(self, input_shape: int, n_classes: int) -> keras.Model:
        """Create model with specific input shape and output classes.
        
        Args:
            input_shape: Number of input features
            n_classes: Number of output classes
            
        Returns:
            Configured Keras model
        """
        hidden_layers = self.hyperparameters['hidden_layers']
        dropout_rate = self.hyperparameters['dropout_rate']
        learning_rate = self.hyperparameters['learning_rate']
        
        # Input layer
        inputs = keras.Input(shape=(input_shape,), name='features')
        
        # Hidden layers
        x = inputs
        for i, units in enumerate(hidden_layers):
            x = layers.Dense(
                units, 
                activation='relu', 
                name=f'dense_{i+1}',
                kernel_regularizer=keras.regularizers.l2(0.001)
            )(x)
            x = layers.Dropout(dropout_rate, name=f'dropout_{i+1}')(x)
            x = layers.BatchNormalization(name=f'batch_norm_{i+1}')(x)
        
        # Output layer
        if self.model_type == 'classification':
            outputs = layers.Dense(n_classes, activation='softmax', name='predictions')(x)
        else:
            outputs = layers.Dense(1, activation='linear', name='predictions')(x)
        
        model = keras.Model(inputs=inputs, outputs=outputs, name=self.model_name)
        
        # Compile model
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        if self.model_type == 'classification':
            model.compile(
                optimizer=optimizer,
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
        else:
            model.compile(
                optimizer=optimizer,
                loss='mse',
                metrics=['mae']
            )
        
        return model
    
    def _setup_callbacks(self) -> List[keras.callbacks.Callback]:
        """Setup training callbacks.
        
        Returns:
            List of Keras callbacks
        """
        callbacks_list = []
        
        # Early stopping
        early_stopping = callbacks.EarlyStopping(
            monitor='val_loss',
            patience=self.hyperparameters['early_stopping_patience'],
            restore_best_weights=True,
            verbose=1
        )
        callbacks_list.append(early_stopping)
        
        # Reduce learning rate on plateau
        reduce_lr = callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=self.hyperparameters['reduce_lr_patience'],
            min_lr=1e-7,
            verbose=1
        )
        callbacks_list.append(reduce_lr)
        
        return callbacks_list
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities.
        
        Args:
            X: Feature matrix
            
        Returns:
            Class probabilities
        """
        if self.model_type != 'classification':
            raise ValueError("predict_proba only available for classification models")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled, verbose=0)
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions.
        
        Args:
            X: Feature matrix
            
        Returns:
            Predictions
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        X_scaled = self.scaler.transform(X)
        
        if self.model_type == 'classification':
            probabilities = self.model.predict(X_scaled, verbose=0)
            return np.argmax(probabilities, axis=1)
        else:
            return self.model.predict(X_scaled, verbose=0).flatten()
    
    def _encode_targets(self, y: pd.Series) -> np.ndarray:
        """Encode target labels for classification.
        
        Args:
            y: Target labels
            
        Returns:
            Encoded labels
        """
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            return self.label_encoder.fit_transform(y)
        else:
            return self.label_encoder.transform(y)
    
    def get_training_history(self) -> Dict[str, Any]:
        """Get detailed training history.
        
        Returns:
            Training history dictionary
        """
        return self.training_history
    
    def plot_training_history(self) -> Dict[str, Any]:
        """Get data for plotting training history.
        
        Returns:
            Plot data dictionary
        """
        if not self.training_history:
            return {}
        
        epochs = list(range(1, len(self.training_history['loss']) + 1))
        
        plot_data = {
            'epochs': epochs,
            'train_loss': self.training_history['loss'],
            'val_loss': self.training_history.get('val_loss', []),
            'train_accuracy': self.training_history.get('accuracy', []),
            'val_accuracy': self.training_history.get('val_accuracy', [])
        }
        
        return plot_data
    
    def tune_hyperparameters(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                           param_ranges: Optional[Dict[str, Any]] = None,
                           n_trials: int = 20) -> Dict[str, Any]:
        """Tune hyperparameters using random search.
        
        Args:
            X: Feature matrix
            y: Target variable
            param_ranges: Parameter ranges for tuning
            n_trials: Number of trials
            
        Returns:
            Best parameters and scores
        """
        if param_ranges is None:
            param_ranges = {
                'hidden_layers': [
                    [64, 32], [128, 64], [128, 64, 32], 
                    [256, 128, 64], [512, 256, 128]
                ],
                'dropout_rate': [0.2, 0.3, 0.4, 0.5],
                'learning_rate': [0.0001, 0.001, 0.01],
                'batch_size': [16, 32, 64]
            }
        
        from sklearn.model_selection import train_test_split
        
        # Split data for validation
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        best_score = -np.inf
        best_params = None
        trial_results = []
        
        for trial in range(n_trials):
            # Sample parameters
            trial_params = {}
            for param, values in param_ranges.items():
                trial_params[param] = np.random.choice(values)
            
            try:
                # Create and train model
                self.hyperparameters = {**self.default_params, **trial_params}
                
                # Scale features
                X_train_scaled = self.scaler.fit_transform(X_train)
                X_val_scaled = self.scaler.transform(X_val)
                
                # Encode targets
                if self.model_type == 'classification':
                    y_train_encoded = self._encode_targets(y_train)
                    y_val_encoded = self._encode_targets(y_val)
                    n_classes = len(np.unique(y_train_encoded))
                else:
                    y_train_encoded = y_train
                    y_val_encoded = y_val
                    n_classes = 1
                
                # Create model
                model = self._create_model_with_shape(X_train_scaled.shape[1], n_classes)
                
                # Train with early stopping
                history = model.fit(
                    X_train_scaled, y_train_encoded,
                    validation_data=(X_val_scaled, y_val_encoded),
                    batch_size=trial_params['batch_size'],
                    epochs=50,  # Reduced for tuning
                    callbacks=[
                        callbacks.EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True)
                    ],
                    verbose=0
                )
                
                # Get best validation score
                val_score = min(history.history['val_loss'])
                score = -val_score  # Convert to maximization problem
                
                trial_results.append({
                    'params': trial_params,
                    'score': score,
                    'val_loss': val_score
                })
                
                if score > best_score:
                    best_score = score
                    best_params = trial_params
                    self.model = model  # Keep best model
                
                self.logger.info(f"Trial {trial + 1}/{n_trials}: Score = {score:.4f}")
                
            except Exception as e:
                self.logger.warning(f"Trial {trial + 1} failed: {e}")
                continue
        
        if best_params is not None:
            self.hyperparameters = {**self.default_params, **best_params}
            self.is_trained = True
        
        results = {
            'best_params': best_params,
            'best_score': best_score,
            'n_trials': len(trial_results),
            'trial_results': trial_results
        }
        
        self.logger.info(f"Hyperparameter tuning completed. Best score: {best_score:.4f}")
        return results
    
    def get_model_summary(self) -> str:
        """Get model architecture summary.
        
        Returns:
            Model summary string
        """
        if self.model is None:
            return "Model not created yet"
        
        import io
        import sys
        
        # Capture model summary
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()
        
        self.model.summary()
        
        sys.stdout = old_stdout
        summary = buffer.getvalue()
        
        return summary
