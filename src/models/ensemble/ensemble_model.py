"""Ensemble model combining multiple football prediction models."""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Union, Optional
import logging
from datetime import datetime

from ..base.base_model import BaseFootballModel


class EnsembleFootballModel(BaseFootballModel):
    """Ensemble model that combines predictions from multiple base models."""
    
    def __init__(self, base_models: List[BaseFootballModel], 
                 ensemble_method: str = 'weighted_average',
                 model_name: str = "ensemble"):
        """Initialize ensemble model.
        
        Args:
            base_models: List of base models to ensemble
            ensemble_method: Method for combining predictions ('weighted_average', 'voting', 'stacking')
            model_name: Name of the ensemble model
        """
        super().__init__(model_name, 'classification')
        
        self.base_models = base_models
        self.ensemble_method = ensemble_method
        self.model_weights = None
        self.meta_model = None
        
        # Validate base models
        if not base_models:
            raise ValueError("At least one base model is required")
        
        # Check model types consistency
        model_types = [model.model_type for model in base_models]
        if len(set(model_types)) > 1:
            self.logger.warning("Base models have different types, ensemble may not work optimally")
        
        self.model_type = base_models[0].model_type
        
        self.logger.info(f"Initialized ensemble with {len(base_models)} base models using {ensemble_method}")
    
    def _create_model(self, **kwargs) -> None:
        """Ensemble doesn't create a single model."""
        pass
    
    def _fit_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Fit ensemble model.
        
        Args:
            X: Feature matrix
            y: Target variable(s)
        """
        self.logger.info("Training base models...")
        
        # Train all base models
        for i, model in enumerate(self.base_models):
            self.logger.info(f"Training base model {i+1}/{len(self.base_models)}: {model.model_name}")
            try:
                model.fit(X, y)
            except Exception as e:
                self.logger.error(f"Failed to train {model.model_name}: {e}")
                raise
        
        # Calculate ensemble weights or train meta-model
        if self.ensemble_method == 'weighted_average':
            self._calculate_weights(X, y)
        elif self.ensemble_method == 'stacking':
            self._train_meta_model(X, y)
        
        self.is_trained = True
        self.logger.info("Ensemble training completed")
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities using ensemble.
        
        Args:
            X: Feature matrix
            
        Returns:
            Ensemble class probabilities
        """
        if self.model_type != 'classification':
            raise ValueError("predict_proba only available for classification models")
        
        # Get predictions from all base models
        base_predictions = []
        for model in self.base_models:
            try:
                if model.is_trained:
                    pred = model.predict_proba(X)
                    base_predictions.append(pred)
                else:
                    self.logger.warning(f"Model {model.model_name} is not trained, skipping")
            except Exception as e:
                self.logger.warning(f"Failed to get predictions from {model.model_name}: {e}")
        
        if not base_predictions:
            raise ValueError("No base models provided valid predictions")
        
        # Combine predictions based on ensemble method
        if self.ensemble_method == 'weighted_average':
            return self._weighted_average_predictions(base_predictions)
        elif self.ensemble_method == 'voting':
            return self._voting_predictions(base_predictions)
        elif self.ensemble_method == 'stacking':
            return self._stacking_predictions(X, base_predictions)
        else:
            # Default to simple average
            return np.mean(base_predictions, axis=0)
    
    def _calculate_weights(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Calculate weights for weighted average ensemble.
        
        Args:
            X: Feature matrix
            y: Target variable
        """
        from sklearn.model_selection import cross_val_score, TimeSeriesSplit
        
        weights = []
        cv = TimeSeriesSplit(n_splits=3)
        
        for model in self.base_models:
            if model.is_trained:
                try:
                    # Use the trained model for cross-validation scoring
                    scores = cross_val_score(
                        model.model, X, y, cv=cv, 
                        scoring='accuracy' if self.model_type == 'classification' else 'neg_mean_squared_error'
                    )
                    weight = np.mean(scores)
                    weights.append(max(0, weight))  # Ensure non-negative weights
                except Exception as e:
                    self.logger.warning(f"Could not calculate weight for {model.model_name}: {e}")
                    weights.append(0.1)  # Small default weight
            else:
                weights.append(0.0)
        
        # Normalize weights
        total_weight = sum(weights)
        if total_weight > 0:
            self.model_weights = [w / total_weight for w in weights]
        else:
            # Equal weights if all failed
            self.model_weights = [1.0 / len(self.base_models)] * len(self.base_models)
        
        self.logger.info(f"Model weights: {dict(zip([m.model_name for m in self.base_models], self.model_weights))}")
    
    def _train_meta_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Train meta-model for stacking ensemble.
        
        Args:
            X: Feature matrix
            y: Target variable
        """
        from sklearn.model_selection import cross_val_predict, TimeSeriesSplit
        from sklearn.linear_model import LogisticRegression
        from sklearn.ensemble import RandomForestClassifier
        
        # Generate meta-features using cross-validation
        meta_features = []
        cv = TimeSeriesSplit(n_splits=3)
        
        for model in self.base_models:
            if model.is_trained:
                try:
                    if self.model_type == 'classification':
                        # Get cross-validated probability predictions
                        cv_preds = cross_val_predict(
                            model.model, X, y, cv=cv, method='predict_proba'
                        )
                        meta_features.append(cv_preds)
                    else:
                        cv_preds = cross_val_predict(model.model, X, y, cv=cv)
                        meta_features.append(cv_preds.reshape(-1, 1))
                except Exception as e:
                    self.logger.warning(f"Could not generate meta-features for {model.model_name}: {e}")
        
        if meta_features:
            # Combine meta-features
            if self.model_type == 'classification':
                X_meta = np.hstack(meta_features)
            else:
                X_meta = np.hstack(meta_features)
            
            # Train meta-model
            if self.model_type == 'classification':
                self.meta_model = LogisticRegression(random_state=42, max_iter=1000)
            else:
                from sklearn.linear_model import LinearRegression
                self.meta_model = LinearRegression()
            
            self.meta_model.fit(X_meta, y)
            self.logger.info("Meta-model trained successfully")
        else:
            self.logger.error("Could not generate meta-features for stacking")
            self.ensemble_method = 'weighted_average'  # Fallback
            self._calculate_weights(X, y)
    
    def _weighted_average_predictions(self, base_predictions: List[np.ndarray]) -> np.ndarray:
        """Combine predictions using weighted average.
        
        Args:
            base_predictions: List of prediction arrays from base models
            
        Returns:
            Weighted average predictions
        """
        if self.model_weights is None:
            # Equal weights
            return np.mean(base_predictions, axis=0)
        
        # Apply weights
        weighted_preds = np.zeros_like(base_predictions[0])
        for pred, weight in zip(base_predictions, self.model_weights):
            weighted_preds += pred * weight
        
        return weighted_preds
    
    def _voting_predictions(self, base_predictions: List[np.ndarray]) -> np.ndarray:
        """Combine predictions using majority voting.
        
        Args:
            base_predictions: List of prediction arrays from base models
            
        Returns:
            Voting-based predictions
        """
        # Convert probabilities to class predictions
        class_predictions = [np.argmax(pred, axis=1) for pred in base_predictions]
        
        # Majority voting
        n_samples = len(class_predictions[0])
        n_classes = base_predictions[0].shape[1]
        
        voting_probs = np.zeros((n_samples, n_classes))
        
        for i in range(n_samples):
            votes = [pred[i] for pred in class_predictions]
            # Count votes for each class
            for class_idx in range(n_classes):
                voting_probs[i, class_idx] = votes.count(class_idx) / len(votes)
        
        return voting_probs
    
    def _stacking_predictions(self, X: pd.DataFrame, base_predictions: List[np.ndarray]) -> np.ndarray:
        """Combine predictions using stacking meta-model.
        
        Args:
            X: Original feature matrix
            base_predictions: List of prediction arrays from base models
            
        Returns:
            Meta-model predictions
        """
        if self.meta_model is None:
            self.logger.warning("Meta-model not trained, falling back to weighted average")
            return self._weighted_average_predictions(base_predictions)
        
        # Create meta-features
        if self.model_type == 'classification':
            X_meta = np.hstack(base_predictions)
        else:
            X_meta = np.hstack([pred.reshape(-1, 1) for pred in base_predictions])
        
        # Get meta-model predictions
        if self.model_type == 'classification':
            return self.meta_model.predict_proba(X_meta)
        else:
            return self.meta_model.predict(X_meta)
    
    def predict_match_outcome(self, X: pd.DataFrame) -> List[Dict[str, Any]]:
        """Predict match outcomes with ensemble-specific information.
        
        Args:
            X: Feature matrix for matches
            
        Returns:
            List of enhanced prediction dictionaries
        """
        base_predictions = super().predict_match_outcome(X)
        
        # Add ensemble-specific information
        for i, prediction in enumerate(base_predictions):
            # Get individual model predictions
            individual_predictions = {}
            for model in self.base_models:
                if model.is_trained:
                    try:
                        model_pred = model.predict_match_outcome(X.iloc[i:i+1])
                        if model_pred:
                            individual_predictions[model.model_name] = model_pred[0]
                    except Exception as e:
                        self.logger.debug(f"Could not get prediction from {model.model_name}: {e}")
            
            prediction['individual_predictions'] = individual_predictions
            prediction['ensemble_method'] = self.ensemble_method
            
            if self.model_weights:
                prediction['model_weights'] = dict(zip(
                    [m.model_name for m in self.base_models], 
                    self.model_weights
                ))
            
            # Calculate prediction agreement
            if individual_predictions:
                outcomes = [pred.get('predicted_outcome', '') for pred in individual_predictions.values()]
                unique_outcomes = set(outcomes)
                agreement = outcomes.count(prediction['predicted_outcome']) / len(outcomes)
                prediction['model_agreement'] = float(agreement)
                prediction['prediction_diversity'] = len(unique_outcomes)
        
        return base_predictions
    
    def get_model_contributions(self, X: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Get individual model contributions to ensemble predictions.
        
        Args:
            X: Feature matrix
            
        Returns:
            Dictionary mapping model names to their predictions
        """
        contributions = {}
        
        for model in self.base_models:
            if model.is_trained:
                try:
                    if self.model_type == 'classification':
                        pred = model.predict_proba(X)
                    else:
                        pred = model.predict(X)
                    contributions[model.model_name] = pred
                except Exception as e:
                    self.logger.warning(f"Could not get contribution from {model.model_name}: {e}")
        
        return contributions
    
    def get_ensemble_info(self) -> Dict[str, Any]:
        """Get comprehensive ensemble information.
        
        Returns:
            Dictionary with ensemble information
        """
        base_info = super().get_model_info()
        
        ensemble_info = {
            'ensemble_method': self.ensemble_method,
            'n_base_models': len(self.base_models),
            'base_models': [model.model_name for model in self.base_models],
            'model_weights': self.model_weights,
            'trained_models': sum(1 for model in self.base_models if model.is_trained)
        }
        
        base_info.update(ensemble_info)
        return base_info
