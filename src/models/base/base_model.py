"""Base model class for football prediction models."""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Union
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
import json
from pathlib import Path

from sklearn.model_selection import cross_val_score, TimeSeriesSplit, StratifiedKFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, log_loss
from src.utils.config import config


class BaseFootballModel(ABC):
    """Abstract base class for football prediction models."""
    
    def __init__(self, model_name: str, model_type: str = 'classification'):
        """Initialize base model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model ('classification', 'regression', 'multi_output')
        """
        self.model_name = model_name
        self.model_type = model_type
        self.model = None
        self.is_trained = False
        self.feature_names = []
        self.feature_importance = {}
        self.training_history = {}
        self.hyperparameters = {}
        
        self.logger = logging.getLogger(f"model.{model_name}")
        self.model_config = config.get_model_config()
    
    @abstractmethod
    def _create_model(self, **kwargs) -> Any:
        """Create the underlying ML model.
        
        Returns:
            Initialized model object
        """
        pass
    
    @abstractmethod
    def _fit_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Fit the model to training data.
        
        Args:
            X: Feature matrix
            y: Target variable(s)
        """
        pass
    
    @abstractmethod
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities.
        
        Args:
            X: Feature matrix
            
        Returns:
            Prediction probabilities
        """
        pass
    
    def fit(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame], 
            validation_data: Optional[Tuple[pd.DataFrame, Union[pd.Series, pd.DataFrame]]] = None,
            **kwargs) -> Dict[str, Any]:
        """Train the model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional training parameters
            
        Returns:
            Training results dictionary
        """
        self.logger.info(f"Training {self.model_name} model...")
        
        # Store feature names
        self.feature_names = list(X.columns)
        
        # Create model if not exists
        if self.model is None:
            self.model = self._create_model(**kwargs)
        
        # Record training start time
        training_start = datetime.now()
        
        # Fit the model
        self._fit_model(X, y)
        
        # Record training completion
        training_time = (datetime.now() - training_start).total_seconds()
        self.is_trained = True
        
        # Calculate feature importance if available
        self._calculate_feature_importance()
        
        # Evaluate on training data
        train_metrics = self.evaluate(X, y)
        
        # Evaluate on validation data if provided
        val_metrics = {}
        if validation_data is not None:
            X_val, y_val = validation_data
            val_metrics = self.evaluate(X_val, y_val)
        
        # Store training history
        self.training_history = {
            'training_time': training_time,
            'training_samples': len(X),
            'features_count': len(self.feature_names),
            'train_metrics': train_metrics,
            'validation_metrics': val_metrics,
            'training_date': datetime.now().isoformat()
        }
        
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        return self.training_history
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions.
        
        Args:
            X: Feature matrix
            
        Returns:
            Predictions
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        if self.model_type == 'classification':
            probabilities = self._predict_proba(X)
            if probabilities.shape[1] == 2:
                # Binary classification - return class with highest probability
                return (probabilities[:, 1] > 0.5).astype(int)
            else:
                # Multi-class classification
                return np.argmax(probabilities, axis=1)
        else:
            # Regression
            return self.model.predict(X)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities.
        
        Args:
            X: Feature matrix
            
        Returns:
            Class probabilities
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        if self.model_type != 'classification':
            raise ValueError("predict_proba only available for classification models")
        
        return self._predict_proba(X)
    
    def predict_match_outcome(self, X: pd.DataFrame) -> List[Dict[str, Any]]:
        """Predict match outcomes with probabilities and confidence.
        
        Args:
            X: Feature matrix for matches
            
        Returns:
            List of prediction dictionaries
        """
        if self.model_type != 'classification':
            raise ValueError("Match outcome prediction only available for classification models")
        
        probabilities = self.predict_proba(X)
        predictions = []
        
        for i, probs in enumerate(probabilities):
            if len(probs) == 3:  # Home/Draw/Away
                home_prob, draw_prob, away_prob = probs
                predicted_outcome = np.argmax(probs)
                outcome_labels = ['Home Win', 'Draw', 'Away Win']
                
                prediction = {
                    'match_index': i,
                    'predicted_outcome': outcome_labels[predicted_outcome],
                    'home_win_prob': float(home_prob),
                    'draw_prob': float(draw_prob),
                    'away_win_prob': float(away_prob),
                    'confidence': float(np.max(probs)),
                    'prediction_date': datetime.now().isoformat()
                }
            else:  # Binary classification
                prob = probs[1] if len(probs) == 2 else probs[0]
                prediction = {
                    'match_index': i,
                    'probability': float(prob),
                    'predicted_class': int(prob > 0.5),
                    'confidence': float(max(prob, 1 - prob)),
                    'prediction_date': datetime.now().isoformat()
                }
            
            predictions.append(prediction)
        
        return predictions
    
    def evaluate(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> Dict[str, float]:
        """Evaluate model performance.
        
        Args:
            X: Feature matrix
            y: True targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        predictions = self.predict(X)
        metrics = {}
        
        if self.model_type == 'classification':
            # Classification metrics
            metrics['accuracy'] = float(accuracy_score(y, predictions))
            
            # Handle multi-class vs binary
            average = 'weighted' if len(np.unique(y)) > 2 else 'binary'
            
            metrics['precision'] = float(precision_score(y, predictions, average=average, zero_division=0))
            metrics['recall'] = float(recall_score(y, predictions, average=average, zero_division=0))
            metrics['f1_score'] = float(f1_score(y, predictions, average=average, zero_division=0))
            
            # Probability-based metrics
            try:
                probabilities = self.predict_proba(X)
                if len(np.unique(y)) == 2:
                    metrics['roc_auc'] = float(roc_auc_score(y, probabilities[:, 1]))
                    metrics['log_loss'] = float(log_loss(y, probabilities))
                else:
                    metrics['roc_auc'] = float(roc_auc_score(y, probabilities, multi_class='ovr'))
                    metrics['log_loss'] = float(log_loss(y, probabilities))
            except Exception as e:
                self.logger.warning(f"Could not calculate probability-based metrics: {e}")
        
        else:
            # Regression metrics
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            
            metrics['mse'] = float(mean_squared_error(y, predictions))
            metrics['rmse'] = float(np.sqrt(metrics['mse']))
            metrics['mae'] = float(mean_absolute_error(y, predictions))
            metrics['r2'] = float(r2_score(y, predictions))
        
        return metrics
    
    def cross_validate(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame], 
                      cv_method: str = 'time_series', n_splits: int = 5) -> Dict[str, Any]:
        """Perform cross-validation.
        
        Args:
            X: Feature matrix
            y: Target variable(s)
            cv_method: Cross-validation method ('time_series', 'stratified', 'kfold')
            n_splits: Number of CV splits
            
        Returns:
            Cross-validation results
        """
        if self.model is None:
            self.model = self._create_model()
        
        # Choose CV strategy
        if cv_method == 'time_series':
            cv = TimeSeriesSplit(n_splits=n_splits)
        elif cv_method == 'stratified':
            cv = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
        else:
            from sklearn.model_selection import KFold
            cv = KFold(n_splits=n_splits, shuffle=True, random_state=42)
        
        # Perform cross-validation
        scoring = 'accuracy' if self.model_type == 'classification' else 'neg_mean_squared_error'
        cv_scores = cross_val_score(self.model, X, y, cv=cv, scoring=scoring)
        
        results = {
            'cv_scores': cv_scores.tolist(),
            'mean_score': float(cv_scores.mean()),
            'std_score': float(cv_scores.std()),
            'cv_method': cv_method,
            'n_splits': n_splits
        }
        
        self.logger.info(f"Cross-validation completed: {results['mean_score']:.4f} ± {results['std_score']:.4f}")
        return results
    
    def _calculate_feature_importance(self) -> None:
        """Calculate and store feature importance."""
        try:
            if hasattr(self.model, 'feature_importances_'):
                # Tree-based models
                importance_values = self.model.feature_importances_
            elif hasattr(self.model, 'coef_'):
                # Linear models
                importance_values = np.abs(self.model.coef_).flatten()
            else:
                self.logger.info("Feature importance not available for this model type")
                return
            
            # Create feature importance dictionary
            self.feature_importance = dict(zip(self.feature_names, importance_values))
            
            # Sort by importance
            self.feature_importance = dict(
                sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)
            )
            
        except Exception as e:
            self.logger.warning(f"Could not calculate feature importance: {e}")
    
    def get_feature_importance(self, top_n: int = 20) -> Dict[str, float]:
        """Get top N most important features.
        
        Args:
            top_n: Number of top features to return
            
        Returns:
            Dictionary of feature names and importance scores
        """
        if not self.feature_importance:
            return {}
        
        return dict(list(self.feature_importance.items())[:top_n])
    
    def save_model(self, filepath: str) -> None:
        """Save model to disk.
        
        Args:
            filepath: Path to save the model
        """
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        model_data = {
            'model': self.model,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'feature_names': self.feature_names,
            'feature_importance': self.feature_importance,
            'training_history': self.training_history,
            'hyperparameters': self.hyperparameters,
            'is_trained': self.is_trained
        }
        
        # Create directory if it doesn't exist
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # Save model
        joblib.dump(model_data, filepath)
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load model from disk.
        
        Args:
            filepath: Path to load the model from
        """
        model_data = joblib.load(filepath)
        
        self.model = model_data['model']
        self.model_name = model_data['model_name']
        self.model_type = model_data['model_type']
        self.feature_names = model_data['feature_names']
        self.feature_importance = model_data.get('feature_importance', {})
        self.training_history = model_data.get('training_history', {})
        self.hyperparameters = model_data.get('hyperparameters', {})
        self.is_trained = model_data.get('is_trained', False)
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information.
        
        Returns:
            Dictionary with model information
        """
        return {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names),
            'hyperparameters': self.hyperparameters,
            'training_history': self.training_history,
            'top_features': self.get_feature_importance(10)
        }
