"""XGBoost model for football prediction."""

import numpy as np
import pandas as pd
from typing import Dict, Any, Union, Optional
import xgboost as xgb
from sklearn.preprocessing import LabelEncoder

from ..base.base_model import BaseFootballModel


class XGBoostFootballModel(BaseFootballModel):
    """XGBoost model for football match prediction."""
    
    def __init__(self, model_name: str = "xgboost", model_type: str = "classification"):
        """Initialize XGBoost model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model ('classification' or 'regression')
        """
        super().__init__(model_name, model_type)
        self.label_encoder = None
        
        # Default hyperparameters
        self.default_params = {
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # Get model-specific config
        xgb_config = self.model_config.get('model_types', {}).get('xgboost', {})
        if xgb_config.get('enabled', True):
            self.default_params.update(xgb_config.get('params', {}))
    
    def _create_model(self, **kwargs) -> Union[xgb.XGBClassifier, xgb.XGBRegressor]:
        """Create XGBoost model.

        Returns:
            Initialized XGBoost model
        """
        # Merge default params with provided kwargs
        params = {**self.default_params, **kwargs}
        self.hyperparameters = params

        if self.model_type == 'classification':
            # Determine objective based on problem type
            if 'objective' not in params:
                params['objective'] = 'multi:softprob'  # Default for multi-class

            model = xgb.XGBClassifier(**params)
        else:
            if 'objective' not in params:
                params['objective'] = 'reg:squarederror'

            model = xgb.XGBRegressor(**params)

        return model
    
    def _fit_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Fit XGBoost model.

        Args:
            X: Feature matrix
            y: Target variable(s)
        """
        # Handle target encoding for classification
        if self.model_type == 'classification':
            if isinstance(y, pd.Series):
                y_encoded = self._encode_targets(y)
            else:
                y_encoded = y
        else:
            y_encoded = y

        # Ensure y_encoded is numpy array
        if hasattr(y_encoded, 'values'):
            y_encoded = y_encoded.values

        # Fit the model
        self.model.fit(X, y_encoded)

        self.logger.info(f"XGBoost model fitted with {len(X)} samples and {len(X.columns)} features")
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities.
        
        Args:
            X: Feature matrix
            
        Returns:
            Class probabilities
        """
        if self.model_type != 'classification':
            raise ValueError("predict_proba only available for classification models")
        
        return self.model.predict_proba(X)
    
    def _encode_targets(self, y: pd.Series) -> np.ndarray:
        """Encode target labels for classification.
        
        Args:
            y: Target labels
            
        Returns:
            Encoded labels
        """
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            return self.label_encoder.fit_transform(y)
        else:
            return self.label_encoder.transform(y)
    
    def predict_match_outcome(self, X: pd.DataFrame) -> list:
        """Predict match outcomes with XGBoost-specific enhancements.
        
        Args:
            X: Feature matrix for matches
            
        Returns:
            List of enhanced prediction dictionaries
        """
        base_predictions = super().predict_match_outcome(X)
        
        # Add XGBoost-specific information
        for i, prediction in enumerate(base_predictions):
            # Add feature contributions (SHAP-like values)
            if hasattr(self.model, 'predict_proba'):
                # Get prediction contributions if available
                try:
                    # This requires XGBoost with prediction contributions
                    contributions = self.model.get_booster().predict(
                        xgb.DMatrix(X.iloc[i:i+1]), pred_contribs=True
                    )
                    if len(contributions) > 0:
                        # Get top contributing features
                        feature_contribs = dict(zip(self.feature_names + ['bias'], contributions[0]))
                        # Sort by absolute contribution
                        sorted_contribs = sorted(
                            feature_contribs.items(), 
                            key=lambda x: abs(x[1]), 
                            reverse=True
                        )[:10]  # Top 10 contributors
                        
                        prediction['top_contributing_features'] = [
                            {'feature': feat, 'contribution': float(contrib)} 
                            for feat, contrib in sorted_contribs if feat != 'bias'
                        ]
                except Exception as e:
                    self.logger.debug(f"Could not get feature contributions: {e}")
            
            # Add model confidence based on prediction margin
            if 'home_win_prob' in prediction:
                probs = [prediction['home_win_prob'], prediction['draw_prob'], prediction['away_win_prob']]
                # Calculate entropy as uncertainty measure
                entropy = -sum(p * np.log(p + 1e-10) for p in probs if p > 0)
                max_entropy = np.log(3)  # Maximum entropy for 3 classes
                prediction['uncertainty'] = float(entropy / max_entropy)
                prediction['model_confidence'] = float(1 - prediction['uncertainty'])
        
        return base_predictions
    
    def get_feature_importance_plot_data(self) -> Dict[str, Any]:
        """Get data for plotting feature importance.
        
        Returns:
            Dictionary with plot data
        """
        if not self.feature_importance:
            return {}
        
        # Get top 20 features
        top_features = self.get_feature_importance(20)
        
        return {
            'features': list(top_features.keys()),
            'importance': list(top_features.values()),
            'importance_type': 'gain',  # XGBoost uses gain by default
            'model_type': 'XGBoost'
        }
    
    def tune_hyperparameters(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                           param_grid: Optional[Dict[str, list]] = None,
                           cv_folds: int = 5, scoring: str = 'accuracy') -> Dict[str, Any]:
        """Tune hyperparameters using grid search.
        
        Args:
            X: Feature matrix
            y: Target variable
            param_grid: Parameter grid for tuning
            cv_folds: Number of CV folds
            scoring: Scoring metric
            
        Returns:
            Best parameters and scores
        """
        from sklearn.model_selection import GridSearchCV, TimeSeriesSplit
        
        if param_grid is None:
            param_grid = {
                'max_depth': [3, 5, 7],
                'learning_rate': [0.01, 0.1, 0.2],
                'n_estimators': [100, 200, 300],
                'subsample': [0.8, 0.9, 1.0],
                'colsample_bytree': [0.8, 0.9, 1.0]
            }
        
        # Create base model
        base_model = self._create_model()
        
        # Use TimeSeriesSplit for time-aware CV
        cv = TimeSeriesSplit(n_splits=cv_folds)
        
        # Perform grid search
        grid_search = GridSearchCV(
            base_model, param_grid, cv=cv, scoring=scoring, 
            n_jobs=-1, verbose=1
        )
        
        # Encode targets if classification
        if self.model_type == 'classification':
            y_encoded = self._encode_targets(y)
        else:
            y_encoded = y
        
        grid_search.fit(X, y_encoded)
        
        # Update model with best parameters
        self.hyperparameters = grid_search.best_params_
        self.model = grid_search.best_estimator_
        self.is_trained = True
        
        results = {
            'best_params': grid_search.best_params_,
            'best_score': float(grid_search.best_score_),
            'cv_results': {
                'mean_test_scores': grid_search.cv_results_['mean_test_score'].tolist(),
                'std_test_scores': grid_search.cv_results_['std_test_score'].tolist(),
                'params': grid_search.cv_results_['params']
            }
        }
        
        self.logger.info(f"Hyperparameter tuning completed. Best score: {results['best_score']:.4f}")
        return results
    
    def get_learning_curve_data(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                              validation_data: Optional[tuple] = None) -> Dict[str, Any]:
        """Get learning curve data for plotting.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data (X_val, y_val)
            
        Returns:
            Learning curve data
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get learning curve data")
        
        # Get training history from XGBoost
        if hasattr(self.model, 'evals_result_'):
            evals_result = self.model.evals_result_
            
            learning_data = {
                'training_iterations': list(range(len(evals_result.get('validation_0', {}).get('logloss', [])))),
                'train_scores': evals_result.get('validation_0', {}).get('logloss', []),
                'val_scores': evals_result.get('validation_1', {}).get('logloss', []) if validation_data else []
            }
        else:
            # If no evaluation results, create basic learning curve
            learning_data = {
                'training_iterations': [],
                'train_scores': [],
                'val_scores': []
            }
        
        return learning_data
    
    def explain_prediction(self, X: pd.DataFrame, match_index: int = 0) -> Dict[str, Any]:
        """Explain a specific prediction using feature contributions.
        
        Args:
            X: Feature matrix
            match_index: Index of the match to explain
            
        Returns:
            Explanation dictionary
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to explain predictions")
        
        if match_index >= len(X):
            raise ValueError(f"Match index {match_index} out of range")
        
        # Get single match data
        match_data = X.iloc[match_index:match_index+1]
        
        # Get prediction
        if self.model_type == 'classification':
            prediction = self.predict_proba(match_data)[0]
            predicted_class = np.argmax(prediction)
        else:
            prediction = self.predict(match_data)[0]
            predicted_class = None
        
        # Get feature contributions
        explanation = {
            'match_index': match_index,
            'prediction': prediction.tolist() if hasattr(prediction, 'tolist') else float(prediction),
            'predicted_class': int(predicted_class) if predicted_class is not None else None,
            'feature_values': match_data.iloc[0].to_dict(),
            'top_features': self.get_feature_importance(10)
        }
        
        return explanation
