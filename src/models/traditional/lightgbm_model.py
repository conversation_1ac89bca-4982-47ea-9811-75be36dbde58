"""LightGBM model for football prediction."""

import numpy as np
import pandas as pd
from typing import Dict, Any, Union, Optional
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder

from ..base.base_model import BaseFootballModel


class LightGBMFootballModel(BaseFootballModel):
    """LightGBM model for football match prediction."""
    
    def __init__(self, model_name: str = "lightgbm", model_type: str = "classification"):
        """Initialize LightGBM model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model ('classification' or 'regression')
        """
        super().__init__(model_name, model_type)
        self.label_encoder = None
        
        # Default hyperparameters
        self.default_params = {
            'num_leaves': 31,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        # Get model-specific config
        lgb_config = self.model_config.get('model_types', {}).get('lightgbm', {})
        if lgb_config.get('enabled', True):
            self.default_params.update(lgb_config.get('params', {}))
    
    def _create_model(self, **kwargs) -> Union[lgb.LGBMClassifier, lgb.LGBMRegressor]:
        """Create LightGBM model.
        
        Returns:
            Initialized LightGBM model
        """
        # Merge default params with provided kwargs
        params = {**self.default_params, **kwargs}
        self.hyperparameters = params
        
        if self.model_type == 'classification':
            # Set objective for multi-class classification
            if 'objective' not in params:
                params['objective'] = 'multiclass'
            
            model = lgb.LGBMClassifier(**params)
        else:
            if 'objective' not in params:
                params['objective'] = 'regression'
            
            model = lgb.LGBMRegressor(**params)
        
        return model
    
    def _fit_model(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame]) -> None:
        """Fit LightGBM model.

        Args:
            X: Feature matrix
            y: Target variable(s)
        """
        # Handle target encoding for classification
        if self.model_type == 'classification':
            if isinstance(y, pd.Series):
                y_encoded = self._encode_targets(y)
            else:
                y_encoded = y
        else:
            y_encoded = y

        # Ensure y_encoded is numpy array
        if hasattr(y_encoded, 'values'):
            y_encoded = y_encoded.values

        # Fit the model
        self.model.fit(X, y_encoded)

        self.logger.info(f"LightGBM model fitted with {len(X)} samples and {len(X.columns)} features")
    
    def _predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict class probabilities.
        
        Args:
            X: Feature matrix
            
        Returns:
            Class probabilities
        """
        if self.model_type != 'classification':
            raise ValueError("predict_proba only available for classification models")
        
        return self.model.predict_proba(X)
    
    def _encode_targets(self, y: pd.Series) -> np.ndarray:
        """Encode target labels for classification.
        
        Args:
            y: Target labels
            
        Returns:
            Encoded labels
        """
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            return self.label_encoder.fit_transform(y)
        else:
            return self.label_encoder.transform(y)
    
    def fit_with_early_stopping(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                               X_val: pd.DataFrame, y_val: Union[pd.Series, pd.DataFrame],
                               early_stopping_rounds: int = 50) -> Dict[str, Any]:
        """Fit model with early stopping.
        
        Args:
            X: Training features
            y: Training targets
            X_val: Validation features
            y_val: Validation targets
            early_stopping_rounds: Number of rounds for early stopping
            
        Returns:
            Training results with early stopping info
        """
        # Create model if not exists
        if self.model is None:
            self.model = self._create_model()
        
        # Encode targets if classification
        if self.model_type == 'classification':
            y_encoded = self._encode_targets(y)
            y_val_encoded = self._encode_targets(y_val)
        else:
            y_encoded = y
            y_val_encoded = y_val
        
        # Fit with early stopping
        self.model.fit(
            X, y_encoded,
            eval_set=[(X_val, y_val_encoded)],
            eval_names=['validation'],
            callbacks=[lgb.early_stopping(early_stopping_rounds), lgb.log_evaluation(0)]
        )
        
        self.is_trained = True
        
        # Get training results
        results = {
            'best_iteration': self.model.best_iteration_,
            'best_score': self.model.best_score_['validation'],
            'early_stopping_rounds': early_stopping_rounds,
            'n_estimators_used': self.model.best_iteration_
        }
        
        self.logger.info(f"Training completed with early stopping at iteration {results['best_iteration']}")
        return results
    
    def predict_match_outcome(self, X: pd.DataFrame) -> list:
        """Predict match outcomes with LightGBM-specific enhancements.
        
        Args:
            X: Feature matrix for matches
            
        Returns:
            List of enhanced prediction dictionaries
        """
        base_predictions = super().predict_match_outcome(X)
        
        # Add LightGBM-specific information
        for i, prediction in enumerate(base_predictions):
            # Add prediction leaf indices (can be useful for similarity analysis)
            try:
                leaf_indices = self.model.predict(X.iloc[i:i+1], pred_leaf=True)
                prediction['leaf_indices'] = leaf_indices[0].tolist() if hasattr(leaf_indices[0], 'tolist') else [int(leaf_indices[0])]
            except Exception as e:
                self.logger.debug(f"Could not get leaf indices: {e}")
            
            # Add model-specific confidence metrics
            if 'home_win_prob' in prediction:
                probs = [prediction['home_win_prob'], prediction['draw_prob'], prediction['away_win_prob']]
                
                # Calculate Gini coefficient as a measure of prediction certainty
                sorted_probs = sorted(probs, reverse=True)
                gini = 1 - sum((2 * i + 1) * p for i, p in enumerate(sorted(probs))) / sum(probs)
                prediction['gini_coefficient'] = float(gini)
                
                # Calculate prediction margin (difference between top two probabilities)
                prediction['prediction_margin'] = float(sorted_probs[0] - sorted_probs[1])
        
        return base_predictions
    
    def get_feature_importance_plot_data(self) -> Dict[str, Any]:
        """Get data for plotting feature importance.
        
        Returns:
            Dictionary with plot data
        """
        if not self.feature_importance:
            return {}
        
        # Get top 20 features
        top_features = self.get_feature_importance(20)
        
        # LightGBM also provides split-based importance
        split_importance = {}
        if hasattr(self.model, 'feature_importances_'):
            # Get split importance
            try:
                booster = self.model.booster_
                split_imp = booster.feature_importance(importance_type='split')
                split_importance = dict(zip(self.feature_names, split_imp))
                split_importance = dict(
                    sorted(split_importance.items(), key=lambda x: x[1], reverse=True)
                )
            except Exception as e:
                self.logger.debug(f"Could not get split importance: {e}")
        
        return {
            'features': list(top_features.keys()),
            'gain_importance': list(top_features.values()),
            'split_importance': [split_importance.get(feat, 0) for feat in top_features.keys()],
            'importance_type': 'gain',
            'model_type': 'LightGBM'
        }
    
    def tune_hyperparameters(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                           param_grid: Optional[Dict[str, list]] = None,
                           cv_folds: int = 5, scoring: str = 'accuracy') -> Dict[str, Any]:
        """Tune hyperparameters using Optuna for efficient search.
        
        Args:
            X: Feature matrix
            y: Target variable
            param_grid: Parameter ranges for tuning
            cv_folds: Number of CV folds
            scoring: Scoring metric
            
        Returns:
            Best parameters and scores
        """
        try:
            import optuna
            from sklearn.model_selection import cross_val_score, TimeSeriesSplit
            
            # Default parameter ranges
            if param_grid is None:
                param_ranges = {
                    'num_leaves': (10, 100),
                    'learning_rate': (0.01, 0.3),
                    'n_estimators': (50, 500),
                    'subsample': (0.6, 1.0),
                    'colsample_bytree': (0.6, 1.0),
                    'min_child_samples': (5, 50)
                }
            else:
                param_ranges = param_grid
            
            # Encode targets if classification
            if self.model_type == 'classification':
                y_encoded = self._encode_targets(y)
            else:
                y_encoded = y
            
            def objective(trial):
                # Sample parameters
                params = {}
                for param, (low, high) in param_ranges.items():
                    if param in ['num_leaves', 'n_estimators', 'min_child_samples']:
                        params[param] = trial.suggest_int(param, low, high)
                    else:
                        params[param] = trial.suggest_float(param, low, high)
                
                # Add fixed parameters
                params.update({
                    'random_state': 42,
                    'n_jobs': -1,
                    'verbose': -1
                })
                
                # Create model
                if self.model_type == 'classification':
                    model = lgb.LGBMClassifier(**params)
                else:
                    model = lgb.LGBMRegressor(**params)
                
                # Cross-validation
                cv = TimeSeriesSplit(n_splits=cv_folds)
                scores = cross_val_score(model, X, y_encoded, cv=cv, scoring=scoring)
                
                return scores.mean()
            
            # Run optimization
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=50, show_progress_bar=True)
            
            # Update model with best parameters
            best_params = study.best_params
            best_params.update({'random_state': 42, 'n_jobs': -1, 'verbose': -1})
            
            self.hyperparameters = best_params
            self.model = self._create_model(**best_params)
            
            # Fit with best parameters
            self._fit_model(X, y)
            
            results = {
                'best_params': best_params,
                'best_score': float(study.best_value),
                'n_trials': len(study.trials),
                'optimization_history': [trial.value for trial in study.trials if trial.value is not None]
            }
            
            self.logger.info(f"Hyperparameter tuning completed. Best score: {results['best_score']:.4f}")
            return results
            
        except ImportError:
            self.logger.warning("Optuna not available, falling back to grid search")
            return self._grid_search_tuning(X, y, param_grid, cv_folds, scoring)
    
    def _grid_search_tuning(self, X: pd.DataFrame, y: Union[pd.Series, pd.DataFrame],
                           param_grid: Optional[Dict[str, list]], cv_folds: int, scoring: str) -> Dict[str, Any]:
        """Fallback grid search tuning."""
        from sklearn.model_selection import GridSearchCV, TimeSeriesSplit
        
        if param_grid is None:
            param_grid = {
                'num_leaves': [31, 50, 100],
                'learning_rate': [0.01, 0.1, 0.2],
                'n_estimators': [100, 200, 300]
            }
        
        base_model = self._create_model()
        cv = TimeSeriesSplit(n_splits=cv_folds)
        
        grid_search = GridSearchCV(
            base_model, param_grid, cv=cv, scoring=scoring, 
            n_jobs=-1, verbose=1
        )
        
        if self.model_type == 'classification':
            y_encoded = self._encode_targets(y)
        else:
            y_encoded = y
        
        grid_search.fit(X, y_encoded)
        
        self.hyperparameters = grid_search.best_params_
        self.model = grid_search.best_estimator_
        self.is_trained = True
        
        return {
            'best_params': grid_search.best_params_,
            'best_score': float(grid_search.best_score_),
            'method': 'grid_search'
        }
    
    def get_training_progress(self) -> Dict[str, Any]:
        """Get training progress information.
        
        Returns:
            Training progress data
        """
        if not hasattr(self.model, 'evals_result_'):
            return {}
        
        evals_result = self.model.evals_result_
        
        progress_data = {
            'iterations': list(range(1, len(list(evals_result.values())[0].values())[0] + 1)),
            'training_scores': [],
            'validation_scores': []
        }
        
        # Extract training and validation scores
        for eval_name, metrics in evals_result.items():
            for metric_name, scores in metrics.items():
                if 'train' in eval_name.lower():
                    progress_data['training_scores'] = scores
                elif 'valid' in eval_name.lower():
                    progress_data['validation_scores'] = scores
        
        return progress_data
