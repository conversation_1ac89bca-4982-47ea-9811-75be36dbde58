"""Machine learning models for football prediction."""

from .base.base_model import BaseFootballModel
from .traditional.xgboost_model import XGBoostFootballModel
from .traditional.lightgbm_model import LightGBMFootballModel
from .ensemble.ensemble_model import EnsembleFootballModel
from .training.model_trainer import ModelTrainer

# Optional neural network model (requires Tensor<PERSON>low)
try:
    import tensorflow as tf
    from .neural.neural_network_model import NeuralNetworkFootballModel
    NEURAL_NETWORK_AVAILABLE = True
except ImportError:
    NeuralNetworkFootballModel = None
    NEURAL_NETWORK_AVAILABLE = False

__all__ = [
    'BaseFootballModel',
    'XGBoostFootballModel',
    'LightGBMFootballModel',
    'EnsembleFootballModel',
    'ModelTrainer'
]

if NEURAL_NETWORK_AVAILABLE:
    __all__.append('NeuralNetworkFootballModel')
