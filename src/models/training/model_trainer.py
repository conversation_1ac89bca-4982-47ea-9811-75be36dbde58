"""Model training pipeline for football prediction models."""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import joblib
import json

from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import LabelEncoder

from ..traditional.xgboost_model import XGBoostFootballModel
from ..traditional.lightgbm_model import LightGBMFootballModel
from ..ensemble.ensemble_model import EnsembleFootballModel

# Optional neural network model
try:
    import tensorflow as tf
    from ..neural.neural_network_model import NeuralNetworkFootballModel
    NEURAL_NETWORK_AVAILABLE = True
except ImportError:
    NeuralNetworkFootballModel = None
    NEURAL_NETWORK_AVAILABLE = False
from src.utils.config import config


class ModelTrainer:
    """Orchestrates the training of multiple football prediction models."""
    
    def __init__(self):
        """Initialize model trainer."""
        self.logger = logging.getLogger("model_trainer")
        self.models = {}
        self.training_results = {}
        self.label_encoder = LabelEncoder()
        
        # Get training configuration
        self.training_config = config.get_model_config()
        
        # Available model types
        self.available_models = {
            'xgboost': XGBoostFootballModel,
            'lightgbm': LightGBMFootballModel
        }

        # Add additional models
        try:
            from src.models.implementations.random_forest_model import RandomForestFootballModel
            self.available_models['random_forest'] = RandomForestFootballModel
        except ImportError:
            pass

        try:
            from src.models.implementations.neural_network_model import NeuralNetworkFootballModel
            self.available_models['neural_network'] = NeuralNetworkFootballModel
        except ImportError:
            pass

        try:
            from src.models.implementations.ensemble_model import EnsembleFootballModel
            self.available_models['ensemble'] = EnsembleFootballModel
        except ImportError:
            pass

        # Add neural network if available
        if NEURAL_NETWORK_AVAILABLE:
            self.available_models['neural_network'] = NeuralNetworkFootballModel
    
    def prepare_data(self, features_df: pd.DataFrame, 
                    target_column: str = 'result',
                    test_size: float = 0.2,
                    validation_size: float = 0.1,
                    time_split: bool = True) -> Dict[str, Any]:
        """Prepare data for training.
        
        Args:
            features_df: Feature matrix with targets
            target_column: Name of target column
            test_size: Proportion of data for testing
            validation_size: Proportion of data for validation
            time_split: Whether to use time-based splitting
            
        Returns:
            Dictionary with train/validation/test splits
        """
        self.logger.info("Preparing data for training...")
        
        # Separate features and targets
        if target_column not in features_df.columns:
            raise ValueError(f"Target column '{target_column}' not found in features")
        
        # Remove rows with missing targets
        clean_df = features_df.dropna(subset=[target_column]).copy()
        
        X = clean_df.drop(columns=[target_column])
        y = clean_df[target_column]
        
        # Remove only non-predictive columns (keep derived features like total_goals, goal_difference)
        non_feature_cols = ['match_id', 'home_team_id', 'away_team_id', 'match_date',
                           'league_id', 'season', 'home_score', 'away_score']
        X = X.drop(columns=[col for col in non_feature_cols if col in X.columns])

        # Keep derived features that are valuable for prediction:
        # - total_goals, goal_difference, over_2_5, both_teams_score
        # These are calculated from historical data and help the model learn patterns
        
        # Handle categorical targets
        if y.dtype == 'object':
            y_encoded = self.label_encoder.fit_transform(y)
            self.logger.info(f"Encoded target classes: {dict(zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_))))}")
        else:
            y_encoded = y.values if hasattr(y, 'values') else y
        
        if time_split and 'match_date' in clean_df.columns:
            # Time-based split
            data_splits = self._time_based_split(X, y_encoded, clean_df['match_date'], test_size, validation_size)
        else:
            # Random split
            data_splits = self._random_split(X, y_encoded, test_size, validation_size)
        
        # Log data statistics
        self.logger.info(f"Data prepared:")
        self.logger.info(f"  Total samples: {len(X)}")
        self.logger.info(f"  Features: {len(X.columns)}")
        self.logger.info(f"  Train samples: {len(data_splits['X_train'])}")
        self.logger.info(f"  Validation samples: {len(data_splits['X_val'])}")
        self.logger.info(f"  Test samples: {len(data_splits['X_test'])}")
        
        return data_splits
    
    def _time_based_split(self, X: pd.DataFrame, y: np.ndarray, dates: pd.Series,
                         test_size: float, validation_size: float) -> Dict[str, Any]:
        """Split data based on time order.
        
        Args:
            X: Features
            y: Targets
            dates: Match dates
            test_size: Test set proportion
            validation_size: Validation set proportion
            
        Returns:
            Data splits dictionary
        """
        # Sort by date
        sorted_indices = dates.sort_values().index
        X_sorted = X.loc[sorted_indices]
        y_sorted = y[sorted_indices]
        
        n_samples = len(X_sorted)
        n_test = int(n_samples * test_size)
        n_val = int(n_samples * validation_size)
        n_train = n_samples - n_test - n_val
        
        # Split chronologically
        X_train = X_sorted.iloc[:n_train]
        y_train = y_sorted[:n_train]
        
        X_val = X_sorted.iloc[n_train:n_train + n_val]
        y_val = y_sorted[n_train:n_train + n_val]
        
        X_test = X_sorted.iloc[n_train + n_val:]
        y_test = y_sorted[n_train + n_val:]
        
        return {
            'X_train': X_train, 'y_train': y_train,
            'X_val': X_val, 'y_val': y_val,
            'X_test': X_test, 'y_test': y_test,
            'split_method': 'time_based'
        }
    
    def _random_split(self, X: pd.DataFrame, y: np.ndarray,
                     test_size: float, validation_size: float) -> Dict[str, Any]:
        """Split data randomly.
        
        Args:
            X: Features
            y: Targets
            test_size: Test set proportion
            validation_size: Validation set proportion
            
        Returns:
            Data splits dictionary
        """
        # First split: train+val vs test
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        # Second split: train vs val
        val_size_adjusted = validation_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=42, stratify=y_temp
        )
        
        return {
            'X_train': X_train, 'y_train': y_train,
            'X_val': X_val, 'y_val': y_val,
            'X_test': X_test, 'y_test': y_test,
            'split_method': 'random'
        }
    
    def train_models(self, data_splits: Dict[str, Any],
                    model_types: Optional[List[str]] = None,
                    tune_hyperparameters: bool = True) -> Dict[str, Any]:
        """Train multiple models.
        
        Args:
            data_splits: Data splits from prepare_data
            model_types: List of model types to train (None for all enabled)
            tune_hyperparameters: Whether to tune hyperparameters
            
        Returns:
            Training results dictionary
        """
        self.logger.info("Starting model training...")
        
        # Determine which models to train
        if model_types is None:
            enabled_models = self.training_config.get('model_types', {})
            model_types = [name for name, config in enabled_models.items() 
                          if config.get('enabled', True) and name in self.available_models]
        
        if not model_types:
            model_types = ['xgboost', 'lightgbm']  # Default models
        
        self.logger.info(f"Training models: {model_types}")
        
        # Extract data
        X_train, y_train = data_splits['X_train'], data_splits['y_train']
        X_val, y_val = data_splits['X_val'], data_splits['y_val']
        X_test, y_test = data_splits['X_test'], data_splits['y_test']
        
        training_results = {}
        
        # Train each model type
        for model_type in model_types:
            self.logger.info(f"Training {model_type} model...")
            
            try:
                # Create model
                model_class = self.available_models[model_type]
                model = model_class(model_name=f"{model_type}_football")
                
                # Tune hyperparameters if requested
                if tune_hyperparameters:
                    self.logger.info(f"Tuning hyperparameters for {model_type}...")
                    tuning_results = model.tune_hyperparameters(X_train, y_train)
                    training_results[f"{model_type}_tuning"] = tuning_results
                
                # Train model
                training_history = model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val)
                )
                
                # Evaluate on test set
                test_metrics = model.evaluate(X_test, y_test)
                
                # Store model and results
                self.models[model_type] = model
                training_results[model_type] = {
                    'training_history': training_history,
                    'test_metrics': test_metrics,
                    'model_info': model.get_model_info()
                }
                
                self.logger.info(f"{model_type} training completed. Test accuracy: {test_metrics.get('accuracy', 'N/A'):.4f}")
                
            except Exception as e:
                self.logger.error(f"Failed to train {model_type}: {e}")
                training_results[model_type] = {'error': str(e)}
        
        # Train ensemble if multiple models were trained successfully
        successful_models = [name for name in model_types if name in self.models]
        if len(successful_models) > 1:
            self.logger.info("Training ensemble model...")
            try:
                base_models = [self.models[name] for name in successful_models]
                ensemble = EnsembleFootballModel(base_models, ensemble_method='weighted_average')
                
                ensemble_history = ensemble.fit(X_train, y_train)
                ensemble_test_metrics = ensemble.evaluate(X_test, y_test)
                
                self.models['ensemble'] = ensemble
                training_results['ensemble'] = {
                    'training_history': ensemble_history,
                    'test_metrics': ensemble_test_metrics,
                    'model_info': ensemble.get_ensemble_info()
                }
                
                self.logger.info(f"Ensemble training completed. Test accuracy: {ensemble_test_metrics.get('accuracy', 'N/A'):.4f}")
                
            except Exception as e:
                self.logger.error(f"Failed to train ensemble: {e}")
                training_results['ensemble'] = {'error': str(e)}
        
        self.training_results = training_results
        self.logger.info("Model training completed")
        
        return training_results
    
    def compare_models(self) -> pd.DataFrame:
        """Compare performance of trained models.
        
        Returns:
            DataFrame with model comparison
        """
        if not self.training_results:
            return pd.DataFrame()
        
        comparison_data = []
        
        for model_name, results in self.training_results.items():
            if 'test_metrics' in results:
                metrics = results['test_metrics']
                model_info = results.get('model_info', {})
                
                row = {
                    'model': model_name,
                    'accuracy': metrics.get('accuracy', np.nan),
                    'precision': metrics.get('precision', np.nan),
                    'recall': metrics.get('recall', np.nan),
                    'f1_score': metrics.get('f1_score', np.nan),
                    'roc_auc': metrics.get('roc_auc', np.nan),
                    'log_loss': metrics.get('log_loss', np.nan),
                    'training_time': model_info.get('training_history', {}).get('training_time', np.nan),
                    'feature_count': model_info.get('feature_count', np.nan)
                }
                comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        if not comparison_df.empty:
            # Sort by accuracy (descending)
            comparison_df = comparison_df.sort_values('accuracy', ascending=False)
        
        return comparison_df
    
    def save_models(self, save_dir: str = "data/models") -> Dict[str, str]:
        """Save all trained models.
        
        Args:
            save_dir: Directory to save models
            
        Returns:
            Dictionary mapping model names to file paths
        """
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        saved_paths = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for model_name, model in self.models.items():
            try:
                filename = f"{model_name}_model_{timestamp}.joblib"
                filepath = save_dir / filename
                model.save_model(str(filepath))
                saved_paths[model_name] = str(filepath)
                self.logger.info(f"Saved {model_name} to {filepath}")
            except Exception as e:
                self.logger.error(f"Failed to save {model_name}: {e}")
        
        # Save training results
        results_file = save_dir / f"training_results_{timestamp}.json"
        try:
            with open(results_file, 'w') as f:
                # Convert numpy types to native Python types for JSON serialization
                serializable_results = self._make_json_serializable(self.training_results)
                json.dump(serializable_results, f, indent=2)
            self.logger.info(f"Saved training results to {results_file}")
        except Exception as e:
            self.logger.error(f"Failed to save training results: {e}")
        
        return saved_paths
    
    def _make_json_serializable(self, obj):
        """Convert numpy types to JSON serializable types."""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def get_best_model(self, metric: str = 'accuracy') -> Optional[Any]:
        """Get the best performing model based on a metric.
        
        Args:
            metric: Metric to use for comparison
            
        Returns:
            Best model or None if no models trained
        """
        if not self.training_results:
            return None
        
        best_score = -np.inf
        best_model = None
        
        for model_name, results in self.training_results.items():
            if 'test_metrics' in results:
                score = results['test_metrics'].get(metric, -np.inf)
                if score > best_score:
                    best_score = score
                    best_model = self.models.get(model_name)
        
        return best_model
    
    def generate_training_report(self) -> str:
        """Generate a comprehensive training report.
        
        Returns:
            Formatted training report
        """
        if not self.training_results:
            return "No training results available."
        
        report = ["Football Prediction Model Training Report", "=" * 50, ""]
        
        # Model comparison
        comparison_df = self.compare_models()
        if not comparison_df.empty:
            report.append("Model Performance Comparison:")
            report.append("-" * 30)
            report.append(comparison_df.to_string(index=False, float_format='%.4f'))
            report.append("")
        
        # Best model
        best_model = self.get_best_model()
        if best_model:
            report.append(f"Best Model: {best_model.model_name}")
            report.append("")
        
        # Feature importance (from best model)
        if best_model and hasattr(best_model, 'get_feature_importance'):
            try:
                top_features = best_model.get_feature_importance(10)
                if isinstance(top_features, pd.DataFrame) and not top_features.empty:
                    report.append("Top 10 Most Important Features:")
                    report.append("-" * 30)
                    for _, row in top_features.iterrows():
                        report.append(f"  {row['feature']}: {row['importance']:.4f}")
                    report.append("")
                elif isinstance(top_features, dict) and top_features:
                    report.append("Top 10 Most Important Features:")
                    report.append("-" * 30)
                    for feature, importance in list(top_features.items())[:10]:
                        report.append(f"  {feature}: {importance:.4f}")
                    report.append("")
            except Exception as e:
                self.logger.warning(f"Could not get feature importance: {e}")
        
        # Training summary
        report.append("Training Summary:")
        report.append("-" * 20)
        successful_models = len([r for r in self.training_results.values() if 'test_metrics' in r])
        failed_models = len([r for r in self.training_results.values() if 'error' in r])
        report.append(f"  Successful models: {successful_models}")
        report.append(f"  Failed models: {failed_models}")
        report.append(f"  Training date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(report)
