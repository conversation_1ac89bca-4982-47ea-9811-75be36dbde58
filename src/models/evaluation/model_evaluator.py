"""Comprehensive model evaluation system for football prediction models."""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
# Optional plotting libraries
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    plt = None
    sns = None
    PLOTTING_AVAILABLE = False
from pathlib import Path

from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, log_loss, confusion_matrix, classification_report
)
from sklearn.model_selection import TimeSeriesSplit, cross_val_score

from ..base.base_model import BaseFootballModel
from src.utils.config import config


class ModelEvaluator:
    """Comprehensive evaluation system for football prediction models."""
    
    def __init__(self):
        """Initialize model evaluator."""
        self.logger = logging.getLogger("model_evaluator")
        self.evaluation_config = config.get_model_config().get('evaluation', {})
        
        # Evaluation metrics for different prediction types
        self.classification_metrics = [
            'accuracy', 'precision', 'recall', 'f1_score', 'roc_auc', 'log_loss'
        ]
        
        self.regression_metrics = [
            'mse', 'rmse', 'mae', 'r2'
        ]
    
    def evaluate_model(self, model: BaseFootballModel, 
                      X_test: pd.DataFrame, y_test: np.ndarray,
                      X_train: Optional[pd.DataFrame] = None,
                      y_train: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """Comprehensive evaluation of a single model.
        
        Args:
            model: Trained model to evaluate
            X_test: Test features
            y_test: Test targets
            X_train: Optional training features for additional analysis
            y_train: Optional training targets for additional analysis
            
        Returns:
            Dictionary with comprehensive evaluation results
        """
        self.logger.info(f"Evaluating model: {model.model_name}")
        
        evaluation_results = {
            'model_name': model.model_name,
            'model_type': model.model_type,
            'evaluation_date': datetime.now().isoformat(),
            'test_samples': len(X_test)
        }
        
        # Basic performance metrics
        basic_metrics = model.evaluate(X_test, y_test)
        evaluation_results['basic_metrics'] = basic_metrics
        
        # Detailed classification analysis
        if model.model_type == 'classification':
            classification_analysis = self._evaluate_classification(model, X_test, y_test)
            evaluation_results.update(classification_analysis)
        
        # Feature importance analysis
        if hasattr(model, 'get_feature_importance'):
            feature_importance = model.get_feature_importance(20)
            evaluation_results['feature_importance'] = feature_importance
        
        # Prediction confidence analysis
        confidence_analysis = self._analyze_prediction_confidence(model, X_test, y_test)
        evaluation_results['confidence_analysis'] = confidence_analysis
        
        # Time-based performance if training data provided
        if X_train is not None and y_train is not None:
            temporal_analysis = self._analyze_temporal_performance(model, X_train, y_train, X_test, y_test)
            evaluation_results['temporal_analysis'] = temporal_analysis
        
        # Football-specific metrics
        football_metrics = self._calculate_football_metrics(model, X_test, y_test)
        evaluation_results['football_metrics'] = football_metrics
        
        self.logger.info(f"Evaluation completed for {model.model_name}")
        return evaluation_results
    
    def _evaluate_classification(self, model: BaseFootballModel, 
                               X_test: pd.DataFrame, y_test: np.ndarray) -> Dict[str, Any]:
        """Detailed classification evaluation.
        
        Args:
            model: Trained classification model
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Classification evaluation results
        """
        predictions = model.predict(X_test)
        probabilities = model.predict_proba(X_test)
        
        # Confusion matrix
        cm = confusion_matrix(y_test, predictions)
        
        # Per-class metrics
        class_report = classification_report(y_test, predictions, output_dict=True)
        
        # ROC AUC for each class (if multi-class)
        roc_auc_per_class = {}
        if len(np.unique(y_test)) > 2:
            try:
                from sklearn.preprocessing import label_binarize
                y_test_bin = label_binarize(y_test, classes=np.unique(y_test))
                for i, class_label in enumerate(np.unique(y_test)):
                    roc_auc_per_class[f'class_{class_label}'] = roc_auc_score(
                        y_test_bin[:, i], probabilities[:, i]
                    )
            except Exception as e:
                self.logger.warning(f"Could not calculate per-class ROC AUC: {e}")
        
        return {
            'confusion_matrix': cm.tolist(),
            'classification_report': class_report,
            'roc_auc_per_class': roc_auc_per_class,
            'prediction_distribution': np.bincount(predictions).tolist()
        }
    
    def _analyze_prediction_confidence(self, model: BaseFootballModel,
                                     X_test: pd.DataFrame, y_test: np.ndarray) -> Dict[str, Any]:
        """Analyze prediction confidence and calibration.
        
        Args:
            model: Trained model
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Confidence analysis results
        """
        if model.model_type != 'classification':
            return {}
        
        probabilities = model.predict_proba(X_test)
        predictions = model.predict(X_test)
        
        # Maximum probability (confidence) for each prediction
        max_probs = np.max(probabilities, axis=1)
        
        # Accuracy by confidence bins
        confidence_bins = np.linspace(0, 1, 11)
        bin_accuracies = []
        bin_counts = []
        
        for i in range(len(confidence_bins) - 1):
            bin_mask = (max_probs >= confidence_bins[i]) & (max_probs < confidence_bins[i + 1])
            if np.sum(bin_mask) > 0:
                bin_accuracy = accuracy_score(y_test[bin_mask], predictions[bin_mask])
                bin_accuracies.append(bin_accuracy)
                bin_counts.append(np.sum(bin_mask))
            else:
                bin_accuracies.append(0.0)
                bin_counts.append(0)
        
        # Overall confidence statistics
        confidence_stats = {
            'mean_confidence': float(np.mean(max_probs)),
            'std_confidence': float(np.std(max_probs)),
            'min_confidence': float(np.min(max_probs)),
            'max_confidence': float(np.max(max_probs))
        }
        
        return {
            'confidence_stats': confidence_stats,
            'confidence_bins': confidence_bins.tolist(),
            'bin_accuracies': bin_accuracies,
            'bin_counts': bin_counts
        }
    
    def _analyze_temporal_performance(self, model: BaseFootballModel,
                                    X_train: pd.DataFrame, y_train: np.ndarray,
                                    X_test: pd.DataFrame, y_test: np.ndarray) -> Dict[str, Any]:
        """Analyze model performance over time.
        
        Args:
            model: Trained model
            X_train: Training features
            y_train: Training targets
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Temporal analysis results
        """
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        # Combine train and test data for temporal analysis
        X_combined = pd.concat([X_train, X_test], ignore_index=True)
        y_combined = np.concatenate([y_train, y_test])
        
        cv_scores = []
        for train_idx, val_idx in tscv.split(X_combined):
            X_cv_train, X_cv_val = X_combined.iloc[train_idx], X_combined.iloc[val_idx]
            y_cv_train, y_cv_val = y_combined[train_idx], y_combined[val_idx]
            
            # Create a copy of the model for CV
            try:
                temp_model = model.__class__(model_name=f"{model.model_name}_cv")
                temp_model.fit(X_cv_train, y_cv_train)
                score = temp_model.evaluate(X_cv_val, y_cv_val)
                cv_scores.append(score.get('accuracy', 0.0))
            except Exception as e:
                self.logger.warning(f"CV fold failed: {e}")
                cv_scores.append(0.0)
        
        return {
            'cv_scores': cv_scores,
            'cv_mean': float(np.mean(cv_scores)),
            'cv_std': float(np.std(cv_scores)),
            'temporal_stability': float(1.0 - np.std(cv_scores))  # Higher is more stable
        }
    
    def _calculate_football_metrics(self, model: BaseFootballModel,
                                  X_test: pd.DataFrame, y_test: np.ndarray) -> Dict[str, Any]:
        """Calculate football-specific evaluation metrics.
        
        Args:
            model: Trained model
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Football-specific metrics
        """
        if model.model_type != 'classification':
            return {}
        
        predictions = model.predict(X_test)
        probabilities = model.predict_proba(X_test)
        
        # Assuming classes are [Away Win, Draw, Home Win] or similar
        # Map to standard football outcomes
        class_mapping = {0: 'Away', 1: 'Draw', 2: 'Home'}  # Adjust based on your encoding
        
        # Home win accuracy
        home_mask = y_test == 2  # Assuming 2 is home win
        home_accuracy = accuracy_score(y_test[home_mask], predictions[home_mask]) if np.sum(home_mask) > 0 else 0.0
        
        # Away win accuracy
        away_mask = y_test == 0  # Assuming 0 is away win
        away_accuracy = accuracy_score(y_test[away_mask], predictions[away_mask]) if np.sum(away_mask) > 0 else 0.0
        
        # Draw accuracy
        draw_mask = y_test == 1  # Assuming 1 is draw
        draw_accuracy = accuracy_score(y_test[draw_mask], predictions[draw_mask]) if np.sum(draw_mask) > 0 else 0.0
        
        # Betting simulation (simple strategy)
        betting_results = self._simulate_betting(probabilities, y_test, predictions)
        
        return {
            'home_win_accuracy': float(home_accuracy),
            'away_win_accuracy': float(away_accuracy),
            'draw_accuracy': float(draw_accuracy),
            'outcome_distribution': {
                'home_wins': int(np.sum(y_test == 2)),
                'draws': int(np.sum(y_test == 1)),
                'away_wins': int(np.sum(y_test == 0))
            },
            'betting_simulation': betting_results
        }
    
    def _simulate_betting(self, probabilities: np.ndarray, y_true: np.ndarray, 
                         predictions: np.ndarray, confidence_threshold: float = 0.6) -> Dict[str, Any]:
        """Simulate betting strategy based on model predictions.
        
        Args:
            probabilities: Prediction probabilities
            y_true: True outcomes
            predictions: Model predictions
            confidence_threshold: Minimum confidence for betting
            
        Returns:
            Betting simulation results
        """
        # Simple betting strategy: bet on predictions with high confidence
        max_probs = np.max(probabilities, axis=1)
        confident_bets = max_probs >= confidence_threshold
        
        if np.sum(confident_bets) == 0:
            return {'total_bets': 0, 'winning_bets': 0, 'win_rate': 0.0, 'roi': 0.0}
        
        # Calculate wins (correct predictions among confident bets)
        confident_predictions = predictions[confident_bets]
        confident_true = y_true[confident_bets]
        winning_bets = np.sum(confident_predictions == confident_true)
        
        # Simple ROI calculation (assuming equal stakes and 2:1 odds)
        total_bets = np.sum(confident_bets)
        win_rate = winning_bets / total_bets
        roi = (winning_bets * 2 - total_bets) / total_bets  # Simplified ROI
        
        return {
            'total_bets': int(total_bets),
            'winning_bets': int(winning_bets),
            'win_rate': float(win_rate),
            'roi': float(roi),
            'confidence_threshold': confidence_threshold
        }
    
    def compare_models(self, evaluation_results: List[Dict[str, Any]]) -> pd.DataFrame:
        """Compare multiple model evaluation results.
        
        Args:
            evaluation_results: List of evaluation result dictionaries
            
        Returns:
            DataFrame with model comparison
        """
        comparison_data = []
        
        for result in evaluation_results:
            basic_metrics = result.get('basic_metrics', {})
            football_metrics = result.get('football_metrics', {})
            confidence_analysis = result.get('confidence_analysis', {})
            temporal_analysis = result.get('temporal_analysis', {})
            
            row = {
                'model': result['model_name'],
                'accuracy': basic_metrics.get('accuracy', np.nan),
                'precision': basic_metrics.get('precision', np.nan),
                'recall': basic_metrics.get('recall', np.nan),
                'f1_score': basic_metrics.get('f1_score', np.nan),
                'roc_auc': basic_metrics.get('roc_auc', np.nan),
                'log_loss': basic_metrics.get('log_loss', np.nan),
                'home_win_accuracy': football_metrics.get('home_win_accuracy', np.nan),
                'draw_accuracy': football_metrics.get('draw_accuracy', np.nan),
                'away_win_accuracy': football_metrics.get('away_win_accuracy', np.nan),
                'mean_confidence': confidence_analysis.get('confidence_stats', {}).get('mean_confidence', np.nan),
                'temporal_stability': temporal_analysis.get('temporal_stability', np.nan),
                'betting_roi': football_metrics.get('betting_simulation', {}).get('roi', np.nan)
            }
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        if not comparison_df.empty:
            # Sort by accuracy (descending)
            comparison_df = comparison_df.sort_values('accuracy', ascending=False)
        
        return comparison_df
    
    def generate_evaluation_report(self, evaluation_results: List[Dict[str, Any]]) -> str:
        """Generate comprehensive evaluation report.
        
        Args:
            evaluation_results: List of evaluation results
            
        Returns:
            Formatted evaluation report
        """
        report = ["Football Prediction Model Evaluation Report", "=" * 60, ""]
        
        # Model comparison
        comparison_df = self.compare_models(evaluation_results)
        if not comparison_df.empty:
            report.append("Model Performance Comparison:")
            report.append("-" * 40)
            report.append(comparison_df.to_string(index=False, float_format='%.4f'))
            report.append("")
        
        # Best model analysis
        if not comparison_df.empty:
            best_model_name = comparison_df.iloc[0]['model']
            best_result = next(r for r in evaluation_results if r['model_name'] == best_model_name)
            
            report.append(f"Best Model Analysis: {best_model_name}")
            report.append("-" * 40)
            
            # Basic metrics
            basic_metrics = best_result.get('basic_metrics', {})
            report.append("Performance Metrics:")
            for metric, value in basic_metrics.items():
                report.append(f"  {metric}: {value:.4f}")
            report.append("")
            
            # Football-specific metrics
            football_metrics = best_result.get('football_metrics', {})
            if football_metrics:
                report.append("Football-Specific Metrics:")
                report.append(f"  Home Win Accuracy: {football_metrics.get('home_win_accuracy', 0):.4f}")
                report.append(f"  Draw Accuracy: {football_metrics.get('draw_accuracy', 0):.4f}")
                report.append(f"  Away Win Accuracy: {football_metrics.get('away_win_accuracy', 0):.4f}")
                
                betting_sim = football_metrics.get('betting_simulation', {})
                if betting_sim:
                    report.append(f"  Betting ROI: {betting_sim.get('roi', 0):.4f}")
                    report.append(f"  Betting Win Rate: {betting_sim.get('win_rate', 0):.4f}")
                report.append("")
            
            # Feature importance
            feature_importance = best_result.get('feature_importance', {})
            if feature_importance:
                report.append("Top 10 Most Important Features:")
                for i, (feature, importance) in enumerate(list(feature_importance.items())[:10], 1):
                    report.append(f"  {i:2d}. {feature}: {importance:.4f}")
                report.append("")
        
        # Summary
        report.append("Evaluation Summary:")
        report.append("-" * 20)
        report.append(f"  Models evaluated: {len(evaluation_results)}")
        report.append(f"  Evaluation date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(report)

    def backtest_model(self, model: BaseFootballModel,
                      historical_data: pd.DataFrame,
                      start_date: str, end_date: str,
                      retrain_frequency: str = 'monthly') -> Dict[str, Any]:
        """Backtest model performance on historical data.

        Args:
            model: Model to backtest
            historical_data: Historical match data with features and targets
            start_date: Start date for backtesting (YYYY-MM-DD)
            end_date: End date for backtesting (YYYY-MM-DD)
            retrain_frequency: How often to retrain ('weekly', 'monthly', 'quarterly')

        Returns:
            Backtesting results
        """
        self.logger.info(f"Starting backtest for {model.model_name}")

        # Convert dates
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)

        # Filter data by date range
        if 'match_date' in historical_data.columns:
            historical_data['match_date'] = pd.to_datetime(historical_data['match_date'])
            backtest_data = historical_data[
                (historical_data['match_date'] >= start_date) &
                (historical_data['match_date'] <= end_date)
            ].copy()
        else:
            backtest_data = historical_data.copy()

        if len(backtest_data) == 0:
            return {'error': 'No data available for backtesting period'}

        # Determine retrain intervals
        retrain_intervals = self._get_retrain_intervals(start_date, end_date, retrain_frequency)

        backtest_results = {
            'model_name': model.model_name,
            'backtest_period': f"{start_date.date()} to {end_date.date()}",
            'retrain_frequency': retrain_frequency,
            'intervals': [],
            'overall_metrics': {},
            'predictions': []
        }

        all_predictions = []
        all_actuals = []

        for i, (train_start, train_end, test_start, test_end) in enumerate(retrain_intervals):
            self.logger.info(f"Backtesting interval {i+1}/{len(retrain_intervals)}: {test_start.date()} to {test_end.date()}")

            # Get training data
            train_data = backtest_data[
                (backtest_data['match_date'] >= train_start) &
                (backtest_data['match_date'] < train_end)
            ]

            # Get test data
            test_data = backtest_data[
                (backtest_data['match_date'] >= test_start) &
                (backtest_data['match_date'] < test_end)
            ]

            if len(train_data) < 50 or len(test_data) == 0:
                continue

            try:
                # Prepare data
                target_col = 'result'  # Adjust based on your target column
                X_train = train_data.drop(columns=[target_col, 'match_date'])
                y_train = train_data[target_col]
                X_test = test_data.drop(columns=[target_col, 'match_date'])
                y_test = test_data[target_col]

                # Train model
                temp_model = model.__class__(model_name=f"{model.model_name}_backtest")
                temp_model.fit(X_train, y_train)

                # Make predictions
                predictions = temp_model.predict(X_test)
                probabilities = temp_model.predict_proba(X_test) if temp_model.model_type == 'classification' else None

                # Evaluate
                metrics = temp_model.evaluate(X_test, y_test)

                # Store results
                interval_result = {
                    'train_period': f"{train_start.date()} to {train_end.date()}",
                    'test_period': f"{test_start.date()} to {test_end.date()}",
                    'train_samples': len(train_data),
                    'test_samples': len(test_data),
                    'metrics': metrics
                }
                backtest_results['intervals'].append(interval_result)

                # Collect predictions for overall analysis
                all_predictions.extend(predictions)
                all_actuals.extend(y_test.values if hasattr(y_test, 'values') else y_test)

                # Store individual predictions
                for j, (pred, actual) in enumerate(zip(predictions, y_test)):
                    backtest_results['predictions'].append({
                        'date': test_data.iloc[j]['match_date'].isoformat(),
                        'predicted': int(pred),
                        'actual': int(actual),
                        'correct': pred == actual
                    })

            except Exception as e:
                self.logger.error(f"Error in backtest interval {i+1}: {e}")
                continue

        # Calculate overall metrics
        if all_predictions and all_actuals:
            overall_accuracy = accuracy_score(all_actuals, all_predictions)
            backtest_results['overall_metrics'] = {
                'accuracy': float(overall_accuracy),
                'total_predictions': len(all_predictions),
                'correct_predictions': int(np.sum(np.array(all_predictions) == np.array(all_actuals)))
            }

        self.logger.info(f"Backtest completed for {model.model_name}")
        return backtest_results

    def _get_retrain_intervals(self, start_date: pd.Timestamp, end_date: pd.Timestamp,
                              frequency: str) -> List[Tuple[pd.Timestamp, pd.Timestamp, pd.Timestamp, pd.Timestamp]]:
        """Get retrain intervals for backtesting.

        Args:
            start_date: Start date
            end_date: End date
            frequency: Retrain frequency

        Returns:
            List of (train_start, train_end, test_start, test_end) tuples
        """
        intervals = []

        # Determine interval length
        if frequency == 'weekly':
            interval_days = 7
        elif frequency == 'monthly':
            interval_days = 30
        elif frequency == 'quarterly':
            interval_days = 90
        else:
            interval_days = 30  # Default to monthly

        # Initial training period (6 months)
        initial_train_days = 180

        current_date = start_date + timedelta(days=initial_train_days)

        while current_date < end_date:
            # Training period: from start to current_date
            train_start = start_date
            train_end = current_date

            # Test period: next interval
            test_start = current_date
            test_end = min(current_date + timedelta(days=interval_days), end_date)

            intervals.append((train_start, train_end, test_start, test_end))

            # Move to next interval
            current_date = test_end

        return intervals
