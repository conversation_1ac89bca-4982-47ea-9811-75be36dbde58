"""Model selection system for choosing the best football prediction model."""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime

from ..base.base_model import BaseFootballModel
from .model_evaluator import ModelEvaluator


class ModelSelector:
    """Intelligent model selection system."""
    
    def __init__(self):
        """Initialize model selector."""
        self.logger = logging.getLogger("model_selector")
        self.evaluator = ModelEvaluator()
        
        # Selection criteria weights
        self.selection_weights = {
            'accuracy': 0.25,
            'precision': 0.15,
            'recall': 0.15,
            'f1_score': 0.15,
            'roc_auc': 0.10,
            'temporal_stability': 0.10,
            'betting_roi': 0.10
        }
    
    def select_best_model(self, models: List[BaseFootballModel],
                         X_test: pd.DataFrame, y_test: np.ndarray,
                         X_train: Optional[pd.DataFrame] = None,
                         y_train: Optional[np.ndarray] = None,
                         selection_criteria: str = 'weighted_score') -> Dict[str, Any]:
        """Select the best model from a list of trained models.
        
        Args:
            models: List of trained models
            X_test: Test features
            y_test: Test targets
            X_train: Optional training features
            y_train: Optional training targets
            selection_criteria: Criteria for selection ('accuracy', 'f1_score', 'weighted_score', 'football_specific')
            
        Returns:
            Selection results with best model and analysis
        """
        self.logger.info(f"Selecting best model from {len(models)} candidates using {selection_criteria}")
        
        # Evaluate all models
        evaluation_results = []
        for model in models:
            if model.is_trained:
                try:
                    result = self.evaluator.evaluate_model(model, X_test, y_test, X_train, y_train)
                    evaluation_results.append(result)
                except Exception as e:
                    self.logger.error(f"Failed to evaluate {model.model_name}: {e}")
        
        if not evaluation_results:
            return {'error': 'No models could be evaluated'}
        
        # Select best model based on criteria
        best_model_info = self._select_by_criteria(evaluation_results, selection_criteria)
        
        # Find the actual model object
        best_model = next(
            (m for m in models if m.model_name == best_model_info['model_name']), 
            None
        )
        
        # Generate selection report
        selection_report = self._generate_selection_report(evaluation_results, best_model_info, selection_criteria)
        
        return {
            'best_model': best_model,
            'best_model_info': best_model_info,
            'all_evaluations': evaluation_results,
            'selection_criteria': selection_criteria,
            'selection_report': selection_report,
            'selection_date': datetime.now().isoformat()
        }
    
    def _select_by_criteria(self, evaluation_results: List[Dict[str, Any]], 
                           criteria: str) -> Dict[str, Any]:
        """Select best model based on specific criteria.
        
        Args:
            evaluation_results: List of evaluation results
            criteria: Selection criteria
            
        Returns:
            Best model information
        """
        if criteria == 'accuracy':
            return max(evaluation_results, 
                      key=lambda x: x.get('basic_metrics', {}).get('accuracy', 0))
        
        elif criteria == 'f1_score':
            return max(evaluation_results, 
                      key=lambda x: x.get('basic_metrics', {}).get('f1_score', 0))
        
        elif criteria == 'football_specific':
            return self._select_football_specific(evaluation_results)
        
        elif criteria == 'weighted_score':
            return self._select_weighted_score(evaluation_results)
        
        else:
            # Default to accuracy
            return max(evaluation_results, 
                      key=lambda x: x.get('basic_metrics', {}).get('accuracy', 0))
    
    def _select_football_specific(self, evaluation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Select model based on football-specific criteria.
        
        Args:
            evaluation_results: List of evaluation results
            
        Returns:
            Best model for football prediction
        """
        def football_score(result):
            basic_metrics = result.get('basic_metrics', {})
            football_metrics = result.get('football_metrics', {})
            
            # Weight different aspects of football prediction
            accuracy = basic_metrics.get('accuracy', 0) * 0.3
            home_accuracy = football_metrics.get('home_win_accuracy', 0) * 0.2
            away_accuracy = football_metrics.get('away_win_accuracy', 0) * 0.2
            draw_accuracy = football_metrics.get('draw_accuracy', 0) * 0.15
            betting_roi = max(0, football_metrics.get('betting_simulation', {}).get('roi', 0)) * 0.15
            
            return accuracy + home_accuracy + away_accuracy + draw_accuracy + betting_roi
        
        return max(evaluation_results, key=football_score)
    
    def _select_weighted_score(self, evaluation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Select model based on weighted score of multiple metrics.
        
        Args:
            evaluation_results: List of evaluation results
            
        Returns:
            Best model based on weighted score
        """
        def weighted_score(result):
            basic_metrics = result.get('basic_metrics', {})
            temporal_analysis = result.get('temporal_analysis', {})
            football_metrics = result.get('football_metrics', {})
            
            score = 0
            for metric, weight in self.selection_weights.items():
                if metric in basic_metrics:
                    score += basic_metrics[metric] * weight
                elif metric == 'temporal_stability':
                    score += temporal_analysis.get('temporal_stability', 0) * weight
                elif metric == 'betting_roi':
                    roi = football_metrics.get('betting_simulation', {}).get('roi', 0)
                    # Normalize ROI to 0-1 scale (assuming max ROI of 1.0)
                    normalized_roi = max(0, min(1, roi + 0.5))  # Shift and cap
                    score += normalized_roi * weight
            
            return score
        
        return max(evaluation_results, key=weighted_score)
    
    def _generate_selection_report(self, evaluation_results: List[Dict[str, Any]],
                                 best_model_info: Dict[str, Any],
                                 criteria: str) -> str:
        """Generate model selection report.
        
        Args:
            evaluation_results: All evaluation results
            best_model_info: Best model information
            criteria: Selection criteria used
            
        Returns:
            Formatted selection report
        """
        report = ["Model Selection Report", "=" * 40, ""]
        
        # Selection summary
        report.append(f"Selection Criteria: {criteria}")
        report.append(f"Models Evaluated: {len(evaluation_results)}")
        report.append(f"Selected Model: {best_model_info['model_name']}")
        report.append("")
        
        # Best model performance
        basic_metrics = best_model_info.get('basic_metrics', {})
        report.append("Selected Model Performance:")
        report.append("-" * 30)
        for metric, value in basic_metrics.items():
            report.append(f"  {metric}: {value:.4f}")
        report.append("")
        
        # Football-specific metrics
        football_metrics = best_model_info.get('football_metrics', {})
        if football_metrics:
            report.append("Football-Specific Performance:")
            report.append("-" * 30)
            report.append(f"  Home Win Accuracy: {football_metrics.get('home_win_accuracy', 0):.4f}")
            report.append(f"  Draw Accuracy: {football_metrics.get('draw_accuracy', 0):.4f}")
            report.append(f"  Away Win Accuracy: {football_metrics.get('away_win_accuracy', 0):.4f}")
            
            betting_sim = football_metrics.get('betting_simulation', {})
            if betting_sim:
                report.append(f"  Betting ROI: {betting_sim.get('roi', 0):.4f}")
                report.append(f"  Betting Win Rate: {betting_sim.get('win_rate', 0):.4f}")
            report.append("")
        
        # Model comparison table
        comparison_df = self.evaluator.compare_models(evaluation_results)
        if not comparison_df.empty:
            report.append("Model Comparison:")
            report.append("-" * 30)
            report.append(comparison_df.to_string(index=False, float_format='%.4f'))
            report.append("")
        
        # Selection reasoning
        report.append("Selection Reasoning:")
        report.append("-" * 20)
        if criteria == 'weighted_score':
            report.append("Selected based on weighted combination of multiple metrics:")
            for metric, weight in self.selection_weights.items():
                report.append(f"  {metric}: {weight:.2f}")
        elif criteria == 'football_specific':
            report.append("Selected based on football-specific performance criteria")
        else:
            report.append(f"Selected based on highest {criteria}")
        
        return "\n".join(report)
    
    def cross_validate_selection(self, models: List[BaseFootballModel],
                               X: pd.DataFrame, y: np.ndarray,
                               cv_folds: int = 5) -> Dict[str, Any]:
        """Cross-validate model selection to ensure robustness.
        
        Args:
            models: List of models to evaluate
            X: Features
            y: Targets
            cv_folds: Number of cross-validation folds
            
        Returns:
            Cross-validation results for model selection
        """
        from sklearn.model_selection import TimeSeriesSplit
        
        self.logger.info(f"Cross-validating model selection with {cv_folds} folds")
        
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        cv_results = {model.model_name: [] for model in models}
        fold_winners = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            self.logger.info(f"Processing fold {fold + 1}/{cv_folds}")
            
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            fold_models = []
            
            # Train each model on this fold
            for model in models:
                try:
                    # Create a copy of the model for this fold
                    fold_model = model.__class__(model_name=f"{model.model_name}_fold_{fold}")
                    fold_model.fit(X_train_fold, y_train_fold)
                    fold_models.append(fold_model)
                    
                    # Evaluate
                    metrics = fold_model.evaluate(X_val_fold, y_val_fold)
                    cv_results[model.model_name].append(metrics)
                    
                except Exception as e:
                    self.logger.error(f"Error training {model.model_name} on fold {fold}: {e}")
                    cv_results[model.model_name].append({'accuracy': 0.0})
            
            # Select best model for this fold
            if fold_models:
                fold_selection = self.select_best_model(
                    fold_models, X_val_fold, y_val_fold,
                    selection_criteria='accuracy'
                )
                fold_winners.append(fold_selection['best_model_info']['model_name'])
        
        # Analyze cross-validation results
        cv_summary = {}
        for model_name, results in cv_results.items():
            accuracies = [r.get('accuracy', 0) for r in results]
            cv_summary[model_name] = {
                'mean_accuracy': np.mean(accuracies),
                'std_accuracy': np.std(accuracies),
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'fold_wins': fold_winners.count(model_name)
            }
        
        # Overall winner
        overall_winner = max(cv_summary.keys(), 
                           key=lambda x: cv_summary[x]['mean_accuracy'])
        
        return {
            'cv_summary': cv_summary,
            'overall_winner': overall_winner,
            'fold_winners': fold_winners,
            'cv_folds': cv_folds
        }
    
    def update_selection_weights(self, new_weights: Dict[str, float]) -> None:
        """Update selection criteria weights.
        
        Args:
            new_weights: New weights for selection criteria
        """
        # Validate weights sum to 1.0
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            self.logger.warning(f"Weights sum to {total_weight}, normalizing to 1.0")
            new_weights = {k: v / total_weight for k, v in new_weights.items()}
        
        self.selection_weights.update(new_weights)
        self.logger.info(f"Updated selection weights: {self.selection_weights}")
    
    def get_model_rankings(self, evaluation_results: List[Dict[str, Any]]) -> pd.DataFrame:
        """Get model rankings across different criteria.
        
        Args:
            evaluation_results: List of evaluation results
            
        Returns:
            DataFrame with model rankings
        """
        rankings_data = []
        
        criteria_list = ['accuracy', 'f1_score', 'weighted_score', 'football_specific']
        
        for result in evaluation_results:
            model_name = result['model_name']
            row = {'model': model_name}
            
            for criteria in criteria_list:
                # Get rank for this criteria
                best_for_criteria = self._select_by_criteria(evaluation_results, criteria)
                rank = 1 if best_for_criteria['model_name'] == model_name else 0
                
                # Calculate score for ranking
                if criteria == 'accuracy':
                    score = result.get('basic_metrics', {}).get('accuracy', 0)
                elif criteria == 'f1_score':
                    score = result.get('basic_metrics', {}).get('f1_score', 0)
                elif criteria == 'weighted_score':
                    score = self._calculate_weighted_score(result)
                elif criteria == 'football_specific':
                    score = self._calculate_football_score(result)
                
                row[f'{criteria}_score'] = score
                row[f'{criteria}_rank'] = rank
            
            rankings_data.append(row)
        
        rankings_df = pd.DataFrame(rankings_data)
        
        # Calculate average rank
        rank_columns = [col for col in rankings_df.columns if col.endswith('_rank')]
        rankings_df['average_rank'] = rankings_df[rank_columns].mean(axis=1)
        
        return rankings_df.sort_values('average_rank', ascending=False)
    
    def _calculate_weighted_score(self, result: Dict[str, Any]) -> float:
        """Calculate weighted score for a model result."""
        basic_metrics = result.get('basic_metrics', {})
        temporal_analysis = result.get('temporal_analysis', {})
        football_metrics = result.get('football_metrics', {})
        
        score = 0
        for metric, weight in self.selection_weights.items():
            if metric in basic_metrics:
                score += basic_metrics[metric] * weight
            elif metric == 'temporal_stability':
                score += temporal_analysis.get('temporal_stability', 0) * weight
            elif metric == 'betting_roi':
                roi = football_metrics.get('betting_simulation', {}).get('roi', 0)
                normalized_roi = max(0, min(1, roi + 0.5))
                score += normalized_roi * weight
        
        return score
    
    def _calculate_football_score(self, result: Dict[str, Any]) -> float:
        """Calculate football-specific score for a model result."""
        basic_metrics = result.get('basic_metrics', {})
        football_metrics = result.get('football_metrics', {})
        
        accuracy = basic_metrics.get('accuracy', 0) * 0.3
        home_accuracy = football_metrics.get('home_win_accuracy', 0) * 0.2
        away_accuracy = football_metrics.get('away_win_accuracy', 0) * 0.2
        draw_accuracy = football_metrics.get('draw_accuracy', 0) * 0.15
        betting_roi = max(0, football_metrics.get('betting_simulation', {}).get('roi', 0)) * 0.15
        
        return accuracy + home_accuracy + away_accuracy + draw_accuracy + betting_roi
