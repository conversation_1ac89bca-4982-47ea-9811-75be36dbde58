"""Comprehensive prediction pipeline for football matches."""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import joblib
from pathlib import Path

from src.features import FeatureEngineeringPipeline
from src.models.evaluation import ModelSelector
from src.data.api_football_orchestrator import APIFootballOrchestrator
from src.utils.database import DatabaseManager
from src.utils.config import config


class FootballPredictionPipeline:
    """Comprehensive prediction pipeline for football matches."""
    
    def __init__(self, model_path: Optional[str] = None):
        """Initialize prediction pipeline.
        
        Args:
            model_path: Path to trained model (if None, loads best available model)
        """
        self.logger = logging.getLogger("prediction_pipeline")
        
        # Initialize components
        self.feature_pipeline = FeatureEngineeringPipeline()
        self.data_orchestrator = APIFootballOrchestrator()
        self.db_manager = DatabaseManager()
        self.model_selector = ModelSelector()
        
        # Model and metadata
        self.model = None
        self.model_metadata = {}
        self.feature_names = []
        
        # Load model
        if model_path:
            self.load_model(model_path)
        else:
            self.load_best_model()
        
        self.logger.info("Prediction pipeline initialized")
    
    def load_model(self, model_path: str) -> None:
        """Load a specific trained model.
        
        Args:
            model_path: Path to model file
        """
        try:
            model_data = joblib.load(model_path)
            
            if isinstance(model_data, dict):
                self.model = model_data.get('model')
                self.model_metadata = model_data.get('metadata', {})
                self.feature_names = model_data.get('feature_names', [])
            else:
                self.model = model_data
                self.model_metadata = {}
                self.feature_names = []
            
            self.logger.info(f"Loaded model from {model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load model from {model_path}: {e}")
            raise
    
    def load_best_model(self) -> None:
        """Load the best available model from the models directory."""
        models_dir = Path("data/models")
        
        if not models_dir.exists():
            raise FileNotFoundError("No models directory found. Train models first.")
        
        model_files = list(models_dir.glob("*.joblib"))
        
        if not model_files:
            raise FileNotFoundError("No trained models found. Train models first.")
        
        # Load the most recent model (you could implement more sophisticated selection)
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        self.load_model(str(latest_model))
        
        self.logger.info(f"Loaded best available model: {latest_model.name}")
    
    def predict_match(self, home_team_id: int, away_team_id: int, 
                     match_date: Optional[datetime] = None,
                     league_id: Optional[int] = None) -> Dict[str, Any]:
        """Predict outcome for a specific match.
        
        Args:
            home_team_id: Home team ID
            away_team_id: Away team ID
            match_date: Match date (defaults to now + 1 day)
            league_id: League ID (optional)
            
        Returns:
            Comprehensive prediction results
        """
        if not self.model:
            raise ValueError("No model loaded. Load a model first.")
        
        if match_date is None:
            match_date = datetime.now() + timedelta(days=1)
        
        self.logger.info(f"Predicting match: Team {home_team_id} vs Team {away_team_id}")
        
        try:
            # Create match data structure
            match_data = {
                'home_team_id': home_team_id,
                'away_team_id': away_team_id,
                'match_date': match_date,
                'league_id': league_id
            }
            
            # Collect context data
            context_data = self._collect_context_data(home_team_id, away_team_id, match_date, league_id)
            
            # Extract features
            features = self.feature_pipeline.extract_features([match_data], context_data)
            
            if features.empty:
                raise ValueError("Failed to extract features for prediction")
            
            # Make prediction
            prediction_results = self._make_prediction(features, match_data, context_data)
            
            # Add metadata
            prediction_results.update({
                'prediction_timestamp': datetime.now().isoformat(),
                'model_info': {
                    'model_name': getattr(self.model, 'model_name', 'unknown'),
                    'model_type': getattr(self.model, 'model_type', 'unknown'),
                    'features_used': len(features.columns),
                    'feature_names': list(features.columns)
                },
                'match_info': match_data
            })
            
            self.logger.info(f"Prediction completed for match {home_team_id} vs {away_team_id}")
            return prediction_results
            
        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            raise
    
    def predict_upcoming_matches(self, days_ahead: int = 7, 
                               league_ids: Optional[List[int]] = None) -> List[Dict[str, Any]]:
        """Predict outcomes for upcoming matches.
        
        Args:
            days_ahead: Number of days ahead to predict
            league_ids: Specific league IDs (None for all configured leagues)
            
        Returns:
            List of prediction results
        """
        self.logger.info(f"Predicting upcoming matches for next {days_ahead} days")
        
        try:
            # Get upcoming matches
            upcoming_matches = self._get_upcoming_matches(days_ahead, league_ids)
            
            if not upcoming_matches:
                self.logger.warning("No upcoming matches found")
                return []
            
            predictions = []
            
            for match in upcoming_matches:
                try:
                    prediction = self.predict_match(
                        home_team_id=match['home_team_id'],
                        away_team_id=match['away_team_id'],
                        match_date=match['match_date'],
                        league_id=match.get('league_id')
                    )
                    
                    # Add match context
                    prediction['fixture_id'] = match.get('external_id')
                    prediction['league_name'] = match.get('league_name')
                    prediction['home_team_name'] = match.get('home_team_name')
                    prediction['away_team_name'] = match.get('away_team_name')
                    
                    predictions.append(prediction)
                    
                except Exception as e:
                    self.logger.error(f"Failed to predict match {match.get('external_id')}: {e}")
                    continue
            
            self.logger.info(f"Completed predictions for {len(predictions)} matches")
            return predictions
            
        except Exception as e:
            self.logger.error(f"Failed to predict upcoming matches: {e}")
            raise
    
    def _collect_context_data(self, home_team_id: int, away_team_id: int, 
                             match_date: datetime, league_id: Optional[int]) -> Dict[str, Any]:
        """Collect context data needed for feature extraction.
        
        Args:
            home_team_id: Home team ID
            away_team_id: Away team ID
            match_date: Match date
            league_id: League ID
            
        Returns:
            Context data dictionary
        """
        context_data = {}
        
        try:
            # Get recent matches for both teams
            context_data['home_team_matches'] = self._get_recent_matches(home_team_id, match_date)
            context_data['away_team_matches'] = self._get_recent_matches(away_team_id, match_date)
            
            # Get head-to-head matches
            context_data['h2h_matches'] = self._get_h2h_matches(home_team_id, away_team_id)
            
            # Get injury data
            context_data['home_team_injuries'] = self._get_team_injuries(home_team_id, match_date)
            context_data['away_team_injuries'] = self._get_team_injuries(away_team_id, match_date)
            
            # Get player data
            context_data['home_team_players'] = self._get_team_players(home_team_id)
            context_data['away_team_players'] = self._get_team_players(away_team_id)
            
            # Get team statistics
            if league_id:
                context_data['home_team_stats'] = self._get_team_stats(home_team_id, league_id)
                context_data['away_team_stats'] = self._get_team_stats(away_team_id, league_id)
                context_data['league_standings'] = self._get_league_standings(league_id)
            
        except Exception as e:
            self.logger.warning(f"Failed to collect some context data: {e}")
        
        return context_data
    
    def _make_prediction(self, features: pd.DataFrame, match_data: Dict[str, Any], 
                        context_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make prediction using the loaded model.
        
        Args:
            features: Feature matrix
            match_data: Match information
            context_data: Context data
            
        Returns:
            Prediction results
        """
        # Ensure features match model expectations
        if self.feature_names:
            missing_features = set(self.feature_names) - set(features.columns)
            if missing_features:
                # Add missing features with default values
                for feature in missing_features:
                    features[feature] = 0.0
            
            # Reorder features to match training order
            features = features[self.feature_names]
        
        # Make predictions
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(features)[0]
            predicted_class = self.model.predict(features)[0]
        else:
            # Handle models without probability prediction
            predicted_class = self.model.predict(features)[0]
            probabilities = [0.33, 0.33, 0.34]  # Default uniform probabilities
        
        # Map predictions to outcomes
        class_mapping = {0: 'Away Win', 1: 'Draw', 2: 'Home Win'}
        outcome_mapping = {0: 'A', 1: 'D', 2: 'H'}
        
        predicted_outcome = outcome_mapping.get(predicted_class, 'D')
        predicted_outcome_text = class_mapping.get(predicted_class, 'Draw')
        
        # Calculate confidence
        confidence = float(np.max(probabilities))
        
        # Get feature importance if available
        feature_importance = {}
        if hasattr(self.model, 'feature_importances_'):
            importance_scores = self.model.feature_importances_
            feature_importance = dict(zip(features.columns, importance_scores))
            # Sort by importance
            feature_importance = dict(sorted(feature_importance.items(), 
                                           key=lambda x: x[1], reverse=True)[:10])
        
        return {
            'predicted_outcome': predicted_outcome,
            'predicted_outcome_text': predicted_outcome_text,
            'probabilities': {
                'home_win': float(probabilities[2]),
                'draw': float(probabilities[1]),
                'away_win': float(probabilities[0])
            },
            'confidence': confidence,
            'confidence_level': self._get_confidence_level(confidence),
            'feature_importance': feature_importance,
            'prediction_factors': self._analyze_prediction_factors(features, context_data)
        }
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Convert confidence score to descriptive level.
        
        Args:
            confidence: Confidence score (0-1)
            
        Returns:
            Confidence level description
        """
        if confidence >= 0.8:
            return "Very High"
        elif confidence >= 0.65:
            return "High"
        elif confidence >= 0.5:
            return "Medium"
        elif confidence >= 0.4:
            return "Low"
        else:
            return "Very Low"
    
    def _analyze_prediction_factors(self, features: pd.DataFrame, 
                                  context_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze key factors influencing the prediction.
        
        Args:
            features: Feature matrix
            context_data: Context data
            
        Returns:
            Analysis of prediction factors
        """
        factors = {}
        
        try:
            # Form analysis
            home_form = features.get('home_recent_form', [0.5]).iloc[0] if not features.empty else 0.5
            away_form = features.get('away_recent_form', [0.5]).iloc[0] if not features.empty else 0.5
            
            factors['form_advantage'] = {
                'home_form': float(home_form),
                'away_form': float(away_form),
                'advantage': 'Home' if home_form > away_form else 'Away' if away_form > home_form else 'Equal'
            }
            
            # Injury impact
            home_injuries = features.get('home_injury_impact', [0]).iloc[0] if not features.empty else 0
            away_injuries = features.get('away_injury_impact', [0]).iloc[0] if not features.empty else 0
            
            factors['injury_impact'] = {
                'home_impact': float(home_injuries),
                'away_impact': float(away_injuries),
                'advantage': 'Home' if away_injuries > home_injuries else 'Away' if home_injuries > away_injuries else 'Equal'
            }
            
            # Head-to-head
            h2h_home_rate = features.get('h2h_home_win_rate', [0.33]).iloc[0] if not features.empty else 0.33
            h2h_away_rate = features.get('h2h_away_win_rate', [0.33]).iloc[0] if not features.empty else 0.33
            
            factors['head_to_head'] = {
                'home_win_rate': float(h2h_home_rate),
                'away_win_rate': float(h2h_away_rate),
                'advantage': 'Home' if h2h_home_rate > h2h_away_rate else 'Away' if h2h_away_rate > h2h_home_rate else 'Equal'
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to analyze prediction factors: {e}")
        
        return factors
    
    def _get_upcoming_matches(self, days_ahead: int,
                             league_ids: Optional[List[int]]) -> List[Dict[str, Any]]:
        """Get upcoming matches from database or API.

        Args:
            days_ahead: Days ahead to look
            league_ids: League IDs to filter

        Returns:
            List of upcoming matches
        """
        try:
            # First try to get from database
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match, Team, League
                from sqlalchemy import and_

                query = session.query(Match).join(Team, Match.home_team_id == Team.id)
                query = query.join(League, Match.league_id == League.id)

                # Filter by date range
                start_date = datetime.now()
                end_date = start_date + timedelta(days=days_ahead)
                query = query.filter(and_(
                    Match.match_date >= start_date,
                    Match.match_date <= end_date,
                    Match.status.in_(['NS', 'TBD'])  # Not started, To be determined
                ))

                # Filter by league IDs if provided
                if league_ids:
                    query = query.filter(Match.league_id.in_(league_ids))

                matches = query.all()

                return [
                    {
                        'external_id': match.external_id,
                        'home_team_id': match.home_team_id,
                        'away_team_id': match.away_team_id,
                        'match_date': match.match_date,
                        'league_id': match.league_id,
                        'home_team_name': match.home_team.name if match.home_team else 'Unknown',
                        'away_team_name': match.away_team.name if match.away_team else 'Unknown',
                        'league_name': match.league.name if match.league else 'Unknown'
                    }
                    for match in matches
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get upcoming matches from database: {e}")
            # Fallback to API collection
            try:
                results = self.data_orchestrator.collect_upcoming_matches(days_ahead)
                return results.get('matches', [])
            except Exception as api_error:
                self.logger.error(f"Failed to get upcoming matches from API: {api_error}")
                return []

    def _get_recent_matches(self, team_id: int, before_date: datetime,
                          limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent matches for a team."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match
                from sqlalchemy import or_, and_

                query = session.query(Match).filter(
                    and_(
                        or_(Match.home_team_id == team_id, Match.away_team_id == team_id),
                        Match.match_date < before_date,
                        Match.status == 'FT'  # Finished matches only
                    )
                ).order_by(Match.match_date.desc()).limit(limit)

                matches = query.all()

                return [
                    {
                        'external_id': match.external_id,
                        'home_team_id': match.home_team_id,
                        'away_team_id': match.away_team_id,
                        'home_score': match.home_score,
                        'away_score': match.away_score,
                        'match_date': match.match_date,
                        'status': match.status
                    }
                    for match in matches
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get recent matches: {e}")
            return []

    def _get_h2h_matches(self, team1_id: int, team2_id: int,
                        limit: int = 10) -> List[Dict[str, Any]]:
        """Get head-to-head matches between two teams."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match
                from sqlalchemy import and_, or_

                query = session.query(Match).filter(
                    and_(
                        or_(
                            and_(Match.home_team_id == team1_id, Match.away_team_id == team2_id),
                            and_(Match.home_team_id == team2_id, Match.away_team_id == team1_id)
                        ),
                        Match.status == 'FT'
                    )
                ).order_by(Match.match_date.desc()).limit(limit)

                matches = query.all()

                return [
                    {
                        'external_id': match.external_id,
                        'home_team_id': match.home_team_id,
                        'away_team_id': match.away_team_id,
                        'home_score': match.home_score,
                        'away_score': match.away_score,
                        'match_date': match.match_date
                    }
                    for match in matches
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get H2H matches: {e}")
            return []

    def _get_team_injuries(self, team_id: int, match_date: datetime) -> List[Dict[str, Any]]:
        """Get current injuries for a team."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import EnhancedInjury
                from sqlalchemy import and_

                query = session.query(EnhancedInjury).filter(
                    and_(
                        EnhancedInjury.team_id == team_id,
                        EnhancedInjury.status == 'Active',
                        EnhancedInjury.injury_date <= match_date
                    )
                )

                injuries = query.all()

                return [
                    {
                        'player_id': injury.player_id,
                        'injury_type': injury.injury_type,
                        'severity': injury.severity,
                        'expected_return_date': injury.expected_return_date
                    }
                    for injury in injuries
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get team injuries: {e}")
            return []

    def _get_team_players(self, team_id: int) -> List[Dict[str, Any]]:
        """Get team players."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Player

                players = session.query(Player).filter(Player.team_id == team_id).all()

                return [
                    {
                        'external_id': player.external_id,
                        'name': player.name,
                        'position': player.position,
                        'rating': player.rating or 5.0,
                        'injured': player.injured
                    }
                    for player in players
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get team players: {e}")
            return []

    def _get_team_stats(self, team_id: int, league_id: int) -> Dict[str, Any]:
        """Get team statistics."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import TeamStatistics
                from sqlalchemy import and_

                stats = session.query(TeamStatistics).filter(
                    and_(
                        TeamStatistics.team_id == team_id,
                        TeamStatistics.league_id == league_id
                    )
                ).first()

                if stats:
                    return {
                        'matches_played': stats.matches_played,
                        'wins': stats.wins,
                        'draws': stats.draws,
                        'losses': stats.losses,
                        'goals_for': stats.goals_for,
                        'goals_against': stats.goals_against,
                        'points': stats.points
                    }

        except Exception as e:
            self.logger.warning(f"Failed to get team stats: {e}")

        return {}

    def _get_league_standings(self, league_id: int) -> List[Dict[str, Any]]:
        """Get league standings."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import TeamStatistics

                standings = session.query(TeamStatistics).filter(
                    TeamStatistics.league_id == league_id
                ).order_by(TeamStatistics.points.desc()).all()

                return [
                    {
                        'team_id': stat.team_id,
                        'position': idx + 1,
                        'points': stat.points,
                        'matches_played': stat.matches_played
                    }
                    for idx, stat in enumerate(standings)
                ]

        except Exception as e:
            self.logger.warning(f"Failed to get league standings: {e}")
            return []
