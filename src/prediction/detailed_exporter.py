"""Detailed prediction exporter with comprehensive match data and statistics."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from pathlib import Path

from .pipeline import FootballPredictionPipeline
from src.utils.database import DatabaseManager
from src.data.api_football_orchestrator import APIFootballOrchestrator


class DetailedPredictionExporter:
    """Export detailed predictions with comprehensive match statistics to CSV."""
    
    def __init__(self):
        """Initialize detailed prediction exporter."""
        self.logger = logging.getLogger("detailed_exporter")
        self.pipeline = FootballPredictionPipeline()
        self.db_manager = DatabaseManager()
        self.data_orchestrator = APIFootballOrchestrator()
        
        self.logger.info("Detailed prediction exporter initialized")
    
    def export_detailed_predictions(self, start_date: datetime, end_date: datetime,
                                  output_file: str, include_upcoming: bool = True) -> Dict[str, Any]:
        """Export detailed predictions with comprehensive match data.
        
        Args:
            start_date: Start date for export
            end_date: End date for export
            output_file: Output CSV file path
            include_upcoming: Include upcoming matches with predictions
            
        Returns:
            Export summary
        """
        self.logger.info(f"Exporting detailed predictions from {start_date.date()} to {end_date.date()}")
        
        try:
            # Get matches for the period
            matches_data = self._get_matches_for_period(start_date, end_date, include_upcoming)
            
            if not matches_data:
                return {'error': 'No matches found for the specified period'}
            
            # Process each match with detailed data
            detailed_records = []
            
            for match in matches_data:
                try:
                    detailed_record = self._create_detailed_record(match)
                    if detailed_record:
                        detailed_records.append(detailed_record)
                except Exception as e:
                    self.logger.error(f"Failed to process match {match.get('id', 'unknown')}: {e}")
                    continue
            
            if not detailed_records:
                return {'error': 'No detailed records could be created'}
            
            # Create DataFrame and export to CSV
            df = pd.DataFrame(detailed_records)
            
            # Ensure output directory exists
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Export to CSV
            df.to_csv(output_path, index=False)
            
            # Generate summary
            summary = {
                'export_file': str(output_path),
                'total_matches': len(detailed_records),
                'finished_matches': len([r for r in detailed_records if r['match_status'] == 'FT']),
                'upcoming_matches': len([r for r in detailed_records if r['match_status'] in ['NS', 'TBD']]),
                'columns_exported': len(df.columns),
                'export_date': datetime.now().isoformat()
            }
            
            self.logger.info(f"Exported {len(detailed_records)} detailed records to {output_file}")
            return summary
            
        except Exception as e:
            self.logger.error(f"Export failed: {e}")
            raise
    
    def _get_matches_for_period(self, start_date: datetime, end_date: datetime,
                               include_upcoming: bool) -> List[Dict[str, Any]]:
        """Get matches for the specified period."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match, Team, League
                from sqlalchemy import and_
                
                query = session.query(Match, Team, Team, League).join(
                    Team, Match.home_team_id == Team.id, aliased=True
                ).join(
                    Team, Match.away_team_id == Team.id, aliased=True
                ).join(
                    League, Match.league_id == League.id
                )
                
                # Filter by date range
                query = query.filter(and_(
                    Match.match_date >= start_date,
                    Match.match_date <= end_date
                ))
                
                # Filter by status if not including upcoming
                if not include_upcoming:
                    query = query.filter(Match.status == 'FT')
                
                results = query.all()
                
                matches_data = []
                for match, home_team, away_team, league in results:
                    matches_data.append({
                        'id': match.id,
                        'external_id': match.external_id,
                        'home_team_id': match.home_team_id,
                        'away_team_id': match.away_team_id,
                        'home_team_name': home_team.name,
                        'away_team_name': away_team.name,
                        'league_id': match.league_id,
                        'league_name': league.name,
                        'match_date': match.match_date,
                        'status': match.status,
                        'home_score': match.home_score,
                        'away_score': match.away_score,
                        'home_score_ht': match.home_score_ht,
                        'away_score_ht': match.away_score_ht,
                        'home_score_ft': match.home_score_ft,
                        'away_score_ft': match.away_score_ft,
                        'venue': match.venue,
                        'referee': match.referee
                    })
                
                return matches_data
                
        except Exception as e:
            self.logger.error(f"Failed to get matches: {e}")
            return []
    
    def _create_detailed_record(self, match: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create detailed record for a single match."""
        try:
            # Basic match information
            record = {
                # Match Identification
                'match_id': match['id'],
                'external_id': match['external_id'],
                'match_date': match['match_date'].isoformat() if match['match_date'] else None,
                'league_name': match['league_name'],
                'home_team': match['home_team_name'],
                'away_team': match['away_team_name'],
                'venue': match.get('venue', ''),
                'referee': match.get('referee', ''),
                'match_status': match['status'],
                
                # Actual Results
                'actual_home_score': match.get('home_score'),
                'actual_away_score': match.get('away_score'),
                'actual_home_score_ht': match.get('home_score_ht'),
                'actual_away_score_ht': match.get('away_score_ht'),
                'actual_home_score_ft': match.get('home_score_ft'),
                'actual_away_score_ft': match.get('away_score_ft'),
                'actual_result': self._determine_actual_result(match),
                'actual_result_ht': self._determine_actual_result_ht(match),
                
                # Goals and Scoring
                'total_goals': (match.get('home_score', 0) or 0) + (match.get('away_score', 0) or 0),
                'goal_difference': (match.get('home_score', 0) or 0) - (match.get('away_score', 0) or 0),
                'both_teams_scored': 1 if (match.get('home_score', 0) or 0) > 0 and (match.get('away_score', 0) or 0) > 0 else 0,
                'clean_sheet_home': 1 if (match.get('away_score', 0) or 0) == 0 else 0,
                'clean_sheet_away': 1 if (match.get('home_score', 0) or 0) == 0 else 0,
            }
            
            # Get prediction if available
            prediction_data = self._get_or_create_prediction(match)
            if prediction_data:
                record.update({
                    # Predictions
                    'predicted_result': prediction_data.get('predicted_outcome', ''),
                    'predicted_result_text': prediction_data.get('predicted_outcome_text', ''),
                    'home_win_probability': prediction_data.get('probabilities', {}).get('home_win', 0),
                    'draw_probability': prediction_data.get('probabilities', {}).get('draw', 0),
                    'away_win_probability': prediction_data.get('probabilities', {}).get('away_win', 0),
                    'prediction_confidence': prediction_data.get('confidence', 0),
                    'confidence_level': prediction_data.get('confidence_level', ''),
                    'prediction_correct': 1 if prediction_data.get('predicted_outcome') == record['actual_result'] else 0,
                })
                
                # Add prediction factors
                factors = prediction_data.get('prediction_factors', {})
                if factors:
                    form_factor = factors.get('form_advantage', {})
                    injury_factor = factors.get('injury_impact', {})
                    h2h_factor = factors.get('head_to_head', {})
                    
                    record.update({
                        'home_form_score': form_factor.get('home_form', 0),
                        'away_form_score': form_factor.get('away_form', 0),
                        'form_advantage': form_factor.get('advantage', 'Equal'),
                        'home_injury_impact': injury_factor.get('home_impact', 0),
                        'away_injury_impact': injury_factor.get('away_impact', 0),
                        'injury_advantage': injury_factor.get('advantage', 'Equal'),
                        'h2h_home_win_rate': h2h_factor.get('home_win_rate', 0),
                        'h2h_away_win_rate': h2h_factor.get('away_win_rate', 0),
                        'h2h_advantage': h2h_factor.get('advantage', 'Equal'),
                    })
            
            # Get detailed match statistics
            match_stats = self._get_match_statistics(match['external_id'])
            if match_stats:
                record.update(match_stats)
            
            # Get match events (goals, cards, etc.)
            match_events = self._get_match_events(match['external_id'])
            if match_events:
                record.update(match_events)
            
            # Get team statistics
            team_stats = self._get_team_statistics(match)
            if team_stats:
                record.update(team_stats)
            
            return record
            
        except Exception as e:
            self.logger.error(f"Failed to create detailed record for match {match.get('id')}: {e}")
            return None
    
    def _determine_actual_result(self, match: Dict[str, Any]) -> str:
        """Determine actual match result."""
        home_score = match.get('home_score') or match.get('home_score_ft', 0) or 0
        away_score = match.get('away_score') or match.get('away_score_ft', 0) or 0
        
        if home_score > away_score:
            return 'H'
        elif away_score > home_score:
            return 'A'
        else:
            return 'D'
    
    def _determine_actual_result_ht(self, match: Dict[str, Any]) -> str:
        """Determine actual half-time result."""
        home_score_ht = match.get('home_score_ht', 0) or 0
        away_score_ht = match.get('away_score_ht', 0) or 0
        
        if home_score_ht > away_score_ht:
            return 'H'
        elif away_score_ht > home_score_ht:
            return 'A'
        else:
            return 'D'
    
    def _get_or_create_prediction(self, match: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get existing prediction or create new one."""
        try:
            # First try to get existing prediction
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Prediction
                
                prediction = session.query(Prediction).filter(
                    Prediction.match_id == match['id']
                ).first()
                
                if prediction:
                    return {
                        'predicted_outcome': self._map_probabilities_to_outcome(
                            prediction.home_win_probability,
                            prediction.draw_probability,
                            prediction.away_win_probability
                        ),
                        'predicted_outcome_text': self._outcome_to_text(
                            self._map_probabilities_to_outcome(
                                prediction.home_win_probability,
                                prediction.draw_probability,
                                prediction.away_win_probability
                            )
                        ),
                        'probabilities': {
                            'home_win': prediction.home_win_probability,
                            'draw': prediction.draw_probability,
                            'away_win': prediction.away_win_probability
                        },
                        'confidence': prediction.confidence_score,
                        'confidence_level': self._get_confidence_level(prediction.confidence_score)
                    }
            
            # If no existing prediction and match is upcoming, create one
            if match['status'] in ['NS', 'TBD']:
                try:
                    prediction = self.pipeline.predict_match(
                        home_team_id=match['home_team_id'],
                        away_team_id=match['away_team_id'],
                        match_date=match['match_date'],
                        league_id=match['league_id']
                    )
                    return prediction
                except Exception as e:
                    self.logger.warning(f"Failed to create prediction for match {match['id']}: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get/create prediction for match {match['id']}: {e}")
            return None
    
    def _get_match_statistics(self, external_id: int) -> Dict[str, Any]:
        """Get detailed match statistics."""
        try:
            # Try to get from database first
            with self.db_manager.get_session() as session:
                from src.utils.database.models import MatchStatistics
                
                stats = session.query(MatchStatistics).filter(
                    MatchStatistics.match_id == external_id
                ).first()
                
                if stats:
                    return {
                        'home_possession': getattr(stats, 'home_possession', 0),
                        'away_possession': getattr(stats, 'away_possession', 0),
                        'home_shots': getattr(stats, 'home_shots', 0),
                        'away_shots': getattr(stats, 'away_shots', 0),
                        'home_shots_on_target': getattr(stats, 'home_shots_on_target', 0),
                        'away_shots_on_target': getattr(stats, 'away_shots_on_target', 0),
                        'home_corners': getattr(stats, 'home_corners', 0),
                        'away_corners': getattr(stats, 'away_corners', 0),
                        'home_fouls': getattr(stats, 'home_fouls', 0),
                        'away_fouls': getattr(stats, 'away_fouls', 0),
                        'home_offsides': getattr(stats, 'home_offsides', 0),
                        'away_offsides': getattr(stats, 'away_offsides', 0),
                    }
            
            return {}
            
        except Exception as e:
            self.logger.warning(f"Failed to get match statistics for {external_id}: {e}")
            return {}
    
    def _get_match_events(self, external_id: int) -> Dict[str, Any]:
        """Get match events (goals, cards, substitutions)."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import MatchEvent
                
                events = session.query(MatchEvent).filter(
                    MatchEvent.match_id == external_id
                ).all()
                
                # Count different types of events
                home_yellow_cards = 0
                away_yellow_cards = 0
                home_red_cards = 0
                away_red_cards = 0
                home_goals = 0
                away_goals = 0
                home_substitutions = 0
                away_substitutions = 0
                
                for event in events:
                    if event.event_detail == 'Yellow Card':
                        if event.team_id == event.match.home_team_id:
                            home_yellow_cards += 1
                        else:
                            away_yellow_cards += 1
                    elif event.event_detail == 'Red Card':
                        if event.team_id == event.match.home_team_id:
                            home_red_cards += 1
                        else:
                            away_red_cards += 1
                    elif event.event_type == 'Goal':
                        if event.team_id == event.match.home_team_id:
                            home_goals += 1
                        else:
                            away_goals += 1
                    elif event.event_type == 'subst':
                        if event.team_id == event.match.home_team_id:
                            home_substitutions += 1
                        else:
                            away_substitutions += 1
                
                return {
                    'home_yellow_cards': home_yellow_cards,
                    'away_yellow_cards': away_yellow_cards,
                    'home_red_cards': home_red_cards,
                    'away_red_cards': away_red_cards,
                    'total_cards': home_yellow_cards + away_yellow_cards + home_red_cards + away_red_cards,
                    'home_goals_events': home_goals,
                    'away_goals_events': away_goals,
                    'home_substitutions': home_substitutions,
                    'away_substitutions': away_substitutions,
                    'total_substitutions': home_substitutions + away_substitutions,
                }
            
        except Exception as e:
            self.logger.warning(f"Failed to get match events for {external_id}: {e}")
            return {
                'home_yellow_cards': 0,
                'away_yellow_cards': 0,
                'home_red_cards': 0,
                'away_red_cards': 0,
                'total_cards': 0,
                'home_goals_events': 0,
                'away_goals_events': 0,
                'home_substitutions': 0,
                'away_substitutions': 0,
                'total_substitutions': 0,
            }
    
    def _get_team_statistics(self, match: Dict[str, Any]) -> Dict[str, Any]:
        """Get team statistics for the season."""
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import TeamStatistics
                
                home_stats = session.query(TeamStatistics).filter(
                    TeamStatistics.team_id == match['home_team_id'],
                    TeamStatistics.league_id == match['league_id']
                ).first()
                
                away_stats = session.query(TeamStatistics).filter(
                    TeamStatistics.team_id == match['away_team_id'],
                    TeamStatistics.league_id == match['league_id']
                ).first()
                
                stats = {}
                
                if home_stats:
                    stats.update({
                        'home_matches_played': home_stats.matches_played,
                        'home_wins': home_stats.wins,
                        'home_draws': home_stats.draws,
                        'home_losses': home_stats.losses,
                        'home_goals_for': home_stats.goals_for,
                        'home_goals_against': home_stats.goals_against,
                        'home_points': home_stats.points,
                        'home_win_rate': home_stats.wins / max(home_stats.matches_played, 1),
                    })
                
                if away_stats:
                    stats.update({
                        'away_matches_played': away_stats.matches_played,
                        'away_wins': away_stats.wins,
                        'away_draws': away_stats.draws,
                        'away_losses': away_stats.losses,
                        'away_goals_for': away_stats.goals_for,
                        'away_goals_against': away_stats.goals_against,
                        'away_points': away_stats.points,
                        'away_win_rate': away_stats.wins / max(away_stats.matches_played, 1),
                    })
                
                return stats
            
        except Exception as e:
            self.logger.warning(f"Failed to get team statistics: {e}")
            return {}
    
    def _map_probabilities_to_outcome(self, home_prob: float, draw_prob: float, away_prob: float) -> str:
        """Map probabilities to outcome string."""
        probs = [away_prob, draw_prob, home_prob]
        max_idx = probs.index(max(probs))
        return ['A', 'D', 'H'][max_idx]
    
    def _outcome_to_text(self, outcome: str) -> str:
        """Convert outcome code to text."""
        mapping = {'H': 'Home Win', 'D': 'Draw', 'A': 'Away Win'}
        return mapping.get(outcome, 'Unknown')
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Convert confidence score to level."""
        if confidence >= 0.8:
            return "Very High"
        elif confidence >= 0.65:
            return "High"
        elif confidence >= 0.5:
            return "Medium"
        elif confidence >= 0.4:
            return "Low"
        else:
            return "Very Low"
