"""Prediction service for managing and scheduling predictions."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import json
from pathlib import Path

from .pipeline import FootballPredictionPipeline
from src.utils.database import DatabaseManager
from src.utils.config import config


class PredictionService:
    """Service for managing football predictions."""
    
    def __init__(self):
        """Initialize prediction service."""
        self.logger = logging.getLogger("prediction_service")
        self.pipeline = FootballPredictionPipeline()
        self.db_manager = DatabaseManager()
        
        # Service configuration
        self.batch_size = 50
        self.max_concurrent_predictions = 10
        self.prediction_cache_ttl = 3600  # 1 hour
        
        self.logger.info("Prediction service initialized")
    
    async def predict_match_async(self, home_team_id: int, away_team_id: int,
                                 match_date: Optional[datetime] = None,
                                 league_id: Optional[int] = None) -> Dict[str, Any]:
        """Asynchronous match prediction.
        
        Args:
            home_team_id: Home team ID
            away_team_id: Away team ID
            match_date: Match date
            league_id: League ID
            
        Returns:
            Prediction results
        """
        loop = asyncio.get_event_loop()
        
        # Run prediction in thread pool to avoid blocking
        prediction = await loop.run_in_executor(
            None,
            self.pipeline.predict_match,
            home_team_id,
            away_team_id,
            match_date,
            league_id
        )
        
        return prediction
    
    async def predict_batch_async(self, matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Asynchronous batch prediction.
        
        Args:
            matches: List of match data dictionaries
            
        Returns:
            List of prediction results
        """
        predictions = []
        
        # Process matches in batches to avoid overwhelming the system
        for i in range(0, len(matches), self.batch_size):
            batch = matches[i:i + self.batch_size]
            
            # Create tasks for concurrent processing
            tasks = []
            for match in batch:
                task = self.predict_match_async(
                    home_team_id=match['home_team_id'],
                    away_team_id=match['away_team_id'],
                    match_date=match.get('match_date'),
                    league_id=match.get('league_id')
                )
                tasks.append(task)
            
            # Limit concurrent predictions
            semaphore = asyncio.Semaphore(self.max_concurrent_predictions)
            
            async def predict_with_semaphore(task):
                async with semaphore:
                    return await task
            
            # Execute batch with concurrency limit
            batch_results = await asyncio.gather(
                *[predict_with_semaphore(task) for task in tasks],
                return_exceptions=True
            )
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    self.logger.error(f"Batch prediction error: {result}")
                else:
                    predictions.append(result)
        
        return predictions
    
    def save_prediction(self, prediction: Dict[str, Any], match_id: Optional[int] = None) -> bool:
        """Save prediction to database.
        
        Args:
            prediction: Prediction results
            match_id: Match ID (optional)
            
        Returns:
            Success status
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Prediction
                
                # Create prediction record
                pred_record = Prediction(
                    match_id=match_id,
                    model_name=prediction.get('model_info', {}).get('model_name', 'unknown'),
                    model_version='1.0',
                    home_win_prob=prediction['probabilities']['home_win'],
                    draw_prob=prediction['probabilities']['draw'],
                    away_win_prob=prediction['probabilities']['away_win'],
                    predicted_home_score=None,  # Add if available
                    predicted_away_score=None,  # Add if available
                    confidence_score=prediction['confidence'],
                    feature_importance=prediction.get('feature_importance', {}),
                    prediction_date=datetime.now()
                )
                
                session.add(pred_record)
                session.commit()
                
                self.logger.info(f"Saved prediction for match {match_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to save prediction: {e}")
            return False
    
    def get_cached_prediction(self, home_team_id: int, away_team_id: int,
                             match_date: datetime) -> Optional[Dict[str, Any]]:
        """Get cached prediction if available and not expired.
        
        Args:
            home_team_id: Home team ID
            away_team_id: Away team ID
            match_date: Match date
            
        Returns:
            Cached prediction or None
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Prediction, Match
                from sqlalchemy import and_
                
                # Find matching prediction
                query = session.query(Prediction).join(Match).filter(
                    and_(
                        Match.home_team_id == home_team_id,
                        Match.away_team_id == away_team_id,
                        Match.match_date == match_date,
                        Prediction.prediction_date > datetime.now() - timedelta(seconds=self.prediction_cache_ttl)
                    )
                ).order_by(Prediction.prediction_date.desc()).first()
                
                if query:
                    return {
                        'predicted_outcome': self._map_probabilities_to_outcome(
                            query.home_win_prob, query.draw_prob, query.away_win_prob
                        ),
                        'probabilities': {
                            'home_win': query.home_win_prob,
                            'draw': query.draw_prob,
                            'away_win': query.away_win_prob
                        },
                        'confidence': query.confidence_score,
                        'cached': True,
                        'prediction_timestamp': query.prediction_date.isoformat()
                    }
                    
        except Exception as e:
            self.logger.warning(f"Failed to get cached prediction: {e}")
        
        return None
    
    def _map_probabilities_to_outcome(self, home_prob: float, draw_prob: float, away_prob: float) -> str:
        """Map probabilities to outcome string."""
        probs = [away_prob, draw_prob, home_prob]
        max_idx = probs.index(max(probs))
        return ['A', 'D', 'H'][max_idx]
    
    async def predict_daily_matches(self, target_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Predict all matches for a specific day.
        
        Args:
            target_date: Target date (defaults to tomorrow)
            
        Returns:
            Prediction results summary
        """
        if target_date is None:
            target_date = datetime.now() + timedelta(days=1)
        
        self.logger.info(f"Predicting matches for {target_date.date()}")
        
        try:
            # Get matches for the target date
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match
                from sqlalchemy import and_
                
                matches = session.query(Match).filter(
                    and_(
                        Match.match_date >= target_date.replace(hour=0, minute=0, second=0),
                        Match.match_date < target_date.replace(hour=23, minute=59, second=59),
                        Match.status.in_(['NS', 'TBD'])
                    )
                ).all()
                
                if not matches:
                    return {'message': 'No matches found for the target date', 'predictions': []}
                
                # Convert to prediction format
                match_data = [
                    {
                        'home_team_id': match.home_team_id,
                        'away_team_id': match.away_team_id,
                        'match_date': match.match_date,
                        'league_id': match.league_id,
                        'match_id': match.id
                    }
                    for match in matches
                ]
                
                # Make predictions
                predictions = await self.predict_batch_async(match_data)
                
                # Save predictions
                saved_count = 0
                for i, prediction in enumerate(predictions):
                    if self.save_prediction(prediction, match_data[i]['match_id']):
                        saved_count += 1
                
                return {
                    'target_date': target_date.date().isoformat(),
                    'matches_found': len(matches),
                    'predictions_made': len(predictions),
                    'predictions_saved': saved_count,
                    'predictions': predictions
                }
                
        except Exception as e:
            self.logger.error(f"Failed to predict daily matches: {e}")
            raise
    
    def generate_prediction_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate prediction performance report.
        
        Args:
            start_date: Report start date
            end_date: Report end date
            
        Returns:
            Prediction report
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Prediction, Match
                from sqlalchemy import and_
                
                # Get predictions with actual results
                query = session.query(Prediction, Match).join(Match).filter(
                    and_(
                        Prediction.prediction_date >= start_date,
                        Prediction.prediction_date <= end_date,
                        Match.status == 'FT'  # Finished matches only
                    )
                )
                
                results = query.all()
                
                if not results:
                    return {'message': 'No completed predictions found for the period'}
                
                # Calculate accuracy metrics
                correct_predictions = 0
                total_predictions = len(results)
                confidence_sum = 0
                
                for prediction, match in results:
                    # Determine actual outcome
                    if match.home_score > match.away_score:
                        actual_outcome = 'H'
                    elif match.home_score < match.away_score:
                        actual_outcome = 'A'
                    else:
                        actual_outcome = 'D'
                    
                    # Determine predicted outcome
                    predicted_outcome = self._map_probabilities_to_outcome(
                        prediction.home_win_prob,
                        prediction.draw_prob,
                        prediction.away_win_prob
                    )
                    
                    if predicted_outcome == actual_outcome:
                        correct_predictions += 1
                    
                    confidence_sum += prediction.confidence_score
                
                accuracy = correct_predictions / total_predictions
                avg_confidence = confidence_sum / total_predictions
                
                return {
                    'period': f"{start_date.date()} to {end_date.date()}",
                    'total_predictions': total_predictions,
                    'correct_predictions': correct_predictions,
                    'accuracy': accuracy,
                    'average_confidence': avg_confidence,
                    'performance_grade': self._get_performance_grade(accuracy)
                }
                
        except Exception as e:
            self.logger.error(f"Failed to generate prediction report: {e}")
            raise
    
    def _get_performance_grade(self, accuracy: float) -> str:
        """Get performance grade based on accuracy."""
        if accuracy >= 0.6:
            return "Excellent"
        elif accuracy >= 0.5:
            return "Good"
        elif accuracy >= 0.4:
            return "Fair"
        else:
            return "Poor"
    
    def export_predictions(self, start_date: datetime, end_date: datetime,
                          output_file: str) -> bool:
        """Export predictions to file.
        
        Args:
            start_date: Export start date
            end_date: Export end date
            output_file: Output file path
            
        Returns:
            Success status
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Prediction, Match, Team
                from sqlalchemy import and_
                
                # Get predictions with match details
                query = session.query(Prediction, Match, Team, Team).join(Match)
                query = query.join(Team, Match.home_team_id == Team.id)
                query = query.join(Team, Match.away_team_id == Team.id)
                query = query.filter(
                    and_(
                        Prediction.prediction_date >= start_date,
                        Prediction.prediction_date <= end_date
                    )
                )
                
                results = query.all()
                
                # Prepare export data
                export_data = []
                for prediction, match, home_team, away_team in results:
                    export_data.append({
                        'prediction_date': prediction.prediction_date.isoformat(),
                        'match_date': match.match_date.isoformat(),
                        'home_team': home_team.name,
                        'away_team': away_team.name,
                        'predicted_outcome': self._map_probabilities_to_outcome(
                            prediction.home_win_prob,
                            prediction.draw_prob,
                            prediction.away_win_prob
                        ),
                        'home_win_probability': prediction.home_win_prob,
                        'draw_probability': prediction.draw_prob,
                        'away_win_probability': prediction.away_win_prob,
                        'confidence': prediction.confidence_score,
                        'actual_home_score': match.home_score,
                        'actual_away_score': match.away_score,
                        'match_status': match.status
                    })
                
                # Write to file
                output_path = Path(output_file)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
                
                self.logger.info(f"Exported {len(export_data)} predictions to {output_file}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to export predictions: {e}")
            return False
