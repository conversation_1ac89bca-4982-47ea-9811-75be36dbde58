"""FastAPI application for football prediction service."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from src.prediction.pipeline import FootballPredictionPipeline
from src.prediction.detailed_exporter import DetailedPredictionExporter
from src.data.api_football_orchestrator import APIFootballOrchestrator
from src.utils.config import config


# Global pipeline instance
prediction_pipeline: Optional[FootballPredictionPipeline] = None
data_orchestrator: Optional[APIFootballOrchestrator] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global prediction_pipeline, data_orchestrator
    
    # Startup
    logging.info("Starting Football Prediction API...")
    
    try:
        # Initialize prediction pipeline
        prediction_pipeline = FootballPredictionPipeline()
        data_orchestrator = APIFootballOrchestrator()
        
        logging.info("Football Prediction API started successfully")
        
    except Exception as e:
        logging.error(f"Failed to start API: {e}")
        raise
    
    yield
    
    # Shutdown
    logging.info("Shutting down Football Prediction API...")


# Create FastAPI app
app = FastAPI(
    title="Football Prediction API",
    description="Comprehensive football match prediction API using machine learning",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models for request/response
class MatchPredictionRequest(BaseModel):
    """Request model for match prediction."""
    home_team_id: int = Field(..., description="Home team ID")
    away_team_id: int = Field(..., description="Away team ID")
    match_date: Optional[datetime] = Field(None, description="Match date (ISO format)")
    league_id: Optional[int] = Field(None, description="League ID")


class PredictionResponse(BaseModel):
    """Response model for predictions."""
    predicted_outcome: str = Field(..., description="Predicted outcome (H/D/A)")
    predicted_outcome_text: str = Field(..., description="Predicted outcome text")
    probabilities: Dict[str, float] = Field(..., description="Outcome probabilities")
    confidence: float = Field(..., description="Prediction confidence (0-1)")
    confidence_level: str = Field(..., description="Confidence level description")
    prediction_timestamp: str = Field(..., description="When prediction was made")
    model_info: Dict[str, Any] = Field(..., description="Model information")
    match_info: Dict[str, Any] = Field(..., description="Match information")
    feature_importance: Dict[str, float] = Field(..., description="Top feature importance")
    prediction_factors: Dict[str, Any] = Field(..., description="Key prediction factors")


class UpcomingMatchesRequest(BaseModel):
    """Request model for upcoming matches prediction."""
    days_ahead: int = Field(7, description="Days ahead to predict", ge=1, le=30)
    league_ids: Optional[List[int]] = Field(None, description="Specific league IDs")


class BatchPredictionRequest(BaseModel):
    """Request model for batch predictions."""
    matches: List[MatchPredictionRequest] = Field(..., description="List of matches to predict")


class DetailedExportRequest(BaseModel):
    """Request model for detailed CSV export."""
    start_date: str = Field(..., description="Start date (YYYY-MM-DD)")
    end_date: str = Field(..., description="End date (YYYY-MM-DD)")
    include_upcoming: bool = Field(True, description="Include upcoming matches")
    output_filename: Optional[str] = Field(None, description="Custom output filename")


class APIStatusResponse(BaseModel):
    """API status response."""
    status: str
    version: str
    model_loaded: bool
    api_football_status: Dict[str, Any]
    timestamp: str


# Dependency to get prediction pipeline
def get_prediction_pipeline() -> FootballPredictionPipeline:
    """Get prediction pipeline instance."""
    if prediction_pipeline is None:
        raise HTTPException(status_code=503, detail="Prediction pipeline not initialized")
    return prediction_pipeline


def get_data_orchestrator() -> APIFootballOrchestrator:
    """Get data orchestrator instance."""
    if data_orchestrator is None:
        raise HTTPException(status_code=503, detail="Data orchestrator not initialized")
    return data_orchestrator


# API Routes
@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint."""
    return {
        "message": "Football Prediction API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "/status"
    }


@app.get("/status", response_model=APIStatusResponse)
async def get_status(
    pipeline: FootballPredictionPipeline = Depends(get_prediction_pipeline),
    orchestrator: APIFootballOrchestrator = Depends(get_data_orchestrator)
):
    """Get API status and health information."""
    try:
        # Get API-Football status
        api_football_status = orchestrator.get_api_status()
        
        return APIStatusResponse(
            status="healthy",
            version="1.0.0",
            model_loaded=pipeline.model is not None,
            api_football_status=api_football_status,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@app.post("/predict", response_model=PredictionResponse)
async def predict_match(
    request: MatchPredictionRequest,
    pipeline: FootballPredictionPipeline = Depends(get_prediction_pipeline)
):
    """Predict outcome for a specific match."""
    try:
        prediction = pipeline.predict_match(
            home_team_id=request.home_team_id,
            away_team_id=request.away_team_id,
            match_date=request.match_date,
            league_id=request.league_id
        )
        
        return PredictionResponse(**prediction)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Prediction failed: {str(e)}")


@app.post("/predict/upcoming", response_model=List[PredictionResponse])
async def predict_upcoming_matches(
    request: UpcomingMatchesRequest,
    pipeline: FootballPredictionPipeline = Depends(get_prediction_pipeline)
):
    """Predict outcomes for upcoming matches."""
    try:
        predictions = pipeline.predict_upcoming_matches(
            days_ahead=request.days_ahead,
            league_ids=request.league_ids
        )
        
        return [PredictionResponse(**pred) for pred in predictions]
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Upcoming predictions failed: {str(e)}")


@app.post("/predict/batch", response_model=List[PredictionResponse])
async def predict_batch(
    request: BatchPredictionRequest,
    pipeline: FootballPredictionPipeline = Depends(get_prediction_pipeline)
):
    """Predict outcomes for multiple matches."""
    try:
        predictions = []
        
        for match_request in request.matches:
            try:
                prediction = pipeline.predict_match(
                    home_team_id=match_request.home_team_id,
                    away_team_id=match_request.away_team_id,
                    match_date=match_request.match_date,
                    league_id=match_request.league_id
                )
                predictions.append(PredictionResponse(**prediction))
                
            except Exception as e:
                # Log error but continue with other predictions
                logging.error(f"Failed to predict match {match_request.home_team_id} vs {match_request.away_team_id}: {e}")
                continue
        
        return predictions
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Batch prediction failed: {str(e)}")


@app.get("/leagues", response_model=List[Dict[str, Any]])
async def get_leagues():
    """Get available leagues."""
    try:
        leagues_config = config.get_leagues()
        return [
            {
                "id": league_data["id"],
                "name": league_data["name"],
                "country": league_data["country"],
                "api_football_id": league_data.get("api_football_id")
            }
            for league_name, league_data in leagues_config.items()
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get leagues: {str(e)}")


@app.get("/model/info", response_model=Dict[str, Any])
async def get_model_info(
    pipeline: FootballPredictionPipeline = Depends(get_prediction_pipeline)
):
    """Get information about the loaded model."""
    try:
        if not pipeline.model:
            raise HTTPException(status_code=404, detail="No model loaded")
        
        return {
            "model_name": getattr(pipeline.model, 'model_name', 'unknown'),
            "model_type": getattr(pipeline.model, 'model_type', 'unknown'),
            "is_trained": getattr(pipeline.model, 'is_trained', False),
            "feature_count": len(pipeline.feature_names),
            "metadata": pipeline.model_metadata
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get model info: {str(e)}")


@app.post("/data/collect")
async def trigger_data_collection(
    background_tasks: BackgroundTasks,
    collection_type: str = "recent",
    days_back: int = 7,
    orchestrator: APIFootballOrchestrator = Depends(get_data_orchestrator)
):
    """Trigger background data collection."""
    try:
        def collect_data():
            """Background data collection task."""
            try:
                if collection_type == "recent":
                    orchestrator.collect_all_data(
                        include_historical=True,
                        days_back=days_back
                    )
                elif collection_type == "upcoming":
                    orchestrator.collect_upcoming_matches(days_ahead=days_back)
                elif collection_type == "injuries":
                    orchestrator.update_injury_status()
                
                logging.info(f"Background data collection ({collection_type}) completed")
                
            except Exception as e:
                logging.error(f"Background data collection failed: {e}")
        
        background_tasks.add_task(collect_data)
        
        return {
            "message": f"Data collection ({collection_type}) started in background",
            "collection_type": collection_type,
            "days_back": days_back
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to trigger data collection: {str(e)}")


@app.post("/export/detailed")
async def export_detailed_predictions(
    request: DetailedExportRequest,
    background_tasks: BackgroundTasks
):
    """Export detailed predictions to CSV."""
    try:
        from datetime import datetime

        # Parse dates
        start_date = datetime.strptime(request.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(request.end_date, '%Y-%m-%d')

        # Generate filename if not provided
        if not request.output_filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"detailed_predictions_{request.start_date}_to_{request.end_date}_{timestamp}.csv"
        else:
            filename = request.output_filename

        output_path = f"data/exports/{filename}"

        def export_task():
            """Background export task."""
            try:
                exporter = DetailedPredictionExporter()
                summary = exporter.export_detailed_predictions(
                    start_date=start_date,
                    end_date=end_date,
                    output_file=output_path,
                    include_upcoming=request.include_upcoming
                )
                logging.info(f"Detailed export completed: {summary}")
            except Exception as e:
                logging.error(f"Detailed export failed: {e}")

        background_tasks.add_task(export_task)

        return {
            "message": "Detailed export started in background",
            "output_file": output_path,
            "start_date": request.start_date,
            "end_date": request.end_date,
            "include_upcoming": request.include_upcoming
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Export request failed: {str(e)}")


@app.get("/health")
async def health_check():
    """Simple health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Handle 404 errors."""
    return JSONResponse(
        status_code=404,
        content={"detail": "Endpoint not found", "path": str(request.url)}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Handle 500 errors."""
    logging.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Development server
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Get API configuration
    try:
        api_config = config._config.get('api', {})
    except AttributeError:
        api_config = {}
    host = api_config.get('host', '0.0.0.0')
    port = api_config.get('port', 8000)
    debug = api_config.get('debug', False)
    
    uvicorn.run(
        "src.api.main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
