"""Head-to-head feature extractor."""

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime, timedelta

from .base_feature_extractor import BaseFeatureExtractor


class HeadToHeadExtractor(BaseFeatureExtractor):
    """Extracts head-to-head features between teams."""
    
    def __init__(self):
        """Initialize head-to-head extractor."""
        super().__init__("head_to_head")
        
        # Get configuration
        self.max_matches = self.feature_config.get('head_to_head', {}).get('max_matches', 10)
        self.years_lookback = self.feature_config.get('head_to_head', {}).get('years_lookback', 5)
    
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract head-to-head features.
        
        Args:
            match_data: Match data dictionary
            context_data: Context data with historical matches
            
        Returns:
            Dictionary of head-to-head features
        """
        features = {}
        
        if not context_data:
            return self._get_default_features()
        
        home_team_id = match_data.get('home_team_id')
        away_team_id = match_data.get('away_team_id')
        
        h2h_matches = context_data.get('h2h_matches', [])
        
        if not h2h_matches:
            return self._get_default_features()
        
        # Overall H2H statistics
        overall_h2h = self._calculate_overall_h2h(h2h_matches, home_team_id, away_team_id)
        features.update(overall_h2h)
        
        # Recent H2H statistics (last 5 matches)
        recent_h2h = self._calculate_recent_h2h(h2h_matches, home_team_id, away_team_id, 5)
        features.update({f'recent_{k}': v for k, v in recent_h2h.items()})
        
        # Home/Away specific H2H
        home_away_h2h = self._calculate_home_away_h2h(h2h_matches, home_team_id, away_team_id)
        features.update(home_away_h2h)
        
        # Goal patterns in H2H
        goal_patterns = self._calculate_h2h_goal_patterns(h2h_matches, home_team_id, away_team_id)
        features.update(goal_patterns)
        
        # H2H trends
        trends = self._calculate_h2h_trends(h2h_matches, home_team_id, away_team_id)
        features.update(trends)
        
        return features
    
    def _calculate_overall_h2h(self, h2h_matches: List[Dict[str, Any]], 
                             home_team_id: int, away_team_id: int) -> Dict[str, float]:
        """Calculate overall head-to-head statistics.
        
        Args:
            h2h_matches: List of head-to-head matches
            home_team_id: Home team ID
            away_team_id: Away team ID
            
        Returns:
            Dictionary of overall H2H features
        """
        if not h2h_matches:
            return self._get_default_h2h_stats()
        
        home_wins = home_draws = home_losses = 0
        home_goals_scored = home_goals_conceded = 0
        total_goals = 0
        
        for match in h2h_matches:
            home_score = match.get('home_score', 0) or 0
            away_score = match.get('away_score', 0) or 0
            total_goals += home_score + away_score
            
            if match['home_team_id'] == home_team_id:
                # Current home team was home in this H2H match
                home_goals_scored += home_score
                home_goals_conceded += away_score
                
                if home_score > away_score:
                    home_wins += 1
                elif home_score == away_score:
                    home_draws += 1
                else:
                    home_losses += 1
            else:
                # Current home team was away in this H2H match
                home_goals_scored += away_score
                home_goals_conceded += home_score
                
                if away_score > home_score:
                    home_wins += 1
                elif away_score == home_score:
                    home_draws += 1
                else:
                    home_losses += 1
        
        total_matches = len(h2h_matches)
        
        return {
            'h2h_matches_played': total_matches,
            'h2h_home_win_rate': home_wins / total_matches,
            'h2h_draw_rate': home_draws / total_matches,
            'h2h_away_win_rate': home_losses / total_matches,
            'h2h_home_goals_per_match': home_goals_scored / total_matches,
            'h2h_away_goals_per_match': home_goals_conceded / total_matches,
            'h2h_goal_difference': (home_goals_scored - home_goals_conceded) / total_matches,
            'h2h_total_goals_per_match': total_goals / total_matches,
            'h2h_over_2_5_rate': sum(1 for m in h2h_matches 
                                   if (m.get('home_score', 0) or 0) + (m.get('away_score', 0) or 0) > 2.5) / total_matches
        }
    
    def _calculate_recent_h2h(self, h2h_matches: List[Dict[str, Any]], 
                            home_team_id: int, away_team_id: int, 
                            num_matches: int) -> Dict[str, float]:
        """Calculate recent head-to-head statistics.
        
        Args:
            h2h_matches: List of head-to-head matches (sorted by date desc)
            home_team_id: Home team ID
            away_team_id: Away team ID
            num_matches: Number of recent matches to consider
            
        Returns:
            Dictionary of recent H2H features
        """
        recent_matches = h2h_matches[:num_matches]
        return self._calculate_overall_h2h(recent_matches, home_team_id, away_team_id)
    
    def _calculate_home_away_h2h(self, h2h_matches: List[Dict[str, Any]], 
                               home_team_id: int, away_team_id: int) -> Dict[str, float]:
        """Calculate home/away specific head-to-head statistics.
        
        Args:
            h2h_matches: List of head-to-head matches
            home_team_id: Home team ID
            away_team_id: Away team ID
            
        Returns:
            Dictionary of home/away H2H features
        """
        # Matches where current home team was actually home
        home_matches = [m for m in h2h_matches if m['home_team_id'] == home_team_id]
        # Matches where current home team was away
        away_matches = [m for m in h2h_matches if m['away_team_id'] == home_team_id]
        
        features = {}
        
        # Home team as home in H2H
        if home_matches:
            home_wins = sum(1 for m in home_matches 
                          if (m.get('home_score', 0) or 0) > (m.get('away_score', 0) or 0))
            features['h2h_home_as_home_win_rate'] = home_wins / len(home_matches)
            features['h2h_home_as_home_matches'] = len(home_matches)
        else:
            features['h2h_home_as_home_win_rate'] = 0.5
            features['h2h_home_as_home_matches'] = 0
        
        # Home team as away in H2H
        if away_matches:
            away_wins = sum(1 for m in away_matches 
                          if (m.get('away_score', 0) or 0) > (m.get('home_score', 0) or 0))
            features['h2h_home_as_away_win_rate'] = away_wins / len(away_matches)
            features['h2h_home_as_away_matches'] = len(away_matches)
        else:
            features['h2h_home_as_away_win_rate'] = 0.5
            features['h2h_home_as_away_matches'] = 0
        
        return features
    
    def _calculate_h2h_goal_patterns(self, h2h_matches: List[Dict[str, Any]], 
                                   home_team_id: int, away_team_id: int) -> Dict[str, float]:
        """Calculate goal patterns in head-to-head matches.
        
        Args:
            h2h_matches: List of head-to-head matches
            home_team_id: Home team ID
            away_team_id: Away team ID
            
        Returns:
            Dictionary of goal pattern features
        """
        if not h2h_matches:
            return {
                'h2h_both_teams_score_rate': 0.5,
                'h2h_clean_sheet_rate_home': 0.3,
                'h2h_clean_sheet_rate_away': 0.3,
                'h2h_high_scoring_rate': 0.3
            }
        
        both_score = 0
        home_clean_sheets = 0
        away_clean_sheets = 0
        high_scoring = 0  # 3+ goals
        
        for match in h2h_matches:
            home_score = match.get('home_score', 0) or 0
            away_score = match.get('away_score', 0) or 0
            total_goals = home_score + away_score
            
            # Both teams score
            if home_score > 0 and away_score > 0:
                both_score += 1
            
            # Clean sheets (from perspective of current home team)
            if match['home_team_id'] == home_team_id:
                if away_score == 0:
                    home_clean_sheets += 1
                if home_score == 0:
                    away_clean_sheets += 1
            else:
                if home_score == 0:
                    home_clean_sheets += 1
                if away_score == 0:
                    away_clean_sheets += 1
            
            # High scoring matches
            if total_goals >= 3:
                high_scoring += 1
        
        total_matches = len(h2h_matches)
        
        return {
            'h2h_both_teams_score_rate': both_score / total_matches,
            'h2h_clean_sheet_rate_home': home_clean_sheets / total_matches,
            'h2h_clean_sheet_rate_away': away_clean_sheets / total_matches,
            'h2h_high_scoring_rate': high_scoring / total_matches
        }
    
    def _calculate_h2h_trends(self, h2h_matches: List[Dict[str, Any]], 
                            home_team_id: int, away_team_id: int) -> Dict[str, float]:
        """Calculate trends in head-to-head matches.
        
        Args:
            h2h_matches: List of head-to-head matches (sorted by date desc)
            home_team_id: Home team ID
            away_team_id: Away team ID
            
        Returns:
            Dictionary of trend features
        """
        if len(h2h_matches) < 4:
            return {
                'h2h_home_trend': 0.0,
                'h2h_goals_trend': 0.0,
                'h2h_competitiveness_trend': 0.0
            }
        
        # Split matches into recent and older
        mid_point = len(h2h_matches) // 2
        recent_matches = h2h_matches[:mid_point]
        older_matches = h2h_matches[mid_point:mid_point*2]
        
        # Calculate win rates for each period
        recent_stats = self._calculate_overall_h2h(recent_matches, home_team_id, away_team_id)
        older_stats = self._calculate_overall_h2h(older_matches, home_team_id, away_team_id)
        
        # Calculate trends
        home_trend = recent_stats['h2h_home_win_rate'] - older_stats['h2h_home_win_rate']
        goals_trend = recent_stats['h2h_total_goals_per_match'] - older_stats['h2h_total_goals_per_match']
        
        # Competitiveness trend (how close the matches are)
        recent_competitiveness = recent_stats['h2h_draw_rate']
        older_competitiveness = older_stats['h2h_draw_rate']
        competitiveness_trend = recent_competitiveness - older_competitiveness
        
        return {
            'h2h_home_trend': home_trend,
            'h2h_goals_trend': goals_trend,
            'h2h_competitiveness_trend': competitiveness_trend
        }
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when no H2H data is available.
        
        Returns:
            Dictionary of default features
        """
        default_stats = self._get_default_h2h_stats()
        
        # Add recent prefix to default stats
        recent_stats = {f'recent_{k}': v for k, v in default_stats.items()}
        
        # Add home/away specific defaults
        home_away_defaults = {
            'h2h_home_as_home_win_rate': 0.5,
            'h2h_home_as_home_matches': 0,
            'h2h_home_as_away_win_rate': 0.5,
            'h2h_home_as_away_matches': 0
        }
        
        # Add goal pattern defaults
        goal_pattern_defaults = {
            'h2h_both_teams_score_rate': 0.5,
            'h2h_clean_sheet_rate_home': 0.3,
            'h2h_clean_sheet_rate_away': 0.3,
            'h2h_high_scoring_rate': 0.3
        }
        
        # Add trend defaults
        trend_defaults = {
            'h2h_home_trend': 0.0,
            'h2h_goals_trend': 0.0,
            'h2h_competitiveness_trend': 0.0
        }
        
        # Combine all defaults
        features = {}
        features.update(default_stats)
        features.update(recent_stats)
        features.update(home_away_defaults)
        features.update(goal_pattern_defaults)
        features.update(trend_defaults)
        
        return features
    
    def _get_default_h2h_stats(self) -> Dict[str, float]:
        """Get default H2H statistics.
        
        Returns:
            Dictionary of default H2H stats
        """
        return {
            'h2h_matches_played': 0,
            'h2h_home_win_rate': 0.45,  # Slight home advantage
            'h2h_draw_rate': 0.25,
            'h2h_away_win_rate': 0.30,
            'h2h_home_goals_per_match': 1.3,
            'h2h_away_goals_per_match': 1.1,
            'h2h_goal_difference': 0.2,
            'h2h_total_goals_per_match': 2.4,
            'h2h_over_2_5_rate': 0.4
        }
