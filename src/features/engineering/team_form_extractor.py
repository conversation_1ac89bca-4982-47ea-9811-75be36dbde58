"""Team form feature extractor."""

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime, timedelta

from .base_feature_extractor import BaseFeatureExtractor


class TeamFormExtractor(BaseFeatureExtractor):
    """Extracts team form-related features."""
    
    def __init__(self):
        """Initialize team form extractor."""
        super().__init__("team_form")
        
        # Get configuration
        self.recent_matches = self.feature_config.get('team_form', {}).get('recent_matches', 5)
        self.weighted_form = self.feature_config.get('team_form', {}).get('weighted_form', True)
    
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract team form features.
        
        Args:
            match_data: Match data dictionary
            context_data: Context data with historical matches
            
        Returns:
            Dictionary of form features
        """
        features = {}
        
        if not context_data:
            return self._get_default_features()
        
        home_team_id = match_data.get('home_team_id')
        away_team_id = match_data.get('away_team_id')
        
        home_matches = context_data.get('home_team_matches', [])
        away_matches = context_data.get('away_team_matches', [])
        
        # Extract home team form features
        home_features = self._extract_team_form_features(
            home_matches, home_team_id, "home"
        )
        features.update(home_features)
        
        # Extract away team form features
        away_features = self._extract_team_form_features(
            away_matches, away_team_id, "away"
        )
        features.update(away_features)
        
        # Calculate relative form features
        relative_features = self._calculate_relative_features(home_features, away_features)
        features.update(relative_features)
        
        return features
    
    def _extract_team_form_features(self, matches: List[Dict[str, Any]], 
                                  team_id: int, prefix: str) -> Dict[str, float]:
        """Extract form features for a specific team.
        
        Args:
            matches: List of team matches
            team_id: Team ID
            prefix: Feature name prefix ("home" or "away")
            
        Returns:
            Dictionary of team form features
        """
        features = {}
        
        if not matches:
            return self._get_default_team_features(prefix)
        
        # Overall form (all matches)
        form_score, form_string = self.calculate_team_form(matches, team_id, len(matches))
        features[f'{prefix}_overall_form'] = form_score
        
        # Recent form (last N matches)
        recent_form_score, recent_form_string = self.calculate_team_form(
            matches, team_id, self.recent_matches
        )
        features[f'{prefix}_recent_form'] = recent_form_score
        
        # Home/Away specific form
        home_matches = [m for m in matches if m['home_team_id'] == team_id]
        away_matches = [m for m in matches if m['away_team_id'] == team_id]
        
        if home_matches:
            home_form_score, _ = self.calculate_team_form(home_matches, team_id, len(home_matches))
            features[f'{prefix}_home_form'] = home_form_score
        else:
            features[f'{prefix}_home_form'] = 0.5
        
        if away_matches:
            away_form_score, _ = self.calculate_team_form(away_matches, team_id, len(away_matches))
            features[f'{prefix}_away_form'] = away_form_score
        else:
            features[f'{prefix}_away_form'] = 0.5
        
        # Win/Draw/Loss percentages
        results = self._calculate_result_percentages(matches, team_id)
        features[f'{prefix}_win_percentage'] = results['win_percentage']
        features[f'{prefix}_draw_percentage'] = results['draw_percentage']
        features[f'{prefix}_loss_percentage'] = results['loss_percentage']
        
        # Goal statistics
        goal_stats = self.calculate_goal_statistics(matches, team_id)
        features[f'{prefix}_goals_scored_avg'] = goal_stats['goals_scored_avg']
        features[f'{prefix}_goals_conceded_avg'] = goal_stats['goals_conceded_avg']
        features[f'{prefix}_goal_difference_avg'] = goal_stats['goal_difference_avg']
        
        # Weighted form (if enabled)
        if self.weighted_form:
            weighted_form = self._calculate_weighted_form(matches, team_id)
            features[f'{prefix}_weighted_form'] = weighted_form
        
        # Form trend (improving/declining)
        form_trend = self._calculate_form_trend(matches, team_id)
        features[f'{prefix}_form_trend'] = form_trend
        
        # Consistency metrics
        consistency = self._calculate_consistency(matches, team_id)
        features[f'{prefix}_consistency'] = consistency
        
        return features
    
    def _calculate_result_percentages(self, matches: List[Dict[str, Any]], 
                                    team_id: int) -> Dict[str, float]:
        """Calculate win/draw/loss percentages.
        
        Args:
            matches: List of matches
            team_id: Team ID
            
        Returns:
            Dictionary with result percentages
        """
        if not matches:
            return {'win_percentage': 0.0, 'draw_percentage': 0.0, 'loss_percentage': 0.0}
        
        wins = draws = losses = 0
        
        for match in matches:
            if match['home_team_id'] == team_id:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if home_score > away_score:
                    wins += 1
                elif home_score == away_score:
                    draws += 1
                else:
                    losses += 1
            else:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if away_score > home_score:
                    wins += 1
                elif away_score == home_score:
                    draws += 1
                else:
                    losses += 1
        
        total = len(matches)
        return {
            'win_percentage': wins / total,
            'draw_percentage': draws / total,
            'loss_percentage': losses / total
        }
    
    def _calculate_weighted_form(self, matches: List[Dict[str, Any]], 
                               team_id: int) -> float:
        """Calculate weighted form with time decay.
        
        Args:
            matches: List of matches (should be sorted by date desc)
            team_id: Team ID
            
        Returns:
            Weighted form score
        """
        if not matches:
            return 0.5
        
        weighted_points = 0.0
        total_weight = 0.0
        
        for i, match in enumerate(matches):
            # Calculate weight (more recent matches have higher weight)
            weight = np.exp(-i * 0.1)  # Exponential decay
            
            # Calculate points for this match
            points = 0
            if match['home_team_id'] == team_id:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if home_score > away_score:
                    points = 3
                elif home_score == away_score:
                    points = 1
            else:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if away_score > home_score:
                    points = 3
                elif away_score == home_score:
                    points = 1
            
            weighted_points += points * weight
            total_weight += weight
        
        # Normalize to 0-1 scale
        if total_weight > 0:
            return (weighted_points / total_weight) / 3.0
        else:
            return 0.5
    
    def _calculate_form_trend(self, matches: List[Dict[str, Any]], 
                            team_id: int) -> float:
        """Calculate form trend (improving/declining).
        
        Args:
            matches: List of matches (sorted by date desc)
            team_id: Team ID
            
        Returns:
            Form trend score (-1 to 1, where 1 is improving, -1 is declining)
        """
        if len(matches) < 4:
            return 0.0
        
        # Split matches into two halves
        mid_point = len(matches) // 2
        recent_matches = matches[:mid_point]
        older_matches = matches[mid_point:mid_point*2]
        
        # Calculate form for each half
        recent_form, _ = self.calculate_team_form(recent_matches, team_id, len(recent_matches))
        older_form, _ = self.calculate_team_form(older_matches, team_id, len(older_matches))
        
        # Return the difference (positive = improving, negative = declining)
        return recent_form - older_form
    
    def _calculate_consistency(self, matches: List[Dict[str, Any]], 
                             team_id: int) -> float:
        """Calculate team consistency based on result variance.
        
        Args:
            matches: List of matches
            team_id: Team ID
            
        Returns:
            Consistency score (0-1, where 1 is most consistent)
        """
        if len(matches) < 3:
            return 0.5
        
        points = []
        for match in matches:
            if match['home_team_id'] == team_id:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if home_score > away_score:
                    points.append(3)
                elif home_score == away_score:
                    points.append(1)
                else:
                    points.append(0)
            else:
                home_score = match['home_score'] or 0
                away_score = match['away_score'] or 0
                if away_score > home_score:
                    points.append(3)
                elif away_score == home_score:
                    points.append(1)
                else:
                    points.append(0)
        
        # Calculate coefficient of variation (lower = more consistent)
        if len(points) > 1 and np.mean(points) > 0:
            cv = np.std(points) / np.mean(points)
            # Convert to 0-1 scale (1 = most consistent)
            return max(0, 1 - cv)
        else:
            return 0.5
    
    def _calculate_relative_features(self, home_features: Dict[str, float], 
                                   away_features: Dict[str, float]) -> Dict[str, float]:
        """Calculate relative features between home and away teams.
        
        Args:
            home_features: Home team features
            away_features: Away team features
            
        Returns:
            Dictionary of relative features
        """
        relative_features = {}
        
        # Form differences
        relative_features['form_difference'] = (
            home_features.get('home_recent_form', 0.5) - 
            away_features.get('away_recent_form', 0.5)
        )
        
        # Goal difference comparison
        relative_features['goal_difference_comparison'] = (
            home_features.get('home_goal_difference_avg', 0.0) - 
            away_features.get('away_goal_difference_avg', 0.0)
        )
        
        # Win percentage difference
        relative_features['win_percentage_difference'] = (
            home_features.get('home_win_percentage', 0.0) - 
            away_features.get('away_win_percentage', 0.0)
        )
        
        return relative_features
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when no data is available.
        
        Returns:
            Dictionary of default features
        """
        features = {}
        features.update(self._get_default_team_features("home"))
        features.update(self._get_default_team_features("away"))
        
        # Default relative features
        features['form_difference'] = 0.0
        features['goal_difference_comparison'] = 0.0
        features['win_percentage_difference'] = 0.0
        
        return features
    
    def _get_default_team_features(self, prefix: str) -> Dict[str, float]:
        """Get default features for a team.
        
        Args:
            prefix: Feature prefix ("home" or "away")
            
        Returns:
            Dictionary of default team features
        """
        return {
            f'{prefix}_overall_form': 0.5,
            f'{prefix}_recent_form': 0.5,
            f'{prefix}_home_form': 0.5,
            f'{prefix}_away_form': 0.5,
            f'{prefix}_win_percentage': 0.33,
            f'{prefix}_draw_percentage': 0.33,
            f'{prefix}_loss_percentage': 0.33,
            f'{prefix}_goals_scored_avg': 1.0,
            f'{prefix}_goals_conceded_avg': 1.0,
            f'{prefix}_goal_difference_avg': 0.0,
            f'{prefix}_weighted_form': 0.5,
            f'{prefix}_form_trend': 0.0,
            f'{prefix}_consistency': 0.5
        }
