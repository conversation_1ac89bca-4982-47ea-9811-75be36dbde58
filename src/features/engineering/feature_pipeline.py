"""Main feature engineering pipeline."""

import logging
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from .team_form_extractor import TeamFormExtractor
from .home_away_extractor import HomeAwayAdvantageExtractor
from .head_to_head_extractor import HeadToHeadExtractor
from .injury_impact_extractor import InjuryImpactExtractor
from src.utils.config import config


class FeatureEngineeringPipeline:
    """Main pipeline for feature engineering."""
    
    def __init__(self):
        """Initialize the feature engineering pipeline."""
        self.logger = logging.getLogger("feature_engineering")
        
        # Initialize feature extractors
        self.extractors = {
            'team_form': TeamFormExtractor(),
            'home_away': HomeAwayAdvantageExtractor(),
            'head_to_head': HeadToHeadExtractor(),
            'injury_impact': InjuryImpactExtractor()
        }
        
        self.feature_config = config.get_feature_config()
    
    def extract_features(self, matches_data: List[Dict[str, Any]], 
                        parallel: bool = True, max_workers: int = 4) -> pd.DataFrame:
        """Extract features for multiple matches.
        
        Args:
            matches_data: List of match dictionaries
            parallel: Whether to use parallel processing
            max_workers: Maximum number of worker threads
            
        Returns:
            DataFrame with extracted features
        """
        self.logger.info(f"Starting feature extraction for {len(matches_data)} matches")
        
        if parallel and len(matches_data) > 10:
            return self._extract_features_parallel(matches_data, max_workers)
        else:
            return self._extract_features_sequential(matches_data)
    
    def _extract_features_sequential(self, matches_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """Extract features sequentially.
        
        Args:
            matches_data: List of match dictionaries
            
        Returns:
            DataFrame with extracted features
        """
        all_features = []
        
        for i, match_data in enumerate(matches_data):
            try:
                if i % 100 == 0:
                    self.logger.info(f"Processing match {i+1}/{len(matches_data)}")
                
                features = self._extract_match_features(match_data)
                all_features.append(features)
                
            except Exception as e:
                self.logger.error(f"Error extracting features for match {match_data.get('id', 'Unknown')}: {e}")
                # Add default features for failed matches
                all_features.append(self._get_default_match_features(match_data))
        
        return pd.DataFrame(all_features)
    
    def _extract_features_parallel(self, matches_data: List[Dict[str, Any]], 
                                 max_workers: int) -> pd.DataFrame:
        """Extract features in parallel.
        
        Args:
            matches_data: List of match dictionaries
            max_workers: Maximum number of worker threads
            
        Returns:
            DataFrame with extracted features
        """
        all_features = [None] * len(matches_data)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_index = {}
            for i, match_data in enumerate(matches_data):
                future = executor.submit(self._extract_match_features, match_data)
                future_to_index[future] = i
            
            # Collect results
            completed = 0
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    features = future.result()
                    all_features[index] = features
                except Exception as e:
                    match_data = matches_data[index]
                    self.logger.error(f"Error extracting features for match {match_data.get('id', 'Unknown')}: {e}")
                    all_features[index] = self._get_default_match_features(match_data)
                
                completed += 1
                if completed % 100 == 0:
                    self.logger.info(f"Completed {completed}/{len(matches_data)} matches")
        
        return pd.DataFrame(all_features)
    
    def _extract_match_features(self, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features for a single match.
        
        Args:
            match_data: Match data dictionary
            
        Returns:
            Dictionary of extracted features
        """
        # Get context data for this match
        context_data = self._get_context_data(match_data)
        
        # Extract features from each extractor
        all_features = {}
        
        for extractor_name, extractor in self.extractors.items():
            try:
                features = extractor.extract_features(match_data, context_data)
                all_features.update(features)
            except Exception as e:
                self.logger.error(f"Error in {extractor_name} extractor: {e}")
                # Continue with other extractors
        
        # Add basic match information
        all_features.update({
            'match_id': match_data.get('id', match_data.get('external_id')),
            'home_team_id': match_data.get('home_team_id'),
            'away_team_id': match_data.get('away_team_id'),
            'match_date': match_data.get('match_date'),
            'league_id': match_data.get('league_id'),
            'season': match_data.get('season')
        })
        
        # Add target variables if available (for training data)
        if match_data.get('status') == 'FINISHED':
            home_score = match_data.get('home_score', 0) or 0
            away_score = match_data.get('away_score', 0) or 0
            
            all_features.update({
                'home_score': home_score,
                'away_score': away_score,
                'total_goals': home_score + away_score,
                'goal_difference': home_score - away_score,
                'result': self._get_match_result(home_score, away_score),
                'over_2_5': 1 if (home_score + away_score) > 2.5 else 0,
                'both_teams_score': 1 if home_score > 0 and away_score > 0 else 0
            })
        
        return all_features
    
    def _get_context_data(self, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get context data for feature extraction.
        
        This method would typically query the database for historical data.
        For now, it returns empty context to avoid database dependencies.
        
        Args:
            match_data: Match data dictionary
            
        Returns:
            Context data dictionary
        """
        # In a real implementation, this would use the database manager
        # to fetch historical matches, team stats, player data, etc.
        # For now, return empty context
        return {
            'home_team_matches': [],
            'away_team_matches': [],
            'h2h_matches': [],
            'home_team_stats': {},
            'away_team_stats': {},
            'home_team_players': [],
            'away_team_players': [],
            'home_team_injuries': [],
            'away_team_injuries': []
        }
    
    def _get_match_result(self, home_score: int, away_score: int) -> str:
        """Get match result from scores.
        
        Args:
            home_score: Home team score
            away_score: Away team score
            
        Returns:
            Match result ('H', 'D', 'A')
        """
        if home_score > away_score:
            return 'H'  # Home win
        elif home_score == away_score:
            return 'D'  # Draw
        else:
            return 'A'  # Away win
    
    def _get_default_match_features(self, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get default features for a match when extraction fails.
        
        Args:
            match_data: Match data dictionary
            
        Returns:
            Dictionary of default features
        """
        # Get default features from each extractor
        default_features = {}
        
        for extractor_name, extractor in self.extractors.items():
            try:
                extractor_defaults = extractor.extract_features(match_data, None)
                default_features.update(extractor_defaults)
            except Exception as e:
                self.logger.error(f"Error getting defaults from {extractor_name}: {e}")
        
        # Add basic match information
        default_features.update({
            'match_id': match_data.get('id', match_data.get('external_id')),
            'home_team_id': match_data.get('home_team_id'),
            'away_team_id': match_data.get('away_team_id'),
            'match_date': match_data.get('match_date'),
            'league_id': match_data.get('league_id'),
            'season': match_data.get('season')
        })
        
        return default_features
    
    def create_feature_matrix(self, matches_data: List[Dict[str, Any]], 
                            include_targets: bool = True) -> pd.DataFrame:
        """Create a complete feature matrix for modeling.
        
        Args:
            matches_data: List of match dictionaries
            include_targets: Whether to include target variables
            
        Returns:
            Feature matrix DataFrame
        """
        self.logger.info("Creating feature matrix...")
        
        # Extract features
        features_df = self.extract_features(matches_data)
        
        # Handle missing values
        features_df = self._handle_missing_values(features_df)
        
        # Feature selection and engineering
        features_df = self._post_process_features(features_df, include_targets)
        
        self.logger.info(f"Feature matrix created with shape: {features_df.shape}")
        return features_df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in the feature matrix.
        
        Args:
            df: Feature DataFrame
            
        Returns:
            DataFrame with missing values handled
        """
        # Identify numeric and categorical columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        categorical_columns = df.select_dtypes(include=['object']).columns
        
        # Fill numeric missing values with median
        for col in numeric_columns:
            if df[col].isnull().any():
                median_value = df[col].median()
                df[col].fillna(median_value, inplace=True)
        
        # Fill categorical missing values with mode
        for col in categorical_columns:
            if df[col].isnull().any():
                mode_value = df[col].mode().iloc[0] if not df[col].mode().empty else 'Unknown'
                df[col].fillna(mode_value, inplace=True)
        
        return df
    
    def _post_process_features(self, df: pd.DataFrame, include_targets: bool) -> pd.DataFrame:
        """Post-process features after extraction.
        
        Args:
            df: Feature DataFrame
            include_targets: Whether to include target variables
            
        Returns:
            Post-processed DataFrame
        """
        # Create additional derived features
        if 'home_recent_form' in df.columns and 'away_recent_form' in df.columns:
            df['form_advantage'] = df['home_recent_form'] - df['away_recent_form']
        
        if 'home_goals_scored_avg' in df.columns and 'away_goals_conceded_avg' in df.columns:
            df['expected_home_goals'] = (df['home_goals_scored_avg'] + df['away_goals_conceded_avg']) / 2
        
        if 'away_goals_scored_avg' in df.columns and 'home_goals_conceded_avg' in df.columns:
            df['expected_away_goals'] = (df['away_goals_scored_avg'] + df['home_goals_conceded_avg']) / 2
        
        # Remove highly correlated features
        df = self._remove_correlated_features(df, threshold=0.95)
        
        # Ensure all numeric features are float type
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].astype(float)
        
        return df
    
    def _remove_correlated_features(self, df: pd.DataFrame, threshold: float = 0.95) -> pd.DataFrame:
        """Remove highly correlated features.
        
        Args:
            df: Feature DataFrame
            threshold: Correlation threshold for removal
            
        Returns:
            DataFrame with correlated features removed
        """
        # Only consider numeric columns for correlation
        numeric_df = df.select_dtypes(include=[np.number])
        
        if numeric_df.empty:
            return df
        
        # Calculate correlation matrix
        corr_matrix = numeric_df.corr().abs()
        
        # Find highly correlated pairs
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        # Find features to drop
        to_drop = [column for column in upper_triangle.columns 
                  if any(upper_triangle[column] > threshold)]
        
        if to_drop:
            self.logger.info(f"Removing {len(to_drop)} highly correlated features: {to_drop}")
            df = df.drop(columns=to_drop)
        
        return df
    
    def get_feature_names(self) -> List[str]:
        """Get list of all possible feature names.
        
        Returns:
            List of feature names
        """
        # This would return all possible feature names from all extractors
        # For now, return a basic list
        feature_names = []
        
        for extractor in self.extractors.values():
            # Get default features to understand the feature names
            try:
                default_features = extractor.extract_features({}, None)
                feature_names.extend(default_features.keys())
            except:
                pass
        
        return feature_names
