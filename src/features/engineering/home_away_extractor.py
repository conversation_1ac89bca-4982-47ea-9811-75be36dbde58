"""Home/Away advantage feature extractor."""

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime, timedelta

from .base_feature_extractor import BaseFeatureExtractor


class HomeAwayAdvantageExtractor(BaseFeatureExtractor):
    """Extracts home/away advantage features."""
    
    def __init__(self):
        """Initialize home/away advantage extractor."""
        super().__init__("home_away_advantage")
        
        # Get configuration
        self.min_matches = self.feature_config.get('home_away', {}).get('min_matches', 10)
        self.seasons_lookback = self.feature_config.get('home_away', {}).get('seasons_lookback', 3)
    
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract home/away advantage features.
        
        Args:
            match_data: Match data dictionary
            context_data: Context data with historical matches
            
        Returns:
            Dictionary of home/away features
        """
        features = {}
        
        if not context_data:
            return self._get_default_features()
        
        home_team_id = match_data.get('home_team_id')
        away_team_id = match_data.get('away_team_id')
        
        home_matches = context_data.get('home_team_matches', [])
        away_matches = context_data.get('away_team_matches', [])
        
        # Extract home team home advantage
        home_advantage = self._calculate_home_advantage(home_matches, home_team_id)
        features.update({f'home_team_{k}': v for k, v in home_advantage.items()})
        
        # Extract away team away performance
        away_performance = self._calculate_away_performance(away_matches, away_team_id)
        features.update({f'away_team_{k}': v for k, v in away_performance.items()})
        
        # Calculate venue-specific features
        venue_features = self._calculate_venue_features(match_data, context_data)
        features.update(venue_features)
        
        # Calculate relative home/away features
        relative_features = self._calculate_relative_features(home_advantage, away_performance)
        features.update(relative_features)
        
        return features
    
    def _calculate_home_advantage(self, matches: List[Dict[str, Any]], 
                                team_id: int) -> Dict[str, float]:
        """Calculate home advantage metrics for a team.
        
        Args:
            matches: List of all team matches
            team_id: Team ID
            
        Returns:
            Dictionary of home advantage metrics
        """
        # Filter for home matches only
        home_matches = [m for m in matches if m['home_team_id'] == team_id]
        
        if len(home_matches) < self.min_matches:
            return self._get_default_home_metrics()
        
        # Calculate home performance
        home_form, _ = self.calculate_team_form(home_matches, team_id, len(home_matches))
        home_goal_stats = self.calculate_goal_statistics(home_matches, team_id)
        home_results = self._calculate_result_percentages(home_matches, team_id)
        
        # Calculate overall performance for comparison
        overall_form, _ = self.calculate_team_form(matches, team_id, len(matches))
        overall_goal_stats = self.calculate_goal_statistics(matches, team_id)
        overall_results = self._calculate_result_percentages(matches, team_id)
        
        # Calculate home advantage (difference between home and overall performance)
        return {
            'home_form': home_form,
            'home_advantage_form': home_form - overall_form,
            'home_win_rate': home_results['win_percentage'],
            'home_advantage_win_rate': home_results['win_percentage'] - overall_results['win_percentage'],
            'home_goals_scored_avg': home_goal_stats['goals_scored_avg'],
            'home_goals_conceded_avg': home_goal_stats['goals_conceded_avg'],
            'home_goal_difference_avg': home_goal_stats['goal_difference_avg'],
            'home_advantage_goals': (home_goal_stats['goals_scored_avg'] - 
                                   overall_goal_stats['goals_scored_avg']),
            'home_defensive_advantage': (overall_goal_stats['goals_conceded_avg'] - 
                                       home_goal_stats['goals_conceded_avg']),
            'home_matches_played': len(home_matches)
        }
    
    def _calculate_away_performance(self, matches: List[Dict[str, Any]], 
                                  team_id: int) -> Dict[str, float]:
        """Calculate away performance metrics for a team.
        
        Args:
            matches: List of all team matches
            team_id: Team ID
            
        Returns:
            Dictionary of away performance metrics
        """
        # Filter for away matches only
        away_matches = [m for m in matches if m['away_team_id'] == team_id]
        
        if len(away_matches) < self.min_matches:
            return self._get_default_away_metrics()
        
        # Calculate away performance
        away_form, _ = self.calculate_team_form(away_matches, team_id, len(away_matches))
        away_goal_stats = self.calculate_goal_statistics(away_matches, team_id)
        away_results = self._calculate_result_percentages(away_matches, team_id)
        
        # Calculate overall performance for comparison
        overall_form, _ = self.calculate_team_form(matches, team_id, len(matches))
        overall_goal_stats = self.calculate_goal_statistics(matches, team_id)
        overall_results = self._calculate_result_percentages(matches, team_id)
        
        # Calculate away disadvantage (difference between away and overall performance)
        return {
            'away_form': away_form,
            'away_disadvantage_form': away_form - overall_form,
            'away_win_rate': away_results['win_percentage'],
            'away_disadvantage_win_rate': away_results['win_percentage'] - overall_results['win_percentage'],
            'away_goals_scored_avg': away_goal_stats['goals_scored_avg'],
            'away_goals_conceded_avg': away_goal_stats['goals_conceded_avg'],
            'away_goal_difference_avg': away_goal_stats['goal_difference_avg'],
            'away_disadvantage_goals': (away_goal_stats['goals_scored_avg'] - 
                                      overall_goal_stats['goals_scored_avg']),
            'away_defensive_disadvantage': (away_goal_stats['goals_conceded_avg'] - 
                                          overall_goal_stats['goals_conceded_avg']),
            'away_matches_played': len(away_matches)
        }
    
    def _calculate_venue_features(self, match_data: Dict[str, Any], 
                                context_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate venue-specific features.
        
        Args:
            match_data: Match data
            context_data: Context data
            
        Returns:
            Dictionary of venue features
        """
        features = {}
        
        # Get venue information
        venue = match_data.get('venue')
        if not venue:
            return {'venue_familiarity_home': 0.5, 'venue_familiarity_away': 0.0}
        
        home_team_id = match_data.get('home_team_id')
        away_team_id = match_data.get('away_team_id')
        
        # Calculate home team familiarity with venue (should be high for home team)
        home_venue_matches = self._count_venue_matches(
            context_data.get('home_team_matches', []), home_team_id, venue
        )
        
        # Calculate away team familiarity with venue
        away_venue_matches = self._count_venue_matches(
            context_data.get('away_team_matches', []), away_team_id, venue
        )
        
        features['venue_familiarity_home'] = min(1.0, home_venue_matches / 10.0)  # Normalize
        features['venue_familiarity_away'] = min(1.0, away_venue_matches / 10.0)  # Normalize
        
        return features
    
    def _count_venue_matches(self, matches: List[Dict[str, Any]], 
                           team_id: int, venue: str) -> int:
        """Count matches played at a specific venue.
        
        Args:
            matches: List of matches
            team_id: Team ID
            venue: Venue name
            
        Returns:
            Number of matches at venue
        """
        count = 0
        for match in matches:
            if match.get('venue') == venue:
                count += 1
        return count
    
    def _calculate_result_percentages(self, matches: List[Dict[str, Any]], 
                                    team_id: int) -> Dict[str, float]:
        """Calculate win/draw/loss percentages for matches.
        
        Args:
            matches: List of matches
            team_id: Team ID
            
        Returns:
            Dictionary with result percentages
        """
        if not matches:
            return {'win_percentage': 0.0, 'draw_percentage': 0.0, 'loss_percentage': 0.0}
        
        wins = draws = losses = 0
        
        for match in matches:
            home_score = match.get('home_score', 0) or 0
            away_score = match.get('away_score', 0) or 0
            
            if match['home_team_id'] == team_id:
                # Team played at home
                if home_score > away_score:
                    wins += 1
                elif home_score == away_score:
                    draws += 1
                else:
                    losses += 1
            else:
                # Team played away
                if away_score > home_score:
                    wins += 1
                elif away_score == home_score:
                    draws += 1
                else:
                    losses += 1
        
        total = len(matches)
        return {
            'win_percentage': wins / total,
            'draw_percentage': draws / total,
            'loss_percentage': losses / total
        }
    
    def _calculate_relative_features(self, home_advantage: Dict[str, float], 
                                   away_performance: Dict[str, float]) -> Dict[str, float]:
        """Calculate relative home/away features.
        
        Args:
            home_advantage: Home team advantage metrics
            away_performance: Away team performance metrics
            
        Returns:
            Dictionary of relative features
        """
        return {
            'home_away_form_difference': (
                home_advantage.get('home_form', 0.5) - 
                away_performance.get('away_form', 0.5)
            ),
            'home_away_advantage_difference': (
                home_advantage.get('home_advantage_form', 0.0) - 
                away_performance.get('away_disadvantage_form', 0.0)
            ),
            'home_away_win_rate_difference': (
                home_advantage.get('home_win_rate', 0.33) - 
                away_performance.get('away_win_rate', 0.33)
            ),
            'home_away_goal_difference': (
                home_advantage.get('home_goal_difference_avg', 0.0) - 
                away_performance.get('away_goal_difference_avg', 0.0)
            )
        }
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when no data is available.
        
        Returns:
            Dictionary of default features
        """
        features = {}
        
        # Default home team features
        home_features = self._get_default_home_metrics()
        features.update({f'home_team_{k}': v for k, v in home_features.items()})
        
        # Default away team features
        away_features = self._get_default_away_metrics()
        features.update({f'away_team_{k}': v for k, v in away_features.items()})
        
        # Default venue features
        features.update({
            'venue_familiarity_home': 0.5,
            'venue_familiarity_away': 0.0
        })
        
        # Default relative features
        features.update({
            'home_away_form_difference': 0.0,
            'home_away_advantage_difference': 0.0,
            'home_away_win_rate_difference': 0.0,
            'home_away_goal_difference': 0.0
        })
        
        return features
    
    def _get_default_home_metrics(self) -> Dict[str, float]:
        """Get default home advantage metrics.
        
        Returns:
            Dictionary of default home metrics
        """
        return {
            'home_form': 0.55,  # Slight home advantage
            'home_advantage_form': 0.05,
            'home_win_rate': 0.45,
            'home_advantage_win_rate': 0.1,
            'home_goals_scored_avg': 1.2,
            'home_goals_conceded_avg': 0.9,
            'home_goal_difference_avg': 0.3,
            'home_advantage_goals': 0.2,
            'home_defensive_advantage': 0.1,
            'home_matches_played': 0
        }
    
    def _get_default_away_metrics(self) -> Dict[str, float]:
        """Get default away performance metrics.
        
        Returns:
            Dictionary of default away metrics
        """
        return {
            'away_form': 0.45,  # Slight away disadvantage
            'away_disadvantage_form': -0.05,
            'away_win_rate': 0.3,
            'away_disadvantage_win_rate': -0.1,
            'away_goals_scored_avg': 0.9,
            'away_goals_conceded_avg': 1.2,
            'away_goal_difference_avg': -0.3,
            'away_disadvantage_goals': -0.2,
            'away_defensive_disadvantage': 0.1,
            'away_matches_played': 0
        }
