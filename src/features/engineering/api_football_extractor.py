"""Enhanced feature extractor leveraging API-Football rich data."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np

from .base_feature_extractor import BaseFeatureExtractor


class APIFootballFeatureExtractor(BaseFeatureExtractor):
    """Feature extractor that leverages API-Football's comprehensive data."""
    
    def __init__(self):
        """Initialize API-Football feature extractor."""
        super().__init__("api_football_features")
        
        # Enhanced position weights based on API-Football position data
        self.position_weights = {
            # Goalkeepers
            'Goalkeeper': 1.0,
            
            # Defenders
            'Centre-Back': 0.85,
            'Left-Back': 0.75,
            'Right-Back': 0.75,
            'Defender': 0.8,
            
            # Midfielders
            'Defensive Midfield': 0.8,
            'Central Midfield': 0.85,
            'Attacking Midfield': 0.8,
            'Left Midfield': 0.7,
            'Right Midfield': 0.7,
            'Midfielder': 0.75,
            
            # Forwards
            'Centre-Forward': 0.9,
            'Left Winger': 0.75,
            'Right Winger': 0.75,
            'Attacker': 0.85
        }
        
        # Event type weights for match events
        self.event_weights = {
            'Goal': 1.0,
            'Card': 0.6,
            'subst': 0.3,
            'Var': 0.4
        }
    
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract comprehensive features using API-Football data.
        
        Args:
            match_data: Match information
            context_data: Rich context data from API-Football
            
        Returns:
            Dictionary of extracted features
        """
        features = {}
        
        if not context_data:
            return self._get_default_features()
        
        # Extract basic match features
        features.update(self._extract_match_context_features(match_data))
        
        # Extract team-specific features
        features.update(self._extract_team_features(match_data, context_data))
        
        # Extract injury impact features
        features.update(self._extract_injury_features(match_data, context_data))
        
        # Extract recent form features
        features.update(self._extract_form_features(match_data, context_data))
        
        # Extract head-to-head features
        features.update(self._extract_h2h_features(match_data, context_data))
        
        # Extract venue and referee features
        features.update(self._extract_venue_referee_features(match_data, context_data))
        
        # Extract statistical features
        features.update(self._extract_statistical_features(match_data, context_data))
        
        return features
    
    def _extract_match_context_features(self, match_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract features from match context."""
        features = {}
        
        # Time-based features
        match_date = match_data.get('match_date')
        if isinstance(match_date, str):
            match_date = datetime.fromisoformat(match_date.replace('Z', '+00:00'))
        
        if match_date:
            features['match_day_of_week'] = match_date.weekday()
            features['match_hour'] = match_date.hour
            features['match_month'] = match_date.month
            features['is_weekend'] = 1.0 if match_date.weekday() >= 5 else 0.0
        
        # Round/matchday features
        round_info = match_data.get('round', '')
        if 'Regular Season' in round_info:
            try:
                matchday = int(round_info.split('-')[-1])
                features['matchday'] = matchday
                features['season_progress'] = matchday / 38.0  # Assuming 38 matchdays
            except:
                features['matchday'] = 0.0
                features['season_progress'] = 0.0
        
        return features
    
    def _extract_team_features(self, match_data: Dict[str, Any], 
                              context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract team-specific features."""
        features = {}
        
        # Team statistics from API-Football
        home_stats = context_data.get('home_team_stats', {})
        away_stats = context_data.get('away_team_stats', {})
        
        if home_stats:
            features.update(self._process_team_stats(home_stats, 'home'))
        
        if away_stats:
            features.update(self._process_team_stats(away_stats, 'away'))
        
        return features
    
    def _process_team_stats(self, team_stats: Dict[str, Any], prefix: str) -> Dict[str, float]:
        """Process team statistics from API-Football."""
        features = {}
        
        # Extract key statistics
        fixtures = team_stats.get('fixtures', {})
        goals = team_stats.get('goals', {})
        
        if fixtures:
            played = fixtures.get('played', {}).get('total', 0)
            wins = fixtures.get('wins', {}).get('total', 0)
            draws = fixtures.get('draws', {}).get('total', 0)
            losses = fixtures.get('loses', {}).get('total', 0)
            
            if played > 0:
                features[f'{prefix}_win_rate'] = wins / played
                features[f'{prefix}_draw_rate'] = draws / played
                features[f'{prefix}_loss_rate'] = losses / played
                features[f'{prefix}_points_per_game'] = (wins * 3 + draws) / played
        
        if goals:
            goals_for = goals.get('for', {}).get('total', {}).get('total', 0)
            goals_against = goals.get('against', {}).get('total', {}).get('total', 0)
            
            features[f'{prefix}_goals_per_game'] = goals_for / max(played, 1)
            features[f'{prefix}_goals_conceded_per_game'] = goals_against / max(played, 1)
            features[f'{prefix}_goal_difference'] = goals_for - goals_against
        
        return features
    
    def _extract_injury_features(self, match_data: Dict[str, Any], 
                                context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract injury impact features using API-Football injury data."""
        features = {}
        
        home_injuries = context_data.get('home_team_injuries', [])
        away_injuries = context_data.get('away_team_injuries', [])
        home_players = context_data.get('home_team_players', [])
        away_players = context_data.get('away_team_players', [])
        
        # Calculate injury impact for each team
        features['home_injury_count'] = len(home_injuries)
        features['away_injury_count'] = len(away_injuries)
        
        # Weighted injury impact based on player importance
        home_injury_impact = self._calculate_injury_impact(home_injuries, home_players)
        away_injury_impact = self._calculate_injury_impact(away_injuries, away_players)
        
        features['home_injury_impact'] = home_injury_impact
        features['away_injury_impact'] = away_injury_impact
        features['injury_advantage'] = away_injury_impact - home_injury_impact
        
        return features
    
    def _calculate_injury_impact(self, injuries: List[Dict[str, Any]], 
                               players: List[Dict[str, Any]]) -> float:
        """Calculate weighted injury impact."""
        if not injuries or not players:
            return 0.0
        
        total_impact = 0.0
        
        for injury in injuries:
            player_id = injury.get('player_id')
            
            # Find player in team roster
            player = next((p for p in players if p.get('external_id') == player_id), None)
            
            if player:
                position = player.get('position', 'Unknown')
                rating = player.get('rating', 5.0)  # Default rating
                
                # Calculate impact based on position importance and player rating
                position_weight = self.position_weights.get(position, 0.5)
                player_impact = position_weight * (rating / 10.0)  # Normalize rating
                
                total_impact += player_impact
        
        return total_impact
    
    def _extract_form_features(self, match_data: Dict[str, Any], 
                              context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract recent form features."""
        features = {}
        
        home_recent_matches = context_data.get('home_team_matches', [])
        away_recent_matches = context_data.get('away_team_matches', [])
        
        # Calculate form over different periods
        for period, matches_count in [(5, 'recent'), (10, 'medium'), (15, 'extended')]:
            home_form = self._calculate_form(home_recent_matches[:matches_count], match_data.get('home_team_id'))
            away_form = self._calculate_form(away_recent_matches[:matches_count], match_data.get('away_team_id'))
            
            features[f'home_{period}_form'] = home_form
            features[f'away_{period}_form'] = away_form
            features[f'{period}_form_difference'] = home_form - away_form
        
        return features
    
    def _calculate_form(self, matches: List[Dict[str, Any]], team_id: int) -> float:
        """Calculate team form from recent matches."""
        if not matches:
            return 0.0
        
        points = 0
        for match in matches:
            home_team_id = match.get('home_team_id')
            away_team_id = match.get('away_team_id')
            home_score = match.get('home_score', 0)
            away_score = match.get('away_score', 0)
            
            if home_score is None or away_score is None:
                continue
            
            if team_id == home_team_id:
                if home_score > away_score:
                    points += 3  # Win
                elif home_score == away_score:
                    points += 1  # Draw
            elif team_id == away_team_id:
                if away_score > home_score:
                    points += 3  # Win
                elif home_score == away_score:
                    points += 1  # Draw
        
        return points / (len(matches) * 3)  # Normalize to 0-1 scale
    
    def _extract_h2h_features(self, match_data: Dict[str, Any], 
                             context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract head-to-head features."""
        features = {}
        
        h2h_matches = context_data.get('h2h_matches', [])
        
        if h2h_matches:
            home_team_id = match_data.get('home_team_id')
            away_team_id = match_data.get('away_team_id')
            
            home_wins = 0
            draws = 0
            away_wins = 0
            total_goals_home = 0
            total_goals_away = 0
            
            for match in h2h_matches:
                h_team = match.get('home_team_id')
                a_team = match.get('away_team_id')
                h_score = match.get('home_score', 0)
                a_score = match.get('away_score', 0)
                
                if h_score is None or a_score is None:
                    continue
                
                # Determine which team is which in the H2H context
                if h_team == home_team_id:
                    total_goals_home += h_score
                    total_goals_away += a_score
                    if h_score > a_score:
                        home_wins += 1
                    elif h_score == a_score:
                        draws += 1
                    else:
                        away_wins += 1
                else:
                    total_goals_home += a_score
                    total_goals_away += h_score
                    if a_score > h_score:
                        home_wins += 1
                    elif h_score == a_score:
                        draws += 1
                    else:
                        away_wins += 1
            
            total_matches = len(h2h_matches)
            if total_matches > 0:
                features['h2h_home_win_rate'] = home_wins / total_matches
                features['h2h_draw_rate'] = draws / total_matches
                features['h2h_away_win_rate'] = away_wins / total_matches
                features['h2h_avg_goals_home'] = total_goals_home / total_matches
                features['h2h_avg_goals_away'] = total_goals_away / total_matches
                features['h2h_matches_count'] = total_matches
        
        return features
    
    def _extract_venue_referee_features(self, match_data: Dict[str, Any], 
                                       context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract venue and referee features."""
        features = {}
        
        # Venue features
        venue = match_data.get('venue', '')
        venue_city = match_data.get('venue_city', '')
        
        # Home advantage indicator
        features['is_home_venue'] = 1.0  # Always 1 for home team by definition
        
        # Referee features (if available)
        referee = match_data.get('referee', '')
        if referee:
            # Could add referee-specific statistics if available in context
            features['has_referee_info'] = 1.0
        else:
            features['has_referee_info'] = 0.0
        
        return features
    
    def _extract_statistical_features(self, match_data: Dict[str, Any], 
                                     context_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract advanced statistical features."""
        features = {}
        
        # League standings if available
        standings = context_data.get('league_standings', [])
        if standings:
            home_team_id = match_data.get('home_team_id')
            away_team_id = match_data.get('away_team_id')
            
            home_position = self._get_team_position(standings, home_team_id)
            away_position = self._get_team_position(standings, away_team_id)
            
            if home_position and away_position:
                features['home_league_position'] = home_position
                features['away_league_position'] = away_position
                features['position_difference'] = away_position - home_position
        
        return features
    
    def _get_team_position(self, standings: List[Dict[str, Any]], team_id: int) -> Optional[int]:
        """Get team position from league standings."""
        for standing in standings:
            if standing.get('team', {}).get('id') == team_id:
                return standing.get('rank')
        return None
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when no context data is available."""
        return {
            'home_injury_count': 0.0,
            'away_injury_count': 0.0,
            'home_injury_impact': 0.0,
            'away_injury_impact': 0.0,
            'injury_advantage': 0.0,
            'home_recent_form': 0.5,
            'away_recent_form': 0.5,
            'form_difference': 0.0,
            'h2h_home_win_rate': 0.33,
            'h2h_draw_rate': 0.33,
            'h2h_away_win_rate': 0.33,
            'is_weekend': 0.0,
            'matchday': 0.0,
            'season_progress': 0.0
        }
