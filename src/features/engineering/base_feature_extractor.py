"""Base feature extractor class for the football prediction system."""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.utils.database import DatabaseManager
from src.utils.config import config


class BaseFeatureExtractor(ABC):
    """Abstract base class for feature extractors."""
    
    def __init__(self, name: str):
        """Initialize base feature extractor.
        
        Args:
            name: Name of the feature extractor
        """
        self.name = name
        self.logger = logging.getLogger(f"features.{name}")
        self.db_manager = DatabaseManager()
        self.feature_config = config.get_feature_config()
    
    @abstractmethod
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract features for a specific match.
        
        Args:
            match_data: Dictionary containing match information
            context_data: Additional context data (historical matches, team stats, etc.)
            
        Returns:
            Dictionary of feature names to values
        """
        pass
    
    def extract_batch_features(self, matches_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """Extract features for multiple matches.
        
        Args:
            matches_data: List of match dictionaries
            
        Returns:
            DataFrame with features for all matches
        """
        features_list = []
        
        for match_data in matches_data:
            try:
                # Get context data for this match
                context_data = self._get_context_data(match_data)
                
                # Extract features
                features = self.extract_features(match_data, context_data)
                
                # Add match identifier
                features['match_id'] = match_data.get('id', match_data.get('external_id'))
                features['home_team_id'] = match_data.get('home_team_id')
                features['away_team_id'] = match_data.get('away_team_id')
                features['match_date'] = match_data.get('match_date')
                
                features_list.append(features)
                
            except Exception as e:
                self.logger.error(f"Error extracting features for match {match_data.get('id', 'Unknown')}: {e}")
        
        return pd.DataFrame(features_list)
    
    def _get_context_data(self, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get context data for feature extraction.
        
        Args:
            match_data: Match data dictionary
            
        Returns:
            Context data dictionary
        """
        context = {}
        
        try:
            home_team_id = match_data.get('home_team_id')
            away_team_id = match_data.get('away_team_id')
            match_date = match_data.get('match_date')
            
            if not all([home_team_id, away_team_id, match_date]):
                return context
            
            # Convert match_date to datetime if it's a string
            if isinstance(match_date, str):
                match_date = pd.to_datetime(match_date)
            
            # Get historical matches for both teams
            context['home_team_matches'] = self._get_team_matches(
                home_team_id, match_date, days_back=365
            )
            context['away_team_matches'] = self._get_team_matches(
                away_team_id, match_date, days_back=365
            )
            
            # Get head-to-head matches
            context['h2h_matches'] = self._get_h2h_matches(
                home_team_id, away_team_id, match_date
            )
            
            # Get team statistics
            context['home_team_stats'] = self._get_team_stats(home_team_id, match_date)
            context['away_team_stats'] = self._get_team_stats(away_team_id, match_date)
            
            # Get player data
            context['home_team_players'] = self._get_team_players(home_team_id)
            context['away_team_players'] = self._get_team_players(away_team_id)
            
            # Get injury data
            context['home_team_injuries'] = self._get_team_injuries(home_team_id, match_date)
            context['away_team_injuries'] = self._get_team_injuries(away_team_id, match_date)
            
        except Exception as e:
            self.logger.error(f"Error getting context data: {e}")
        
        return context
    
    def _get_team_matches(self, team_id: int, before_date: datetime, 
                         days_back: int = 365) -> List[Dict[str, Any]]:
        """Get historical matches for a team.
        
        Args:
            team_id: Team ID
            before_date: Get matches before this date
            days_back: Number of days to look back
            
        Returns:
            List of match dictionaries
        """
        try:
            cutoff_date = before_date - timedelta(days=days_back)
            
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match
                
                matches = session.query(Match).filter(
                    ((Match.home_team_id == team_id) | (Match.away_team_id == team_id)),
                    Match.match_date >= cutoff_date,
                    Match.match_date < before_date,
                    Match.status == 'FINISHED'
                ).order_by(Match.match_date.desc()).all()
                
                return [self._match_to_dict(match) for match in matches]
                
        except Exception as e:
            self.logger.error(f"Error getting team matches for team {team_id}: {e}")
            return []
    
    def _get_h2h_matches(self, home_team_id: int, away_team_id: int, 
                        before_date: datetime, max_matches: int = 10) -> List[Dict[str, Any]]:
        """Get head-to-head matches between two teams.
        
        Args:
            home_team_id: Home team ID
            away_team_id: Away team ID
            before_date: Get matches before this date
            max_matches: Maximum number of matches to return
            
        Returns:
            List of head-to-head match dictionaries
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Match
                
                matches = session.query(Match).filter(
                    (
                        (Match.home_team_id == home_team_id) & (Match.away_team_id == away_team_id)
                    ) | (
                        (Match.home_team_id == away_team_id) & (Match.away_team_id == home_team_id)
                    ),
                    Match.match_date < before_date,
                    Match.status == 'FINISHED'
                ).order_by(Match.match_date.desc()).limit(max_matches).all()
                
                return [self._match_to_dict(match) for match in matches]
                
        except Exception as e:
            self.logger.error(f"Error getting H2H matches: {e}")
            return []
    
    def _get_team_stats(self, team_id: int, as_of_date: datetime) -> Dict[str, Any]:
        """Get team statistics as of a specific date.
        
        Args:
            team_id: Team ID
            as_of_date: Date to get statistics as of
            
        Returns:
            Team statistics dictionary
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import TeamStatistics
                
                # Get the most recent statistics before the given date
                stats = session.query(TeamStatistics).filter(
                    TeamStatistics.team_id == team_id
                ).order_by(TeamStatistics.updated_at.desc()).first()
                
                if stats:
                    return {
                        'position': stats.position,
                        'points': stats.points,
                        'matches_played': stats.matches_played,
                        'wins': stats.wins,
                        'draws': stats.draws,
                        'losses': stats.losses,
                        'goals_for': stats.goals_for,
                        'goals_against': stats.goals_against,
                        'goal_difference': stats.goal_difference,
                        'form': stats.form
                    }
                
        except Exception as e:
            self.logger.error(f"Error getting team stats for team {team_id}: {e}")
        
        return {}
    
    def _get_team_players(self, team_id: int) -> List[Dict[str, Any]]:
        """Get players for a team.
        
        Args:
            team_id: Team ID
            
        Returns:
            List of player dictionaries
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Player
                
                players = session.query(Player).filter(
                    Player.team_id == team_id
                ).all()
                
                return [self._player_to_dict(player) for player in players]
                
        except Exception as e:
            self.logger.error(f"Error getting players for team {team_id}: {e}")
            return []
    
    def _get_team_injuries(self, team_id: int, as_of_date: datetime) -> List[Dict[str, Any]]:
        """Get active injuries for a team as of a specific date.
        
        Args:
            team_id: Team ID
            as_of_date: Date to check injuries as of
            
        Returns:
            List of injury dictionaries
        """
        try:
            with self.db_manager.get_session() as session:
                from src.utils.database.models import Injury, Player
                
                injuries = session.query(Injury).join(Player).filter(
                    Player.team_id == team_id,
                    Injury.injury_date <= as_of_date,
                    (Injury.expected_return_date.is_(None)) | (Injury.expected_return_date > as_of_date),
                    Injury.status == 'Active'
                ).all()
                
                return [self._injury_to_dict(injury) for injury in injuries]
                
        except Exception as e:
            self.logger.error(f"Error getting injuries for team {team_id}: {e}")
            return []
    
    def _match_to_dict(self, match) -> Dict[str, Any]:
        """Convert match object to dictionary.
        
        Args:
            match: Match database object
            
        Returns:
            Match dictionary
        """
        return {
            'id': match.id,
            'external_id': match.external_id,
            'home_team_id': match.home_team_id,
            'away_team_id': match.away_team_id,
            'match_date': match.match_date,
            'home_score': match.home_score,
            'away_score': match.away_score,
            'home_score_ht': match.home_score_ht,
            'away_score_ht': match.away_score_ht,
            'status': match.status
        }
    
    def _player_to_dict(self, player) -> Dict[str, Any]:
        """Convert player object to dictionary.
        
        Args:
            player: Player database object
            
        Returns:
            Player dictionary
        """
        return {
            'id': player.id,
            'external_id': player.external_id,
            'name': player.name,
            'position': player.position,
            'age': player.age,
            'nationality': player.nationality
        }
    
    def _injury_to_dict(self, injury) -> Dict[str, Any]:
        """Convert injury object to dictionary.
        
        Args:
            injury: Injury database object
            
        Returns:
            Injury dictionary
        """
        return {
            'id': injury.id,
            'player_id': injury.player_id,
            'injury_type': injury.injury_type,
            'injury_date': injury.injury_date,
            'expected_return_date': injury.expected_return_date,
            'severity': injury.severity,
            'status': injury.status
        }
    
    def calculate_team_form(self, matches: List[Dict[str, Any]], team_id: int, 
                          num_matches: int = 5) -> Tuple[float, str]:
        """Calculate team form based on recent matches.
        
        Args:
            matches: List of team matches
            team_id: Team ID
            num_matches: Number of recent matches to consider
            
        Returns:
            Tuple of (form_score, form_string)
        """
        if not matches:
            return 0.0, ""
        
        recent_matches = matches[:num_matches]
        form_string = ""
        points = 0
        
        for match in recent_matches:
            if match['home_team_id'] == team_id:
                # Team played at home
                if match['home_score'] > match['away_score']:
                    form_string += "W"
                    points += 3
                elif match['home_score'] == match['away_score']:
                    form_string += "D"
                    points += 1
                else:
                    form_string += "L"
            else:
                # Team played away
                if match['away_score'] > match['home_score']:
                    form_string += "W"
                    points += 3
                elif match['away_score'] == match['home_score']:
                    form_string += "D"
                    points += 1
                else:
                    form_string += "L"
        
        # Calculate form score (0-1 scale)
        max_points = len(recent_matches) * 3
        form_score = points / max_points if max_points > 0 else 0.0
        
        return form_score, form_string
    
    def calculate_goal_statistics(self, matches: List[Dict[str, Any]], 
                                team_id: int) -> Dict[str, float]:
        """Calculate goal statistics for a team.
        
        Args:
            matches: List of team matches
            team_id: Team ID
            
        Returns:
            Dictionary of goal statistics
        """
        if not matches:
            return {
                'goals_scored_avg': 0.0,
                'goals_conceded_avg': 0.0,
                'goal_difference_avg': 0.0
            }
        
        goals_scored = []
        goals_conceded = []
        
        for match in matches:
            if match['home_team_id'] == team_id:
                goals_scored.append(match['home_score'] or 0)
                goals_conceded.append(match['away_score'] or 0)
            else:
                goals_scored.append(match['away_score'] or 0)
                goals_conceded.append(match['home_score'] or 0)
        
        return {
            'goals_scored_avg': float(np.mean(goals_scored)),
            'goals_conceded_avg': float(np.mean(goals_conceded)),
            'goal_difference_avg': float(np.mean(goals_scored) - np.mean(goals_conceded))
        }
