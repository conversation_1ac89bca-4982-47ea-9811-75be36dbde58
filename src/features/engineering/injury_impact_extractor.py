"""Injury impact feature extractor."""

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime, timedelta

from .base_feature_extractor import BaseFeatureExtractor


class InjuryImpactExtractor(BaseFeatureExtractor):
    """Extracts injury impact features."""
    
    def __init__(self):
        """Initialize injury impact extractor."""
        super().__init__("injury_impact")
        
        # Get configuration
        self.key_positions = self.feature_config.get('player_impact', {}).get('key_positions', ['GK', 'CB', 'CM', 'ST'])
        self.injury_impact_days = self.feature_config.get('player_impact', {}).get('injury_impact_days', 30)
        
        # Position importance weights
        self.position_weights = {
            'GK': 1.0,   # Goalkeeper is crucial
            'CB': 0.8,   # Center backs are important
            'LB': 0.6, 'RB': 0.6,  # Full backs
            'CM': 0.8,   # Central midfielders
            'LM': 0.6, 'RM': 0.6,  # Wing midfielders
            'CAM': 0.7, 'CDM': 0.7,  # Attacking/Defensive midfielders
            'ST': 0.9,   # Strikers are very important
            'LW': 0.7, 'RW': 0.7,  # Wingers
            'CF': 0.8    # Center forwards
        }
    
    def extract_features(self, match_data: Dict[str, Any], 
                        context_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract injury impact features.
        
        Args:
            match_data: Match data dictionary
            context_data: Context data with player and injury information
            
        Returns:
            Dictionary of injury impact features
        """
        features = {}
        
        if not context_data:
            return self._get_default_features()
        
        home_team_id = match_data.get('home_team_id')
        away_team_id = match_data.get('away_team_id')
        match_date = match_data.get('match_date')
        
        if isinstance(match_date, str):
            match_date = datetime.fromisoformat(match_date.replace('Z', '+00:00'))
        
        # Get injury data
        home_injuries = context_data.get('home_team_injuries', [])
        away_injuries = context_data.get('away_team_injuries', [])
        
        # Get player data
        home_players = context_data.get('home_team_players', [])
        away_players = context_data.get('away_team_players', [])
        
        # Calculate injury impact for home team
        home_impact = self._calculate_team_injury_impact(
            home_injuries, home_players, match_date, "home"
        )
        features.update(home_impact)
        
        # Calculate injury impact for away team
        away_impact = self._calculate_team_injury_impact(
            away_injuries, away_players, match_date, "away"
        )
        features.update(away_impact)
        
        # Calculate relative injury impact
        relative_impact = self._calculate_relative_impact(home_impact, away_impact)
        features.update(relative_impact)
        
        return features
    
    def _calculate_team_injury_impact(self, injuries: List[Dict[str, Any]], 
                                    players: List[Dict[str, Any]], 
                                    match_date: datetime, prefix: str) -> Dict[str, float]:
        """Calculate injury impact for a specific team.
        
        Args:
            injuries: List of team injuries
            players: List of team players
            match_date: Match date
            prefix: Feature prefix ("home" or "away")
            
        Returns:
            Dictionary of injury impact features
        """
        if not injuries:
            return self._get_default_team_features(prefix)
        
        # Create player lookup
        player_lookup = {p['id']: p for p in players}
        
        # Filter active injuries for match date
        active_injuries = self._filter_active_injuries(injuries, match_date)
        
        # Calculate basic injury metrics
        total_injuries = len(active_injuries)
        total_players = len(players)
        injury_rate = total_injuries / max(total_players, 1)
        
        # Calculate position-specific impacts
        position_impacts = self._calculate_position_impacts(active_injuries, player_lookup)
        
        # Calculate severity impacts
        severity_impacts = self._calculate_severity_impacts(active_injuries)
        
        # Calculate key player impacts
        key_player_impacts = self._calculate_key_player_impacts(active_injuries, player_lookup)
        
        features = {
            f'{prefix}_total_injuries': float(total_injuries),
            f'{prefix}_injury_rate': injury_rate,
            f'{prefix}_goalkeeper_injured': position_impacts['goalkeeper'],
            f'{prefix}_defense_injury_impact': position_impacts['defense'],
            f'{prefix}_midfield_injury_impact': position_impacts['midfield'],
            f'{prefix}_attack_injury_impact': position_impacts['attack'],
            f'{prefix}_severe_injuries': severity_impacts['severe'],
            f'{prefix}_moderate_injuries': severity_impacts['moderate'],
            f'{prefix}_minor_injuries': severity_impacts['minor'],
            f'{prefix}_key_players_injured': key_player_impacts['key_players'],
            f'{prefix}_injury_weighted_impact': self._calculate_weighted_impact(active_injuries, player_lookup)
        }
        
        return features
    
    def _filter_active_injuries(self, injuries: List[Dict[str, Any]], 
                              match_date: datetime) -> List[Dict[str, Any]]:
        """Filter injuries that are active on match date.
        
        Args:
            injuries: List of all injuries
            match_date: Match date
            
        Returns:
            List of active injuries
        """
        active_injuries = []
        
        for injury in injuries:
            injury_date = injury.get('injury_date')
            return_date = injury.get('expected_return_date')
            status = injury.get('status', 'Unknown')
            
            # Convert dates if they're strings
            if isinstance(injury_date, str):
                try:
                    injury_date = datetime.fromisoformat(injury_date.replace('Z', '+00:00'))
                except:
                    continue
            
            if isinstance(return_date, str):
                try:
                    return_date = datetime.fromisoformat(return_date.replace('Z', '+00:00'))
                except:
                    return_date = None
            
            # Check if injury is active on match date
            if (injury_date and injury_date <= match_date and 
                status == 'Active' and 
                (return_date is None or return_date > match_date)):
                active_injuries.append(injury)
        
        return active_injuries
    
    def _calculate_position_impacts(self, injuries: List[Dict[str, Any]], 
                                  player_lookup: Dict[int, Dict[str, Any]]) -> Dict[str, float]:
        """Calculate position-specific injury impacts.
        
        Args:
            injuries: List of active injuries
            player_lookup: Dictionary mapping player IDs to player data
            
        Returns:
            Dictionary of position impacts
        """
        position_counts = {
            'goalkeeper': 0,
            'defense': 0,
            'midfield': 0,
            'attack': 0
        }
        
        for injury in injuries:
            player_id = injury.get('player_id')
            if player_id in player_lookup:
                position = player_lookup[player_id].get('position', '').upper()
                
                if position == 'GK':
                    position_counts['goalkeeper'] = 1  # Binary for goalkeeper
                elif position in ['CB', 'LB', 'RB', 'LWB', 'RWB']:
                    position_counts['defense'] += 1
                elif position in ['CM', 'CDM', 'CAM', 'LM', 'RM']:
                    position_counts['midfield'] += 1
                elif position in ['ST', 'CF', 'LW', 'RW']:
                    position_counts['attack'] += 1
        
        # Normalize counts (except goalkeeper which is binary)
        return {
            'goalkeeper': float(position_counts['goalkeeper']),
            'defense': min(1.0, position_counts['defense'] / 4.0),  # Assume 4 defenders
            'midfield': min(1.0, position_counts['midfield'] / 4.0),  # Assume 4 midfielders
            'attack': min(1.0, position_counts['attack'] / 3.0)  # Assume 3 attackers
        }
    
    def _calculate_severity_impacts(self, injuries: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate severity-based injury impacts.
        
        Args:
            injuries: List of active injuries
            
        Returns:
            Dictionary of severity impacts
        """
        severity_counts = {'severe': 0, 'moderate': 0, 'minor': 0}
        
        for injury in injuries:
            severity = injury.get('severity', 'Unknown').lower()
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        total_injuries = len(injuries)
        if total_injuries == 0:
            return {k: 0.0 for k in severity_counts}
        
        return {k: float(v) / total_injuries for k, v in severity_counts.items()}
    
    def _calculate_key_player_impacts(self, injuries: List[Dict[str, Any]], 
                                    player_lookup: Dict[int, Dict[str, Any]]) -> Dict[str, float]:
        """Calculate impact of key player injuries.
        
        Args:
            injuries: List of active injuries
            player_lookup: Dictionary mapping player IDs to player data
            
        Returns:
            Dictionary of key player impacts
        """
        key_players_injured = 0
        
        for injury in injuries:
            player_id = injury.get('player_id')
            if player_id in player_lookup:
                player = player_lookup[player_id]
                position = player.get('position', '').upper()
                
                # Consider players in key positions as key players
                if position in self.key_positions:
                    key_players_injured += 1
        
        return {'key_players': float(key_players_injured)}
    
    def _calculate_weighted_impact(self, injuries: List[Dict[str, Any]], 
                                 player_lookup: Dict[int, Dict[str, Any]]) -> float:
        """Calculate weighted injury impact based on position importance and severity.
        
        Args:
            injuries: List of active injuries
            player_lookup: Dictionary mapping player IDs to player data
            
        Returns:
            Weighted impact score
        """
        total_impact = 0.0
        
        for injury in injuries:
            player_id = injury.get('player_id')
            if player_id in player_lookup:
                player = player_lookup[player_id]
                position = player.get('position', '').upper()
                severity = injury.get('severity', 'Moderate').lower()
                
                # Get position weight
                position_weight = self.position_weights.get(position, 0.5)
                
                # Get severity multiplier
                severity_multiplier = {
                    'severe': 1.0,
                    'moderate': 0.7,
                    'minor': 0.4
                }.get(severity, 0.7)
                
                impact = position_weight * severity_multiplier
                total_impact += impact
        
        # Normalize by typical squad size
        return min(1.0, total_impact / 11.0)  # 11 starting players
    
    def _calculate_relative_impact(self, home_impact: Dict[str, float], 
                                 away_impact: Dict[str, float]) -> Dict[str, float]:
        """Calculate relative injury impact between teams.
        
        Args:
            home_impact: Home team injury impact
            away_impact: Away team injury impact
            
        Returns:
            Dictionary of relative impact features
        """
        return {
            'injury_impact_difference': (
                home_impact.get('home_injury_weighted_impact', 0.0) - 
                away_impact.get('away_injury_weighted_impact', 0.0)
            ),
            'injury_rate_difference': (
                home_impact.get('home_injury_rate', 0.0) - 
                away_impact.get('away_injury_rate', 0.0)
            ),
            'key_players_difference': (
                home_impact.get('home_key_players_injured', 0.0) - 
                away_impact.get('away_key_players_injured', 0.0)
            )
        }
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when no injury data is available.
        
        Returns:
            Dictionary of default features
        """
        features = {}
        features.update(self._get_default_team_features("home"))
        features.update(self._get_default_team_features("away"))
        
        # Default relative features
        features.update({
            'injury_impact_difference': 0.0,
            'injury_rate_difference': 0.0,
            'key_players_difference': 0.0
        })
        
        return features
    
    def _get_default_team_features(self, prefix: str) -> Dict[str, float]:
        """Get default injury features for a team.
        
        Args:
            prefix: Feature prefix ("home" or "away")
            
        Returns:
            Dictionary of default team injury features
        """
        return {
            f'{prefix}_total_injuries': 0.0,
            f'{prefix}_injury_rate': 0.0,
            f'{prefix}_goalkeeper_injured': 0.0,
            f'{prefix}_defense_injury_impact': 0.0,
            f'{prefix}_midfield_injury_impact': 0.0,
            f'{prefix}_attack_injury_impact': 0.0,
            f'{prefix}_severe_injuries': 0.0,
            f'{prefix}_moderate_injuries': 0.0,
            f'{prefix}_minor_injuries': 0.0,
            f'{prefix}_key_players_injured': 0.0,
            f'{prefix}_injury_weighted_impact': 0.0
        }
