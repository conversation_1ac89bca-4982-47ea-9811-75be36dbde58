"""Database models for the football prediction system."""

from datetime import datetime, date
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Date, Boolean, 
    ForeignKey, Text, JSON, UniqueConstraint, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

Base = declarative_base()


class League(Base):
    """League/Competition model."""
    __tablename__ = 'leagues'
    
    id = Column(Integer, primary_key=True)
    external_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    country = Column(String(50), nullable=False)
    season_start_month = Column(Integer, default=8)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    teams = relationship("Team", back_populates="league")
    matches = relationship("Match", back_populates="league")


class Team(Base):
    """Team model."""
    __tablename__ = 'teams'
    
    id = Column(Integer, primary_key=True)
    external_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    short_name = Column(String(10))
    logo_url = Column(String(255))
    founded = Column(Integer)
    venue_name = Column(String(100))
    venue_capacity = Column(Integer)
    league_id = Column(Integer, ForeignKey('leagues.id'))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    league = relationship("League", back_populates="teams")
    home_matches = relationship("Match", foreign_keys="Match.home_team_id", back_populates="home_team")
    away_matches = relationship("Match", foreign_keys="Match.away_team_id", back_populates="away_team")
    players = relationship("Player", back_populates="team")
    team_statistics = relationship("TeamStatistics", back_populates="team")


class Player(Base):
    """Player model."""
    __tablename__ = 'players'
    
    id = Column(Integer, primary_key=True)
    external_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    position = Column(String(10))  # GK, DF, MF, FW
    age = Column(Integer)
    nationality = Column(String(50))
    height = Column(Integer)  # in cm
    weight = Column(Integer)  # in kg
    team_id = Column(Integer, ForeignKey('teams.id'))
    market_value = Column(Float)  # in millions
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    team = relationship("Team", back_populates="players")
    injuries = relationship("Injury", back_populates="player")
    player_statistics = relationship("PlayerStatistics", back_populates="player")


class Match(Base):
    """Match model."""
    __tablename__ = 'matches'
    
    id = Column(Integer, primary_key=True)
    external_id = Column(Integer, unique=True, nullable=False)
    league_id = Column(Integer, ForeignKey('leagues.id'))
    season = Column(String(10))  # e.g., "2023-24"
    matchday = Column(Integer)
    home_team_id = Column(Integer, ForeignKey('teams.id'))
    away_team_id = Column(Integer, ForeignKey('teams.id'))
    match_date = Column(DateTime, nullable=False)
    status = Column(String(20))  # SCHEDULED, LIVE, FINISHED, POSTPONED, CANCELLED
    
    # Match result
    home_score = Column(Integer)
    away_score = Column(Integer)
    home_score_ht = Column(Integer)  # Half-time score
    away_score_ht = Column(Integer)
    
    # Additional match info
    referee = Column(String(100))
    venue = Column(String(100))
    attendance = Column(Integer)
    weather_conditions = Column(JSON)  # Temperature, humidity, wind, etc.
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    league = relationship("League", back_populates="matches")
    home_team = relationship("Team", foreign_keys=[home_team_id], back_populates="home_matches")
    away_team = relationship("Team", foreign_keys=[away_team_id], back_populates="away_matches")
    match_statistics = relationship("MatchStatistics", back_populates="match")
    predictions = relationship("Prediction", back_populates="match")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('home_team_id', 'away_team_id', 'match_date', name='unique_match'),
        Index('idx_match_date', 'match_date'),
        Index('idx_match_teams', 'home_team_id', 'away_team_id'),
    )


class MatchStatistics(Base):
    """Detailed match statistics."""
    __tablename__ = 'match_statistics'
    
    id = Column(Integer, primary_key=True)
    match_id = Column(Integer, ForeignKey('matches.id'), unique=True)
    
    # Possession and shots
    home_possession = Column(Float)
    away_possession = Column(Float)
    home_shots = Column(Integer)
    away_shots = Column(Integer)
    home_shots_on_target = Column(Integer)
    away_shots_on_target = Column(Integer)
    
    # Expected goals
    home_xg = Column(Float)
    away_xg = Column(Float)
    
    # Passing statistics
    home_passes = Column(Integer)
    away_passes = Column(Integer)
    home_pass_accuracy = Column(Float)
    away_pass_accuracy = Column(Float)
    
    # Defensive statistics
    home_tackles = Column(Integer)
    away_tackles = Column(Integer)
    home_interceptions = Column(Integer)
    away_interceptions = Column(Integer)
    home_clearances = Column(Integer)
    away_clearances = Column(Integer)
    
    # Disciplinary
    home_yellow_cards = Column(Integer)
    away_yellow_cards = Column(Integer)
    home_red_cards = Column(Integer)
    away_red_cards = Column(Integer)
    
    # Set pieces
    home_corners = Column(Integer)
    away_corners = Column(Integer)
    home_free_kicks = Column(Integer)
    away_free_kicks = Column(Integer)
    
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    match = relationship("Match", back_populates="match_statistics")


class Injury(Base):
    """Player injury model."""
    __tablename__ = 'injuries'
    
    id = Column(Integer, primary_key=True)
    player_id = Column(Integer, ForeignKey('players.id'))
    injury_type = Column(String(100))
    injury_date = Column(Date)
    expected_return_date = Column(Date)
    actual_return_date = Column(Date)
    severity = Column(String(20))  # Minor, Moderate, Severe
    status = Column(String(20))  # Active, Recovered
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    player = relationship("Player", back_populates="injuries")


class TeamStatistics(Base):
    """Team statistics for a specific season."""
    __tablename__ = 'team_statistics'
    
    id = Column(Integer, primary_key=True)
    team_id = Column(Integer, ForeignKey('teams.id'))
    season = Column(String(10))
    league_id = Column(Integer, ForeignKey('leagues.id'))
    
    # League position and points
    position = Column(Integer)
    points = Column(Integer)
    matches_played = Column(Integer)
    wins = Column(Integer)
    draws = Column(Integer)
    losses = Column(Integer)
    
    # Goals
    goals_for = Column(Integer)
    goals_against = Column(Integer)
    goal_difference = Column(Integer)
    
    # Home/Away splits
    home_wins = Column(Integer)
    home_draws = Column(Integer)
    home_losses = Column(Integer)
    away_wins = Column(Integer)
    away_draws = Column(Integer)
    away_losses = Column(Integer)
    
    # Form (last 5 matches)
    form = Column(String(5))  # e.g., "WWDLW"
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    team = relationship("Team", back_populates="team_statistics")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('team_id', 'season', 'league_id', name='unique_team_season'),
    )


class PlayerStatistics(Base):
    """Player statistics for a specific season."""
    __tablename__ = 'player_statistics'
    
    id = Column(Integer, primary_key=True)
    player_id = Column(Integer, ForeignKey('players.id'))
    season = Column(String(10))
    
    # Appearances
    appearances = Column(Integer)
    minutes_played = Column(Integer)
    starts = Column(Integer)
    
    # Goals and assists
    goals = Column(Integer)
    assists = Column(Integer)
    penalties_scored = Column(Integer)
    penalties_missed = Column(Integer)
    
    # Disciplinary
    yellow_cards = Column(Integer)
    red_cards = Column(Integer)
    
    # Rating
    average_rating = Column(Float)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    player = relationship("Player", back_populates="player_statistics")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('player_id', 'season', name='unique_player_season'),
    )


class Prediction(Base):
    """Model predictions for matches."""
    __tablename__ = 'predictions'
    
    id = Column(Integer, primary_key=True)
    match_id = Column(Integer, ForeignKey('matches.id'))
    model_name = Column(String(50))
    model_version = Column(String(20))
    
    # Predictions
    home_win_prob = Column(Float)
    draw_prob = Column(Float)
    away_win_prob = Column(Float)
    predicted_home_score = Column(Float)
    predicted_away_score = Column(Float)
    
    # Confidence and features
    confidence_score = Column(Float)
    feature_importance = Column(JSON)
    
    # Metadata
    prediction_date = Column(DateTime, default=func.now())
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    match = relationship("Match", back_populates="predictions")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('match_id', 'model_name', 'model_version', name='unique_prediction'),
        Index('idx_prediction_date', 'prediction_date'),
    )
