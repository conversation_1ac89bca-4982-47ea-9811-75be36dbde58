"""Database connection and management utilities."""

import logging
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .models import Base, League, Team, Player, Match, Injury, TeamStatistics, PlayerStatistics, Prediction
from src.utils.config import config


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        """Initialize database manager."""
        self.logger = logging.getLogger("database")
        self.db_config = config.get_database_config()
        self.engine = self._create_engine()
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def _create_engine(self):
        """Create database engine based on configuration."""
        if self.db_config.type == 'postgresql':
            database_url = (
                f"postgresql://{self.db_config.user}:{self.db_config.password}"
                f"@{self.db_config.host}:{self.db_config.port}/{self.db_config.name}"
            )
        elif self.db_config.type == 'sqlite':
            database_url = f"sqlite:///{self.db_config.name}.db"
        else:
            raise ValueError(f"Unsupported database type: {self.db_config.type}")
        
        engine = create_engine(
            database_url,
            echo=False,  # Set to True for SQL debugging
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        self.logger.info(f"Created database engine for {self.db_config.type}")
        return engine
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("Database tables created successfully")
        except SQLAlchemyError as e:
            self.logger.error(f"Error creating database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.info("Database tables dropped successfully")
        except SQLAlchemyError as e:
            self.logger.error(f"Error dropping database tables: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """Get database session with automatic cleanup."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """Test database connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            self.logger.info("Database connection test successful")
            return True
        except Exception as e:
            self.logger.error(f"Database connection test failed: {e}")
            return False
    
    def save_leagues(self, leagues_data: List[Dict[str, Any]]) -> int:
        """Save league data to database.
        
        Args:
            leagues_data: List of league dictionaries
            
        Returns:
            Number of leagues saved
        """
        saved_count = 0
        
        with self.get_session() as session:
            for league_data in leagues_data:
                try:
                    # Check if league already exists
                    existing_league = session.query(League).filter_by(
                        external_id=league_data['external_id']
                    ).first()
                    
                    if existing_league:
                        # Update existing league
                        for key, value in league_data.items():
                            if hasattr(existing_league, key):
                                setattr(existing_league, key, value)
                    else:
                        # Create new league
                        league = League(**league_data)
                        session.add(league)
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error saving league {league_data.get('name', 'Unknown')}: {e}")
        
        self.logger.info(f"Saved {saved_count} leagues to database")
        return saved_count
    
    def save_teams(self, teams_data: List[Dict[str, Any]]) -> int:
        """Save team data to database.
        
        Args:
            teams_data: List of team dictionaries
            
        Returns:
            Number of teams saved
        """
        saved_count = 0
        
        with self.get_session() as session:
            for team_data in teams_data:
                try:
                    # Check if team already exists
                    existing_team = session.query(Team).filter_by(
                        external_id=team_data['external_id']
                    ).first()
                    
                    if existing_team:
                        # Update existing team
                        for key, value in team_data.items():
                            if hasattr(existing_team, key):
                                setattr(existing_team, key, value)
                    else:
                        # Create new team
                        team = Team(**team_data)
                        session.add(team)
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error saving team {team_data.get('name', 'Unknown')}: {e}")
        
        self.logger.info(f"Saved {saved_count} teams to database")
        return saved_count
    
    def save_matches(self, matches_data: List[Dict[str, Any]]) -> int:
        """Save match data to database.
        
        Args:
            matches_data: List of match dictionaries
            
        Returns:
            Number of matches saved
        """
        saved_count = 0
        
        with self.get_session() as session:
            for match_data in matches_data:
                try:
                    # Check if match already exists
                    existing_match = session.query(Match).filter_by(
                        external_id=match_data['external_id']
                    ).first()
                    
                    if existing_match:
                        # Update existing match
                        for key, value in match_data.items():
                            if hasattr(existing_match, key):
                                setattr(existing_match, key, value)
                    else:
                        # Create new match
                        match = Match(**match_data)
                        session.add(match)
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error saving match {match_data.get('external_id', 'Unknown')}: {e}")
        
        self.logger.info(f"Saved {saved_count} matches to database")
        return saved_count
    
    def save_players(self, players_data: List[Dict[str, Any]]) -> int:
        """Save player data to database.
        
        Args:
            players_data: List of player dictionaries
            
        Returns:
            Number of players saved
        """
        saved_count = 0
        
        with self.get_session() as session:
            for player_data in players_data:
                try:
                    # Check if player already exists
                    existing_player = session.query(Player).filter_by(
                        external_id=player_data['external_id']
                    ).first()
                    
                    if existing_player:
                        # Update existing player
                        for key, value in player_data.items():
                            if hasattr(existing_player, key):
                                setattr(existing_player, key, value)
                    else:
                        # Create new player
                        player = Player(**player_data)
                        session.add(player)
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error saving player {player_data.get('name', 'Unknown')}: {e}")
        
        self.logger.info(f"Saved {saved_count} players to database")
        return saved_count
    
    def save_injuries(self, injuries_data: List[Dict[str, Any]]) -> int:
        """Save injury data to database.
        
        Args:
            injuries_data: List of injury dictionaries
            
        Returns:
            Number of injuries saved
        """
        saved_count = 0
        
        with self.get_session() as session:
            for injury_data in injuries_data:
                try:
                    # Find player by name (since we might not have external_id)
                    player = session.query(Player).filter_by(
                        name=injury_data['player_name']
                    ).first()
                    
                    if not player:
                        self.logger.warning(f"Player not found for injury: {injury_data['player_name']}")
                        continue
                    
                    # Check if injury already exists
                    existing_injury = session.query(Injury).filter_by(
                        player_id=player.id,
                        injury_type=injury_data['injury_type'],
                        injury_date=injury_data['injury_date']
                    ).first()
                    
                    if not existing_injury:
                        # Create new injury
                        injury_data['player_id'] = player.id
                        # Remove fields that don't exist in the model
                        model_data = {k: v for k, v in injury_data.items() 
                                    if k in ['player_id', 'injury_type', 'injury_date', 
                                           'expected_return_date', 'severity', 'status']}
                        
                        injury = Injury(**model_data)
                        session.add(injury)
                        saved_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error saving injury for {injury_data.get('player_name', 'Unknown')}: {e}")
        
        self.logger.info(f"Saved {saved_count} injuries to database")
        return saved_count
    
    def save_standings(self, standings_data: List[Dict[str, Any]]) -> int:
        """Save team standings data to database.
        
        Args:
            standings_data: List of standings dictionaries
            
        Returns:
            Number of standings saved
        """
        # This would be implemented based on the specific standings data structure
        # For now, return 0 as placeholder
        self.logger.info("Standings saving not yet implemented")
        return 0
    
    def get_teams_by_league(self, league_id: int) -> List[Team]:
        """Get all teams for a specific league.
        
        Args:
            league_id: League ID
            
        Returns:
            List of Team objects
        """
        with self.get_session() as session:
            return session.query(Team).filter_by(league_id=league_id).all()
    
    def get_recent_matches(self, days: int = 7) -> List[Match]:
        """Get recent matches from the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of Match objects
        """
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with self.get_session() as session:
            return session.query(Match).filter(
                Match.match_date >= cutoff_date,
                Match.status == 'FINISHED'
            ).order_by(Match.match_date.desc()).all()
    
    def get_upcoming_matches(self, days: int = 7) -> List[Match]:
        """Get upcoming matches for the next N days.
        
        Args:
            days: Number of days to look ahead
            
        Returns:
            List of Match objects
        """
        from datetime import datetime, timedelta
        
        end_date = datetime.now() + timedelta(days=days)
        
        with self.get_session() as session:
            return session.query(Match).filter(
                Match.match_date <= end_date,
                Match.match_date >= datetime.now(),
                Match.status.in_(['SCHEDULED', 'TIMED'])
            ).order_by(Match.match_date.asc()).all()
