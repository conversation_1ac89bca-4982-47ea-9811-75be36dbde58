"""Configuration management for the football prediction system."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """Database configuration."""
    type: str
    host: str
    port: int
    name: str
    user: str
    password: str


@dataclass
class APIConfig:
    """API configuration."""
    host: str
    port: int
    debug: bool
    cors_origins: list


class ConfigManager:
    """Manages configuration loading and access."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. If None, uses default path.
        """
        if config_path is None:
            config_path = self._get_default_config_path()
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path."""
        # Try to find config.yaml, fallback to config.example.yaml
        project_root = Path(__file__).parent.parent.parent.parent
        config_dir = project_root / "config"
        
        config_file = config_dir / "config.yaml"
        if config_file.exists():
            return str(config_file)
        
        example_config = config_dir / "config.example.yaml"
        if example_config.exists():
            return str(example_config)
        
        raise FileNotFoundError("No configuration file found")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
            
            # Override with environment variables if they exist
            config = self._override_with_env_vars(config)
            return config
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing configuration file: {e}")
    
    def _override_with_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Override configuration with environment variables."""
        # Database configuration
        if 'database' in config:
            db_config = config['database']
            db_config['host'] = os.getenv('DB_HOST', db_config.get('host'))
            db_config['port'] = int(os.getenv('DB_PORT', db_config.get('port', 5432)))
            db_config['name'] = os.getenv('DB_NAME', db_config.get('name'))
            db_config['user'] = os.getenv('DB_USER', db_config.get('user'))
            db_config['password'] = os.getenv('DB_PASSWORD', db_config.get('password'))
        
        # API keys
        if 'data_sources' in config:
            sources = config['data_sources']
            if 'football_data_api' in sources:
                sources['football_data_api']['api_key'] = os.getenv(
                    'FOOTBALL_DATA_API_KEY', 
                    sources['football_data_api'].get('api_key')
                )
            if 'rapid_api' in sources:
                sources['rapid_api']['api_key'] = os.getenv(
                    'RAPID_API_KEY',
                    sources['rapid_api'].get('api_key')
                )
        
        return config
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation, e.g., 'database.host')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration."""
        db_config = self.get('database', {})
        return DatabaseConfig(
            type=db_config.get('type', 'sqlite'),
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', 5432),
            name=db_config.get('name', 'football_prediction'),
            user=db_config.get('user', ''),
            password=db_config.get('password', '')
        )
    
    def get_api_config(self) -> APIConfig:
        """Get API configuration."""
        api_config = self.get('api', {})
        return APIConfig(
            host=api_config.get('host', '0.0.0.0'),
            port=api_config.get('port', 8000),
            debug=api_config.get('debug', False),
            cors_origins=api_config.get('cors_origins', ['*'])
        )
    
    def get_leagues(self) -> Dict[str, Dict[str, Any]]:
        """Get leagues configuration."""
        return self.get('leagues', {})
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration."""
        return self.get('models', {})
    
    def get_feature_config(self) -> Dict[str, Any]:
        """Get feature engineering configuration."""
        return self.get('features', {})
    
    def reload(self) -> None:
        """Reload configuration from file."""
        self._config = self._load_config()


# Global configuration instance
config = ConfigManager()
