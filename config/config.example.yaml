# Football Prediction ML System Configuration

# Database Configuration
database:
  type: "postgresql"  # postgresql, sqlite, mysql
  host: "localhost"
  port: 5432
  name: "football_prediction"
  user: "your_username"
  password: "your_password"
  
# Data Sources Configuration
data_sources:
  # API-Football (Primary data source)
  api_football:
    base_url: "https://v3.football.api-sports.io"
    api_key: "your_api_football_key_here"
    rate_limit: 100  # requests per day for free tier (1000 for paid)
    enabled: true
    timeout: 30
    retry_attempts: 3
    retry_delay: 1

  # Football Data API (Backup)
  football_data_api:
    base_url: "https://api.football-data.org/v4"
    api_key: "your_api_key"
    rate_limit: 10  # requests per minute
    enabled: false

  # Web Scraping Sources (Backup)
  scraping:
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    delay_range: [1, 3]  # seconds between requests
    enabled: false
    
# Leagues and Competitions (API-Football IDs)
leagues:
  premier_league:
    id: 39
    name: "Premier League"
    country: "England"
    season_start_month: 8
    api_football_id: 39

  la_liga:
    id: 140
    name: "La Liga"
    country: "Spain"
    season_start_month: 8
    api_football_id: 140

  bundesliga:
    id: 78
    name: "Bundesliga"
    country: "Germany"
    season_start_month: 8
    api_football_id: 78

  serie_a:
    id: 135
    name: "Serie A"
    country: "Italy"
    season_start_month: 8
    api_football_id: 135

  ligue_1:
    id: 61
    name: "Ligue 1"
    country: "France"
    season_start_month: 8
    api_football_id: 61

  champions_league:
    id: 2
    name: "UEFA Champions League"
    country: "Europe"
    season_start_month: 9
    api_football_id: 2

  europa_league:
    id: 3
    name: "UEFA Europa League"
    country: "Europe"
    season_start_month: 9
    api_football_id: 3

# Feature Engineering Configuration
features:
  # Team performance features
  team_form:
    recent_matches: 5  # Number of recent matches to consider
    weighted_form: true  # Apply time decay to older matches
    
  # Home/Away advantage
  home_away:
    min_matches: 10  # Minimum matches to calculate advantage
    seasons_lookback: 3  # Seasons to look back for historical data
    
  # Head-to-head features
  head_to_head:
    max_matches: 10  # Maximum H2H matches to consider
    years_lookback: 5  # Years to look back for H2H data
    
  # Player impact features
  player_impact:
    key_positions: ["GK", "CB", "CM", "ST"]  # Key positions to track
    injury_impact_days: 30  # Days to consider injury impact
    
  # Advanced statistics
  advanced_stats:
    expected_goals: true
    possession_stats: true
    pressing_stats: true
    set_piece_stats: true

# Model Configuration
models:
  # Cross-validation settings
  cross_validation:
    method: "time_series"  # time_series, stratified_kfold
    n_splits: 5
    test_size: 0.2
    
  # Hyperparameter optimization
  hyperparameter_tuning:
    method: "optuna"  # optuna, grid_search, random_search
    n_trials: 100
    timeout: 3600  # seconds
    
  # Model types to train
  model_types:
    xgboost:
      enabled: true
      params:
        max_depth: [3, 5, 7]
        learning_rate: [0.01, 0.1, 0.2]
        n_estimators: [100, 300, 500]
        
    lightgbm:
      enabled: true
      params:
        num_leaves: [31, 50, 100]
        learning_rate: [0.01, 0.1, 0.2]
        n_estimators: [100, 300, 500]
        
    neural_network:
      enabled: true
      params:
        hidden_layers: [[64, 32], [128, 64, 32], [256, 128, 64]]
        dropout_rate: [0.2, 0.3, 0.5]
        learning_rate: [0.001, 0.01]

# Evaluation Configuration
evaluation:
  # Metrics to track
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "roc_auc"
    - "log_loss"
    - "brier_score"
    
  # Backtesting configuration
  backtesting:
    start_date: "2020-01-01"
    end_date: "2023-12-31"
    retraining_frequency: "monthly"  # daily, weekly, monthly
    
# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]
  
# Monitoring Configuration
monitoring:
  # Model drift detection
  drift_detection:
    method: "ks_test"  # ks_test, psi, wasserstein
    threshold: 0.05
    check_frequency: "daily"
    
  # Performance monitoring
  performance_monitoring:
    alert_threshold: 0.05  # Alert if accuracy drops by 5%
    min_predictions: 100  # Minimum predictions before alerting
    
# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/football_prediction.log"
  max_file_size: "10MB"
  backup_count: 5

# Scheduling Configuration
scheduling:
  data_collection:
    frequency: "daily"
    time: "02:00"  # UTC time
    
  model_retraining:
    frequency: "weekly"
    day: "sunday"
    time: "03:00"
    
  predictions:
    frequency: "daily"
    time: "10:00"
