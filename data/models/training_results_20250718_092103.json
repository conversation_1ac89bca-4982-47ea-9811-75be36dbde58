{"neural_network": {"error": "NeuralNetworkFootballModel._calculate_feature_importance() missing 2 required positional arguments: 'X' and 'y'"}, "ensemble": {"error": "EnsembleFootballModel._calculate_feature_importance() missing 2 required positional arguments: 'X' and 'y'"}, "random_forest": {"training_history": {"training_time": 0.203874, "training_samples": 266, "features_count": 116, "train_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 92.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 53.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 121.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 266.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 266.0}}, "confusion_matrix": [[92, 0, 0], [0, 53, 0], [0, 0, 121]], "n_test_samples": 266}, "validation_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 15.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 20.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 38.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 38.0}}, "confusion_matrix": [[3, 0, 0], [0, 15, 0], [0, 0, 20]], "n_test_samples": 38}, "training_date": "2025-07-18T09:21:03.564719"}, "test_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 28.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 34.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 76.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 76.0}}, "confusion_matrix": [[28, 0, 0], [0, 14, 0], [0, 0, 34]], "n_test_samples": 76}, "model_info": {"model_name": "random_forest_football", "model_type": "classification", "is_trained": true, "config": {"n_estimators": 100, "max_depth": 10, "min_samples_split": 5, "min_samples_leaf": 2, "max_features": "sqrt", "random_state": 42, "n_jobs": -1, "class_weight": "balanced"}, "n_estimators": 100, "max_depth": 10, "n_features_in": 116, "n_classes": 3}}}