{"neural_network": {"training_history": {"training_time": 0.138218, "training_samples": 266, "features_count": 116, "train_metrics": {"accuracy": 0.8872180451127819, "classification_report": {"0": {"precision": 0.7540983606557377, "recall": 1.0, "f1-score": 0.8598130841121495, "support": 92.0}, "1": {"precision": 1.0, "recall": 0.4528301886792453, "f1-score": 0.6233766233766234, "support": 53.0}, "2": {"precision": 1.0, "recall": 0.9917355371900827, "f1-score": 0.995850622406639, "support": 121.0}, "accuracy": 0.8872180451127819, "macro avg": {"precision": 0.9180327868852459, "recall": 0.8148552419564427, "f1-score": 0.8263467766318039, "support": 266.0}, "weighted avg": {"precision": 0.9149513127079995, "recall": 0.8872180451127819, "f1-score": 0.8745853010845193, "support": 266.0}}, "confusion_matrix": [[92, 0, 0], [29, 24, 0], [1, 0, 120]], "n_test_samples": 266, "training_iterations": 23, "final_loss": 0.4048451301866987}, "validation_metrics": {"accuracy": 0.7631578947368421, "classification_report": {"0": {"precision": 0.25, "recall": 1.0, "f1-score": 0.4, "support": 3.0}, "1": {"precision": 1.0, "recall": 0.6, "f1-score": 0.75, "support": 15.0}, "2": {"precision": 1.0, "recall": 0.85, "f1-score": 0.918918918918919, "support": 20.0}, "accuracy": 0.7631578947368421, "macro avg": {"precision": 0.75, "recall": 0.8166666666666668, "f1-score": 0.6896396396396396, "support": 38.0}, "weighted avg": {"precision": 0.9407894736842105, "recall": 0.7631578947368421, "f1-score": 0.8112731152204836, "support": 38.0}}, "confusion_matrix": [[3, 0, 0], [6, 9, 0], [3, 0, 17]], "n_test_samples": 38, "training_iterations": 23, "final_loss": 0.4048451301866987}, "training_date": "2025-07-18T09:25:51.788404"}, "test_metrics": {"accuracy": 0.881578947368421, "classification_report": {"0": {"precision": 0.7567567567567568, "recall": 1.0, "f1-score": 0.8615384615384616, "support": 28.0}, "1": {"precision": 1.0, "recall": 0.35714285714285715, "f1-score": 0.5263157894736842, "support": 14.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 34.0}, "accuracy": 0.881578947368421, "macro avg": {"precision": 0.918918918918919, "recall": 0.7857142857142857, "f1-score": 0.7959514170040486, "support": 76.0}, "weighted avg": {"precision": 0.9103840682788051, "recall": 0.881578947368421, "f1-score": 0.8617302365224803, "support": 76.0}}, "confusion_matrix": [[28, 0, 0], [9, 5, 0], [0, 0, 34]], "n_test_samples": 76, "training_iterations": 23, "final_loss": 0.4048451301866987}, "model_info": {"model_name": "neural_network_football", "model_type": "classification", "is_trained": true, "config": {"hidden_layer_sizes": [128, 64, 32], "activation": "relu", "solver": "adam", "alpha": 0.001, "batch_size": "auto", "learning_rate": "adaptive", "learning_rate_init": 0.001, "max_iter": 500, "shuffle": true, "random_state": 42, "tol": 0.0001, "validation_fraction": 0.1, "beta_1": 0.9, "beta_2": 0.999, "epsilon": 1e-08, "n_iter_no_change": 10, "early_stopping": true}, "hidden_layer_sizes": [128, 64, 32], "n_features_in": 116, "n_outputs": 3, "n_layers": 5, "n_iter": 23, "loss": 0.4048451301866987}}, "ensemble": {"training_history": {"training_time": 3.393694, "training_samples": 266, "features_count": 116, "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "roc_auc": 1.0, "log_loss": 0.17836695129680707}, "validation_metrics": {}, "training_date": "2025-07-18T09:26:01.331194"}, "test_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "roc_auc": 1.0, "log_loss": 0.17702898002971326}, "model_info": {"model_name": "ensemble", "model_type": "classification", "is_trained": true, "feature_count": 116, "hyperparameters": {}, "training_history": {"training_time": 3.393694, "training_samples": 266, "features_count": 116, "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "roc_auc": 1.0, "log_loss": 0.17836695129680707}, "validation_metrics": {}, "training_date": "2025-07-18T09:26:01.331194"}, "top_features": {}, "ensemble_method": "weighted_average", "n_base_models": 3, "base_models": ["neural_network_football", "ensemble_football", "random_forest_football"], "model_weights": [0.27605118829981723, 0.36197440585009144, 0.36197440585009144], "trained_models": 3}}, "random_forest": {"training_history": {"training_time": 0.200032, "training_samples": 266, "features_count": 116, "train_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 92.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 53.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 121.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 266.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 266.0}}, "confusion_matrix": [[92, 0, 0], [0, 53, 0], [0, 0, 121]], "n_test_samples": 266}, "validation_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 15.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 20.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 38.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 38.0}}, "confusion_matrix": [[3, 0, 0], [0, 15, 0], [0, 0, 20]], "n_test_samples": 38}, "training_date": "2025-07-18T09:25:57.589414"}, "test_metrics": {"accuracy": 1.0, "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 28.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "2": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 34.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 76.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 76.0}}, "confusion_matrix": [[28, 0, 0], [0, 14, 0], [0, 0, 34]], "n_test_samples": 76}, "model_info": {"model_name": "random_forest_football", "model_type": "classification", "is_trained": true, "config": {"n_estimators": 100, "max_depth": 10, "min_samples_split": 5, "min_samples_leaf": 2, "max_features": "sqrt", "random_state": 42, "n_jobs": -1, "class_weight": "balanced"}, "n_estimators": 100, "max_depth": 10, "n_features_in": 116, "n_classes": 3}}}