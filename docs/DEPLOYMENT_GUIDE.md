# Football Prediction System - Deployment Guide

This guide provides comprehensive instructions for deploying the Football Prediction ML system in production.

## Overview

The system consists of:
- **Prediction Pipeline**: Core ML prediction engine
- **FastAPI Service**: RESTful API for predictions
- **Data Collection**: Automated data collection from API-Football
- **Database**: PostgreSQL for data storage
- **Monitoring**: Logging and performance monitoring

## Prerequisites

### System Requirements

- **Python**: 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: 10GB+ available space
- **CPU**: Multi-core processor recommended
- **Network**: Stable internet connection for API-Football

### Dependencies

Install all required packages:

```bash
pip install -r requirements.txt
```

Key dependencies:
- FastAPI + Uvicorn for API service
- XGBoost + LightGBM for ML models
- SQLAlchemy for database operations
- Requests for API-Football integration

## Configuration

### 1. Environment Setup

Create environment configuration:

```bash
# Copy example configuration
cp config/config.example.yaml config/config.yaml

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export API_FOOTBALL_KEY="your_api_key_here"
export DATABASE_URL="****************************************************"
```

### 2. API-Football Configuration

Update `config/config.yaml`:

```yaml
data_sources:
  api_football:
    api_key: "your_api_football_key_here"
    base_url: "https://v3.football.api-sports.io"
    rate_limit: 100  # Adjust based on your subscription
    enabled: true
```

### 3. Database Configuration

```yaml
database:
  url: "postgresql://user:password@localhost:5432/football_prediction"
  pool_size: 10
  max_overflow: 20
  echo: false  # Set to true for SQL debugging
```

### 4. API Configuration

```yaml
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]  # Restrict in production
```

## Database Setup

### 1. PostgreSQL Installation

```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS
brew install postgresql

# Start PostgreSQL service
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS
```

### 2. Database Creation

```sql
-- Connect to PostgreSQL
sudo -u postgres psql

-- Create database and user
CREATE DATABASE football_prediction;
CREATE USER football_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE football_prediction TO football_user;
```

### 3. Schema Migration

```bash
# Initialize database schema
python scripts/init_database.py

# Or run migrations if using Alembic
alembic upgrade head
```

## Model Training

### 1. Data Collection

```bash
# Collect initial data
python scripts/collect_api_football_data.py --collection-type full --include-historical

# Collect recent data
python scripts/collect_api_football_data.py --collection-type recent --days-back 60
```

### 2. Model Training

```bash
# Train all models
python scripts/train_models.py --data-source database --min-samples 100

# Train specific model
python scripts/train_models.py --model-type xgboost --optimize-hyperparameters
```

### 3. Model Evaluation

```bash
# Evaluate and select best model
python scripts/evaluate_models.py --selection-criteria weighted_score --cross-validate
```

## Deployment Options

### Option 1: Direct Python Deployment

```bash
# Start API server
python scripts/start_api.py --host 0.0.0.0 --port 8000

# Or with Gunicorn for production
gunicorn -w 4 -k uvicorn.workers.UvicornWorker src.api.main:app --bind 0.0.0.0:8000
```

### Option 2: Docker Deployment

Create `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port
EXPOSE 8000

# Start application
CMD ["python", "scripts/start_api.py", "--host", "0.0.0.0", "--port", "8000"]
```

Build and run:

```bash
# Build image
docker build -t football-prediction .

# Run container
docker run -d -p 8000:8000 \
  -e API_FOOTBALL_KEY=your_key \
  -e DATABASE_URL=****************************** \
  football-prediction
```

### Option 3: Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - API_FOOTBALL_KEY=${API_FOOTBALL_KEY}
      - DATABASE_URL=*******************************************/football_prediction
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=football_prediction
      - POSTGRES_USER=football_user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

Deploy:

```bash
docker-compose up -d
```

## Production Considerations

### 1. Security

- **API Keys**: Store securely using environment variables or secrets management
- **Database**: Use strong passwords and restrict access
- **HTTPS**: Implement SSL/TLS certificates
- **CORS**: Restrict origins in production
- **Rate Limiting**: Implement API rate limiting

### 2. Performance

- **Load Balancing**: Use nginx or similar for load balancing
- **Caching**: Implement Redis for prediction caching
- **Database**: Optimize queries and use connection pooling
- **Model Loading**: Load models once at startup
- **Async Processing**: Use async endpoints for better concurrency

### 3. Monitoring

- **Logging**: Centralized logging with ELK stack or similar
- **Metrics**: Prometheus + Grafana for monitoring
- **Health Checks**: Implement comprehensive health checks
- **Alerting**: Set up alerts for failures and performance issues

### 4. Backup and Recovery

- **Database Backups**: Regular automated backups
- **Model Versioning**: Version control for trained models
- **Configuration**: Backup configuration files
- **Data Recovery**: Disaster recovery procedures

## Scaling

### Horizontal Scaling

```bash
# Multiple API instances behind load balancer
docker-compose up --scale api=3
```

### Database Scaling

- **Read Replicas**: For read-heavy workloads
- **Connection Pooling**: PgBouncer or similar
- **Partitioning**: For large datasets

### Caching Strategy

```python
# Redis caching for predictions
import redis

redis_client = redis.Redis(host='localhost', port=6379, db=0)

# Cache predictions for 1 hour
def cache_prediction(key, prediction):
    redis_client.setex(key, 3600, json.dumps(prediction))
```

## Maintenance

### Regular Tasks

1. **Data Collection**: Schedule regular data updates
2. **Model Retraining**: Retrain models weekly/monthly
3. **Performance Monitoring**: Monitor prediction accuracy
4. **Database Maintenance**: Regular cleanup and optimization
5. **Security Updates**: Keep dependencies updated

### Automated Scheduling

```bash
# Crontab entries
# Daily data collection at 2 AM
0 2 * * * /path/to/python /path/to/scripts/collect_api_football_data.py --collection-type recent

# Weekly model retraining on Sundays at 3 AM
0 3 * * 0 /path/to/python /path/to/scripts/train_models.py --data-source database

# Monthly full data collection on 1st at 1 AM
0 1 1 * * /path/to/python /path/to/scripts/collect_api_football_data.py --collection-type full
```

## Troubleshooting

### Common Issues

1. **API-Football Rate Limits**
   - Monitor usage with `/status` endpoint
   - Implement request queuing
   - Consider upgrading subscription

2. **Model Loading Errors**
   - Check model file paths
   - Verify model compatibility
   - Retrain if necessary

3. **Database Connection Issues**
   - Check connection string
   - Verify database is running
   - Check firewall settings

4. **Memory Issues**
   - Monitor memory usage
   - Optimize model loading
   - Consider model compression

### Debugging

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Check API status
curl http://localhost:8000/status

# Test prediction
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{"home_team_id": 33, "away_team_id": 34}'

# Check logs
tail -f logs/api.log
tail -f logs/predictions.log
```

## Performance Benchmarks

Expected performance metrics:
- **Single Prediction**: 100-200ms
- **Batch Predictions**: 50ms per prediction
- **API Throughput**: 100+ requests/second
- **Memory Usage**: 500MB-2GB depending on models
- **Startup Time**: 10-30 seconds

## Support and Monitoring

### Health Checks

```bash
# API health
curl http://localhost:8000/health

# Detailed status
curl http://localhost:8000/status

# Model information
curl http://localhost:8000/model/info
```

### Monitoring Endpoints

- `/health` - Basic health check
- `/status` - Comprehensive status
- `/metrics` - Prometheus metrics (if implemented)

This deployment guide ensures a robust, scalable, and maintainable production deployment of your Football Prediction ML system!
