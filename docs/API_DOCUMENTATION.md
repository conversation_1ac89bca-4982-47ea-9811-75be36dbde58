# Football Prediction API Documentation

This document provides comprehensive documentation for the Football Prediction API.

## Overview

The Football Prediction API is a RESTful service that provides machine learning-powered predictions for football matches. It leverages comprehensive data from API-Football and advanced ML models to deliver accurate predictions with confidence scores.

## Quick Start

### Starting the API Server

```bash
# Start with default configuration
python scripts/start_api.py

# Start with custom host and port
python scripts/start_api.py --host 0.0.0.0 --port 8080

# Start in development mode with auto-reload
python scripts/start_api.py --reload --log-level debug
```

### API Endpoints

The API will be available at:
- **Base URL**: `http://localhost:8000`
- **Interactive Documentation**: `http://localhost:8000/docs`
- **Alternative Documentation**: `http://localhost:8000/redoc`

## API Endpoints

### Health and Status

#### GET `/health`
Simple health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### GET `/status`
Comprehensive API status and information.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "model_loaded": true,
  "api_football_status": {
    "requests_remaining": 95,
    "requests_made_today": 5
  },
  "timestamp": "2024-01-15T10:30:00"
}
```

### Predictions

#### POST `/predict`
Predict outcome for a specific match.

**Request Body:**
```json
{
  "home_team_id": 33,
  "away_team_id": 34,
  "match_date": "2024-01-20T15:00:00",
  "league_id": 39
}
```

**Response:**
```json
{
  "predicted_outcome": "H",
  "predicted_outcome_text": "Home Win",
  "probabilities": {
    "home_win": 0.65,
    "draw": 0.20,
    "away_win": 0.15
  },
  "confidence": 0.65,
  "confidence_level": "High",
  "prediction_timestamp": "2024-01-15T10:30:00",
  "model_info": {
    "model_name": "xgboost_football",
    "model_type": "classification",
    "features_used": 125
  },
  "feature_importance": {
    "home_recent_form": 0.15,
    "away_injury_impact": 0.12,
    "h2h_home_win_rate": 0.10
  },
  "prediction_factors": {
    "form_advantage": {
      "home_form": 0.75,
      "away_form": 0.45,
      "advantage": "Home"
    },
    "injury_impact": {
      "home_impact": 0.1,
      "away_impact": 0.3,
      "advantage": "Home"
    }
  }
}
```

#### POST `/predict/upcoming`
Predict outcomes for upcoming matches.

**Request Body:**
```json
{
  "days_ahead": 7,
  "league_ids": [39, 140]
}
```

**Response:**
```json
[
  {
    "predicted_outcome": "H",
    "predicted_outcome_text": "Home Win",
    "probabilities": {
      "home_win": 0.65,
      "draw": 0.20,
      "away_win": 0.15
    },
    "confidence": 0.65,
    "confidence_level": "High",
    "prediction_timestamp": "2024-01-15T10:30:00"
  }
]
```

#### POST `/predict/batch`
Predict outcomes for multiple matches.

**Request Body:**
```json
{
  "matches": [
    {
      "home_team_id": 33,
      "away_team_id": 34,
      "match_date": "2024-01-20T15:00:00",
      "league_id": 39
    },
    {
      "home_team_id": 35,
      "away_team_id": 36,
      "match_date": "2024-01-20T17:30:00",
      "league_id": 39
    }
  ]
}
```

### Information

#### GET `/leagues`
Get available leagues.

**Response:**
```json
[
  {
    "id": 39,
    "name": "Premier League",
    "country": "England",
    "api_football_id": 39
  },
  {
    "id": 140,
    "name": "La Liga",
    "country": "Spain",
    "api_football_id": 140
  }
]
```

#### GET `/model/info`
Get information about the loaded model.

**Response:**
```json
{
  "model_name": "xgboost_football",
  "model_type": "classification",
  "is_trained": true,
  "feature_count": 125,
  "metadata": {
    "training_date": "2024-01-10",
    "accuracy": 0.68,
    "cross_validation_score": 0.65
  }
}
```

### Data Management

#### POST `/data/collect`
Trigger background data collection.

**Query Parameters:**
- `collection_type`: Type of collection (`recent`, `upcoming`, `injuries`)
- `days_back`: Number of days back to collect (default: 7)

**Response:**
```json
{
  "message": "Data collection (recent) started in background",
  "collection_type": "recent",
  "days_back": 7
}
```

## CLI Tools

### Prediction CLI

The system includes a comprehensive CLI tool for predictions:

```bash
# Predict a single match
python scripts/predict_matches.py predict --home-team-id 33 --away-team-id 34

# Predict upcoming matches
python scripts/predict_matches.py upcoming --days-ahead 7

# Predict daily matches
python scripts/predict_matches.py daily --date 2024-01-20

# Generate performance report
python scripts/predict_matches.py report --start-date 2024-01-01 --end-date 2024-01-31

# Show model information
python scripts/predict_matches.py info
```

## Response Codes

- **200 OK**: Successful request
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

## Error Handling

All errors return a consistent format:

```json
{
  "detail": "Error description",
  "error_code": "PREDICTION_FAILED",
  "timestamp": "2024-01-15T10:30:00"
}
```

## Rate Limiting

The API implements rate limiting to protect against abuse:
- **Default**: 100 requests per minute per IP
- **Burst**: Up to 200 requests in a 10-second window
- **Headers**: Rate limit information in response headers

## Authentication

Currently, the API operates without authentication. For production deployment, consider implementing:
- API key authentication
- JWT tokens
- OAuth 2.0

## Configuration

### Environment Variables

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost/football_prediction

# API-Football Configuration
API_FOOTBALL_KEY=your_api_key_here
```

### Configuration File

Update `config/config.yaml`:

```yaml
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]

data_sources:
  api_football:
    api_key: "your_api_key_here"
    rate_limit: 100
```

## Monitoring and Logging

### Logging

Logs are written to:
- Console output
- `logs/api.log` - API server logs
- `logs/predictions.log` - Prediction logs

### Metrics

The API provides metrics for monitoring:
- Request count and response times
- Prediction accuracy over time
- API-Football usage statistics
- Model performance metrics

## Deployment

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "scripts/start_api.py", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Considerations

1. **Load Balancing**: Use nginx or similar for load balancing
2. **SSL/TLS**: Implement HTTPS for secure communication
3. **Database**: Use production database (PostgreSQL recommended)
4. **Caching**: Implement Redis for prediction caching
5. **Monitoring**: Use Prometheus/Grafana for monitoring

## Testing

### Unit Tests

```bash
# Run all tests
python -m pytest tests/

# Run API tests
python -m pytest tests/test_api.py

# Run prediction tests
python -m pytest tests/test_predictions.py
```

### Integration Tests

```bash
# Test full prediction pipeline
python scripts/predict_matches.py predict --home-team-id 1 --away-team-id 2

# Test API endpoints
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{"home_team_id": 33, "away_team_id": 34}'
```

## Performance

### Optimization Tips

1. **Model Loading**: Models are loaded once at startup
2. **Feature Caching**: Features are cached for repeated predictions
3. **Database Connections**: Connection pooling is used
4. **Async Processing**: Batch predictions use async processing

### Benchmarks

- **Single Prediction**: ~100-200ms
- **Batch Predictions**: ~50ms per prediction
- **Upcoming Matches**: ~2-5 seconds for 50 matches
- **Memory Usage**: ~500MB with loaded models

## Support

For support and questions:
- Check the logs for error details
- Review the interactive documentation at `/docs`
- Ensure all prerequisites are met
- Verify API-Football configuration

## Changelog

### Version 1.0.0
- Initial release
- Single match predictions
- Batch predictions
- Upcoming matches
- Performance reporting
- CLI tools
- Comprehensive documentation
