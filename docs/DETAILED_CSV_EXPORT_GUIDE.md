# Detailed CSV Export Guide

This guide explains how to export comprehensive football prediction data to CSV format with detailed match statistics, scores, cards, and analysis.

## Overview

The detailed CSV export system provides comprehensive match data including:

- **Match Information**: Teams, dates, venues, referees
- **Actual Results**: Full-time and half-time scores
- **Predictions**: ML predictions with confidence scores
- **Match Statistics**: Possession, shots, corners, fouls
- **Cards & Events**: Yellow/red cards, substitutions
- **Team Performance**: Season statistics and form
- **Analysis Factors**: Form, injuries, head-to-head data

## Export Methods

### 1. Command Line Interface (CLI)

#### Basic Export
```bash
# Export matches from a date range
python scripts/export_detailed_predictions.py \
  --start-date 2024-01-01 \
  --end-date 2024-01-31 \
  --output-file data/exports/january_predictions.csv
```

#### Advanced Options
```bash
# Include upcoming matches with predictions
python scripts/export_detailed_predictions.py \
  --start-date 2024-01-01 \
  --end-date 2024-02-15 \
  --include-upcoming \
  --output-file data/exports/detailed_with_upcoming.csv

# Export only finished matches
python scripts/export_detailed_predictions.py \
  --start-date 2024-01-01 \
  --end-date 2024-01-31 \
  --finished-only \
  --output-file data/exports/finished_only.csv
```

### 2. API Endpoint

#### POST `/export/detailed`

**Request:**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "include_upcoming": true,
  "output_filename": "custom_export.csv"
}
```

**Response:**
```json
{
  "message": "Detailed export started in background",
  "output_file": "data/exports/custom_export.csv",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "include_upcoming": true
}
```

#### Using cURL
```bash
curl -X POST "http://localhost:8000/export/detailed" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "include_upcoming": true
  }'
```

### 3. Python Code

```python
from src.prediction.detailed_exporter import DetailedPredictionExporter
from datetime import datetime

# Initialize exporter
exporter = DetailedPredictionExporter()

# Export detailed predictions
summary = exporter.export_detailed_predictions(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    output_file="data/exports/my_export.csv",
    include_upcoming=True
)

print(f"Exported {summary['total_matches']} matches")
```

## CSV Structure

### Column Categories

#### 1. Match Identification (8 columns)
- `match_id`: Internal match ID
- `external_id`: API-Football fixture ID
- `match_date`: Match date and time (ISO format)
- `league_name`: League name
- `home_team`: Home team name
- `away_team`: Away team name
- `venue`: Stadium/venue name
- `referee`: Referee name
- `match_status`: Match status (FT, NS, TBD, etc.)

#### 2. Actual Results (11 columns)
- `actual_home_score`: Final home team score
- `actual_away_score`: Final away team score
- `actual_home_score_ht`: Half-time home score
- `actual_away_score_ht`: Half-time away score
- `actual_home_score_ft`: Full-time home score
- `actual_away_score_ft`: Full-time away score
- `actual_result`: Actual result (H/D/A)
- `actual_result_ht`: Half-time result (H/D/A)
- `total_goals`: Total goals in match
- `goal_difference`: Goal difference (home - away)
- `both_teams_scored`: 1 if both teams scored, 0 otherwise
- `clean_sheet_home`: 1 if home team kept clean sheet
- `clean_sheet_away`: 1 if away team kept clean sheet

#### 3. Predictions (8 columns)
- `predicted_result`: Predicted result (H/D/A)
- `predicted_result_text`: Predicted result in text
- `home_win_probability`: Probability of home win (0-1)
- `draw_probability`: Probability of draw (0-1)
- `away_win_probability`: Probability of away win (0-1)
- `prediction_confidence`: Overall confidence score (0-1)
- `confidence_level`: Confidence level (Very High/High/Medium/Low/Very Low)
- `prediction_correct`: 1 if prediction was correct, 0 otherwise

#### 4. Prediction Factors (9 columns)
- `home_form_score`: Home team form score (0-1)
- `away_form_score`: Away team form score (0-1)
- `form_advantage`: Which team has form advantage
- `home_injury_impact`: Home team injury impact score
- `away_injury_impact`: Away team injury impact score
- `injury_advantage`: Which team has injury advantage
- `h2h_home_win_rate`: Head-to-head home win rate
- `h2h_away_win_rate`: Head-to-head away win rate
- `h2h_advantage`: Head-to-head advantage

#### 5. Match Statistics (12 columns)
- `home_possession`: Home team possession percentage
- `away_possession`: Away team possession percentage
- `home_shots`: Home team total shots
- `away_shots`: Away team total shots
- `home_shots_on_target`: Home team shots on target
- `away_shots_on_target`: Away team shots on target
- `home_corners`: Home team corner kicks
- `away_corners`: Away team corner kicks
- `home_fouls`: Home team fouls committed
- `away_fouls`: Away team fouls committed
- `home_offsides`: Home team offsides
- `away_offsides`: Away team offsides

#### 6. Cards & Events (11 columns)
- `home_yellow_cards`: Home team yellow cards
- `away_yellow_cards`: Away team yellow cards
- `home_red_cards`: Home team red cards
- `away_red_cards`: Away team red cards
- `total_cards`: Total cards in match
- `home_goals_events`: Home team goals (from events)
- `away_goals_events`: Away team goals (from events)
- `home_substitutions`: Home team substitutions
- `away_substitutions`: Away team substitutions
- `total_substitutions`: Total substitutions

#### 7. Team Statistics (18 columns)
**Home Team:**
- `home_matches_played`: Matches played this season
- `home_wins`: Wins this season
- `home_draws`: Draws this season
- `home_losses`: Losses this season
- `home_goals_for`: Goals scored this season
- `home_goals_against`: Goals conceded this season
- `home_points`: Points this season
- `home_win_rate`: Win rate (wins/matches_played)

**Away Team:**
- `away_matches_played`: Matches played this season
- `away_wins`: Wins this season
- `away_draws`: Draws this season
- `away_losses`: Losses this season
- `away_goals_for`: Goals scored this season
- `away_goals_against`: Goals conceded this season
- `away_points`: Points this season
- `away_win_rate`: Win rate (wins/matches_played)

## Sample Data

A sample CSV with 10 matches and 77 columns has been created at:
`data/exports/sample_detailed_predictions.csv`

### Sample Row Preview
```csv
match_date,home_team,away_team,actual_home_score,actual_away_score,predicted_result_text,prediction_confidence,total_cards,home_possession
2024-01-15T15:00:00,Manchester City,Liverpool,2,0,Home Win,0.751,6,51.7
```

## Use Cases

### 1. Performance Analysis
```python
import pandas as pd

# Load exported data
df = pd.read_csv('data/exports/detailed_predictions.csv')

# Calculate prediction accuracy
accuracy = df['prediction_correct'].mean()
print(f"Overall Accuracy: {accuracy:.1%}")

# Analyze by confidence level
confidence_analysis = df.groupby('confidence_level')['prediction_correct'].agg(['count', 'mean'])
print(confidence_analysis)
```

### 2. Match Statistics Analysis
```python
# Analyze cards vs goals relationship
import matplotlib.pyplot as plt

plt.scatter(df['total_cards'], df['total_goals'])
plt.xlabel('Total Cards')
plt.ylabel('Total Goals')
plt.title('Cards vs Goals Relationship')
plt.show()
```

### 3. Team Performance Comparison
```python
# Compare home vs away performance
home_stats = df.groupby('home_team').agg({
    'actual_home_score': 'mean',
    'home_win_probability': 'mean',
    'prediction_correct': 'mean'
})

away_stats = df.groupby('away_team').agg({
    'actual_away_score': 'mean',
    'away_win_probability': 'mean',
    'prediction_correct': 'mean'
})
```

### 4. Betting Analysis
```python
# Analyze high-confidence predictions
high_confidence = df[df['prediction_confidence'] > 0.7]
roi_analysis = high_confidence.groupby('predicted_result_text')['prediction_correct'].agg(['count', 'mean'])
print("High Confidence Predictions ROI:")
print(roi_analysis)
```

## Data Quality Notes

### Missing Data Handling
- Missing statistics are filled with 0 or appropriate defaults
- Upcoming matches have predictions but no actual results
- Some older matches may lack detailed statistics

### Data Sources
- **Match Results**: Database records from API-Football
- **Predictions**: ML model predictions (existing or generated)
- **Statistics**: Match statistics from API-Football
- **Events**: Goals, cards, substitutions from match events
- **Team Stats**: Season statistics from team records

## Export Performance

### Typical Export Times
- **10 matches**: ~5-10 seconds
- **100 matches**: ~30-60 seconds
- **1000 matches**: ~5-10 minutes

### File Sizes
- **10 matches**: ~50KB
- **100 matches**: ~500KB
- **1000 matches**: ~5MB

## Troubleshooting

### Common Issues

1. **No matches found**
   - Check date range
   - Verify data has been collected for the period
   - Ensure database connectivity

2. **Missing statistics**
   - Some matches may not have detailed statistics
   - Older matches might have limited data
   - Check API-Football data collection status

3. **Export fails**
   - Check disk space
   - Verify output directory permissions
   - Check database connectivity

### Debug Commands
```bash
# Check available matches
python -c "
from src.utils.database import DatabaseManager
with DatabaseManager().get_session() as session:
    from src.utils.database.models import Match
    count = session.query(Match).count()
    print(f'Total matches in database: {count}')
"

# Test export with small date range
python scripts/export_detailed_predictions.py \
  --start-date 2024-01-01 \
  --end-date 2024-01-02 \
  --output-file test_export.csv
```

This comprehensive CSV export system provides everything you need for detailed football match analysis, betting strategies, and ML model evaluation!
