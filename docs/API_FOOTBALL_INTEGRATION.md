# API-Football Integration Guide

This document provides comprehensive information about the API-Football integration in our football prediction ML system.

## Overview

API-Football (api-football.com) is our primary data source, providing comprehensive football data including:

- **Match Data**: Fixtures, results, statistics, events
- **Team Data**: Squad information, statistics, standings
- **Player Data**: Detailed player information, statistics, ratings
- **Injury Data**: Real-time injury reports and status
- **Historical Data**: Extensive historical match and performance data
- **Live Data**: Real-time match events and statistics

## Configuration

### API Key Setup

1. Sign up at [API-Football](https://www.api-football.com/)
2. Get your API key from the dashboard
3. Update your configuration file:

```yaml
data_sources:
  api_football:
    base_url: "https://v3.football.api-sports.io"
    api_key: "your_api_football_key_here"
    rate_limit: 100  # requests per day for free tier
    enabled: true
    timeout: 30
    retry_attempts: 3
    retry_delay: 1
```

### League Configuration

Configure the leagues you want to collect data for:

```yaml
leagues:
  premier_league:
    id: 39
    name: "Premier League"
    country: "England"
    api_football_id: 39
  
  la_liga:
    id: 140
    name: "La Liga"
    country: "Spain"
    api_football_id: 140
```

## Data Collection

### Comprehensive Data Collection

Collect all available data for configured leagues:

```bash
python scripts/collect_api_football_data.py --collection-type full --include-historical
```

### Recent Data Collection

Collect recent matches and statistics:

```bash
python scripts/collect_api_football_data.py --collection-type recent --days-back 30
```

### Upcoming Matches

Collect upcoming matches for prediction:

```bash
python scripts/collect_api_football_data.py --collection-type upcoming --days-ahead 7
```

### Injury Data

Update current injury status:

```bash
python scripts/collect_api_football_data.py --collection-type injuries
```

### Specific Match Details

Collect comprehensive data for a specific match:

```bash
python scripts/collect_api_football_data.py --collection-type match-details --fixture-id 12345
```

## Data Types Collected

### 1. Match Data

- **Basic Information**: Date, time, venue, referee
- **Scores**: Half-time, full-time, extra-time, penalties
- **Status**: Live updates, match status
- **Events**: Goals, cards, substitutions, VAR decisions
- **Statistics**: Possession, shots, passes, corners, etc.
- **Lineups**: Starting XI, substitutes, formations

### 2. Team Data

- **Basic Information**: Name, logo, venue, capacity
- **Season Statistics**: Wins, draws, losses, goals
- **Performance Metrics**: Home/away form, recent results
- **Squad Information**: Complete player roster

### 3. Player Data

- **Personal Information**: Name, age, nationality, photo
- **Physical Attributes**: Height, weight, position
- **Performance Data**: Rating, statistics, market value
- **Status**: Injury status, captain status

### 4. Injury Data

- **Current Injuries**: Active injury list
- **Injury Details**: Type, severity, expected return
- **Historical Injuries**: Past injury records
- **Impact Assessment**: Position-based impact analysis

## Enhanced Features

### 1. Comprehensive Feature Extraction

The API-Football integration enables extraction of 150+ features:

```python
from src.features.engineering.api_football_extractor import APIFootballFeatureExtractor

extractor = APIFootballFeatureExtractor()
features = extractor.extract_features(match_data, context_data)
```

### 2. Real-time Injury Impact

Advanced injury impact assessment using:
- Player importance ratings
- Position-specific weights
- Historical performance impact
- Recovery time predictions

### 3. Enhanced Team Statistics

Detailed team performance metrics:
- Form analysis over multiple periods
- Home/away performance splits
- Goal scoring patterns
- Defensive statistics

### 4. Head-to-Head Analysis

Comprehensive H2H analysis including:
- Historical matchup results
- Goal patterns in H2H matches
- Venue-specific performance
- Recent trend analysis

## API Usage and Limits

### Rate Limiting

- **Free Tier**: 100 requests/day
- **Paid Tiers**: Up to 10,000+ requests/day
- **Automatic Rate Limiting**: Built-in request throttling
- **Request Tracking**: Monitor daily usage

### Best Practices

1. **Batch Collection**: Collect data in batches to minimize API calls
2. **Caching**: Cache frequently accessed data
3. **Incremental Updates**: Only collect new/changed data
4. **Error Handling**: Robust error handling and retry logic

### Monitoring Usage

Check your API usage:

```bash
python scripts/collect_api_football_data.py --check-api-status
```

## Database Schema

### Enhanced Models

The integration includes enhanced database models:

- **Match**: Extended with API-Football specific fields
- **Player**: Comprehensive player information
- **MatchEvent**: Goals, cards, substitutions
- **MatchLineup**: Team formations and lineups
- **EnhancedInjury**: Detailed injury tracking

### Data Relationships

```
League -> Teams -> Players -> Injuries
       -> Matches -> Events
                  -> Lineups
                  -> Statistics
```

## Feature Engineering

### API-Football Specific Features

1. **Match Context Features**
   - Time-based features (day, hour, month)
   - Round/matchday information
   - Season progress indicators

2. **Team Performance Features**
   - Win/draw/loss rates
   - Goals per game statistics
   - Home/away performance splits

3. **Injury Impact Features**
   - Position-weighted injury impact
   - Player importance ratings
   - Recovery time analysis

4. **Form Analysis Features**
   - Recent form over multiple periods
   - Weighted form calculations
   - Momentum indicators

## Integration Examples

### Basic Data Collection

```python
from src.data.api_football_orchestrator import APIFootballOrchestrator

orchestrator = APIFootballOrchestrator()

# Collect all data for Premier League
results = orchestrator.collect_all_data(
    leagues=['premier_league'],
    include_historical=True,
    days_back=30
)
```

### Feature Extraction

```python
from src.features.engineering.api_football_extractor import APIFootballFeatureExtractor

extractor = APIFootballFeatureExtractor()

# Extract features for a match
features = extractor.extract_features(
    match_data={
        'home_team_id': 33,
        'away_team_id': 34,
        'match_date': '2024-01-15T15:00:00Z'
    },
    context_data={
        'home_team_injuries': [...],
        'away_team_injuries': [...],
        'home_team_matches': [...],
        'away_team_matches': [...],
        'h2h_matches': [...]
    }
)
```

## Troubleshooting

### Common Issues

1. **API Key Issues**
   - Verify API key is correct
   - Check subscription status
   - Ensure key has proper permissions

2. **Rate Limit Exceeded**
   - Monitor daily usage
   - Implement request spacing
   - Consider upgrading subscription

3. **Data Quality Issues**
   - Validate data before processing
   - Handle missing/null values
   - Implement data quality checks

### Error Handling

The system includes comprehensive error handling:
- Automatic retries for transient errors
- Graceful degradation for missing data
- Detailed error logging and reporting

## Performance Optimization

### Efficient Data Collection

1. **Parallel Processing**: Collect data for multiple leagues simultaneously
2. **Incremental Updates**: Only collect new/changed data
3. **Smart Caching**: Cache frequently accessed data
4. **Batch Operations**: Group related API calls

### Memory Management

- Stream large datasets
- Process data in chunks
- Clean up temporary data
- Monitor memory usage

## Support and Resources

- **API Documentation**: [API-Football Docs](https://www.api-football.com/documentation-v3)
- **Rate Limits**: Check your subscription limits
- **Support**: Contact API-Football support for API issues
- **Community**: Join the API-Football community forums

## Next Steps

1. **Set up your API key** in the configuration
2. **Run initial data collection** for your target leagues
3. **Monitor API usage** to ensure you stay within limits
4. **Customize feature extraction** for your specific needs
5. **Integrate with ML pipeline** for predictions

The API-Football integration provides the foundation for world-class football predictions with comprehensive, real-time data!
