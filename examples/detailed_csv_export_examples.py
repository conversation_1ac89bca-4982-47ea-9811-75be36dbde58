#!/usr/bin/env python3
"""Examples of using the detailed CSV export system."""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def example_1_basic_export():
    """Example 1: Basic detailed export using the exporter class."""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Detailed Export")
    print("="*60)
    
    try:
        from src.prediction.detailed_exporter import DetailedPredictionExporter
        
        # Initialize exporter
        exporter = DetailedPredictionExporter()
        
        # Define date range (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        print(f"Exporting matches from {start_date.date()} to {end_date.date()}")
        
        # Export detailed predictions
        summary = exporter.export_detailed_predictions(
            start_date=start_date,
            end_date=end_date,
            output_file="data/exports/example_basic_export.csv",
            include_upcoming=True
        )
        
        if 'error' not in summary:
            print(f"✅ Export successful!")
            print(f"📁 File: {summary['export_file']}")
            print(f"📊 Total matches: {summary['total_matches']}")
            print(f"✅ Finished matches: {summary['finished_matches']}")
            print(f"🔮 Upcoming matches: {summary['upcoming_matches']}")
        else:
            print(f"❌ Export failed: {summary['error']}")
            
    except Exception as e:
        print(f"❌ Example failed: {e}")


def example_2_cli_usage():
    """Example 2: CLI usage examples."""
    print("\n" + "="*60)
    print("EXAMPLE 2: CLI Usage Examples")
    print("="*60)
    
    print("📋 CLI Commands for detailed export:")
    print()
    
    print("1. Basic export for last month:")
    print("   python scripts/export_detailed_predictions.py \\")
    print("     --start-date 2024-01-01 \\")
    print("     --end-date 2024-01-31 \\")
    print("     --output-file january_predictions.csv")
    print()
    
    print("2. Include upcoming matches:")
    print("   python scripts/export_detailed_predictions.py \\")
    print("     --start-date 2024-01-01 \\")
    print("     --end-date 2024-02-15 \\")
    print("     --include-upcoming \\")
    print("     --output-file predictions_with_upcoming.csv")
    print()
    
    print("3. Export only finished matches:")
    print("   python scripts/export_detailed_predictions.py \\")
    print("     --start-date 2024-01-01 \\")
    print("     --end-date 2024-01-31 \\")
    print("     --finished-only \\")
    print("     --output-file finished_matches_only.csv")


def example_3_api_usage():
    """Example 3: API usage examples."""
    print("\n" + "="*60)
    print("EXAMPLE 3: API Usage Examples")
    print("="*60)
    
    print("📋 API Endpoint: POST /export/detailed")
    print()
    
    print("1. Basic API request:")
    print('   curl -X POST "http://localhost:8000/export/detailed" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{')
    print('       "start_date": "2024-01-01",')
    print('       "end_date": "2024-01-31",')
    print('       "include_upcoming": true')
    print('     }\'')
    print()
    
    print("2. Custom filename:")
    print('   curl -X POST "http://localhost:8000/export/detailed" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{')
    print('       "start_date": "2024-01-01",')
    print('       "end_date": "2024-01-31",')
    print('       "include_upcoming": false,')
    print('       "output_filename": "my_custom_export.csv"')
    print('     }\'')


def example_4_data_analysis():
    """Example 4: Analyzing exported CSV data."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Data Analysis Examples")
    print("="*60)
    
    # Use the sample CSV we created
    csv_file = "data/exports/sample_detailed_predictions.csv"
    
    try:
        # Load the CSV
        df = pd.read_csv(csv_file)
        print(f"📊 Loaded {len(df)} matches from {csv_file}")
        print()
        
        # 1. Prediction Accuracy Analysis
        print("1. PREDICTION ACCURACY ANALYSIS:")
        print("-" * 40)
        overall_accuracy = df['prediction_correct'].mean()
        print(f"   Overall Accuracy: {overall_accuracy:.1%}")
        
        # Accuracy by confidence level
        if 'confidence_level' in df.columns:
            confidence_accuracy = df.groupby('confidence_level')['prediction_correct'].agg(['count', 'mean'])
            print("   Accuracy by Confidence Level:")
            for level, stats in confidence_accuracy.iterrows():
                print(f"     {level}: {stats['mean']:.1%} ({stats['count']} matches)")
        print()
        
        # 2. Scoring Analysis
        print("2. SCORING ANALYSIS:")
        print("-" * 40)
        avg_goals = df['total_goals'].mean()
        print(f"   Average Goals per Match: {avg_goals:.1f}")
        
        both_scored_rate = df['both_teams_scored'].mean()
        print(f"   Both Teams Scored Rate: {both_scored_rate:.1%}")
        
        clean_sheet_rate = (df['clean_sheet_home'] + df['clean_sheet_away']).mean()
        print(f"   Clean Sheet Rate: {clean_sheet_rate:.1%}")
        print()
        
        # 3. Cards Analysis
        print("3. CARDS ANALYSIS:")
        print("-" * 40)
        avg_cards = df['total_cards'].mean()
        print(f"   Average Cards per Match: {avg_cards:.1f}")
        
        avg_yellow = (df['home_yellow_cards'] + df['away_yellow_cards']).mean()
        avg_red = (df['home_red_cards'] + df['away_red_cards']).mean()
        print(f"   Average Yellow Cards: {avg_yellow:.1f}")
        print(f"   Average Red Cards: {avg_red:.1f}")
        print()
        
        # 4. Team Performance Analysis
        print("4. TEAM PERFORMANCE ANALYSIS:")
        print("-" * 40)
        
        # Home advantage
        home_wins = (df['actual_result'] == 'H').sum()
        draws = (df['actual_result'] == 'D').sum()
        away_wins = (df['actual_result'] == 'A').sum()
        
        print(f"   Home Wins: {home_wins} ({home_wins/len(df):.1%})")
        print(f"   Draws: {draws} ({draws/len(df):.1%})")
        print(f"   Away Wins: {away_wins} ({away_wins/len(df):.1%})")
        print()
        
        # 5. Possession vs Results
        print("5. POSSESSION ANALYSIS:")
        print("-" * 40)
        avg_home_possession = df['home_possession'].mean()
        print(f"   Average Home Possession: {avg_home_possession:.1f}%")
        
        # Correlation between possession and winning
        home_wins_df = df[df['actual_result'] == 'H']
        if not home_wins_df.empty:
            avg_possession_when_home_wins = home_wins_df['home_possession'].mean()
            print(f"   Home Possession when Home Wins: {avg_possession_when_home_wins:.1f}%")
        print()
        
        # 6. Top Teams Analysis
        print("6. TOP TEAMS ANALYSIS:")
        print("-" * 40)
        
        # Most goals scored
        home_goals = df.groupby('home_team')['actual_home_score'].sum()
        away_goals = df.groupby('away_team')['actual_away_score'].sum()
        
        print("   Top Goal Scorers:")
        all_goals = {}
        for team in set(df['home_team'].tolist() + df['away_team'].tolist()):
            home_g = home_goals.get(team, 0)
            away_g = away_goals.get(team, 0)
            all_goals[team] = home_g + away_g
        
        top_scorers = sorted(all_goals.items(), key=lambda x: x[1], reverse=True)[:3]
        for i, (team, goals) in enumerate(top_scorers, 1):
            print(f"     {i}. {team}: {goals} goals")
        
    except FileNotFoundError:
        print(f"❌ Sample CSV not found. Run create_sample_detailed_export.py first.")
    except Exception as e:
        print(f"❌ Analysis failed: {e}")


def example_5_advanced_filtering():
    """Example 5: Advanced filtering and analysis."""
    print("\n" + "="*60)
    print("EXAMPLE 5: Advanced Filtering Examples")
    print("="*60)
    
    csv_file = "data/exports/sample_detailed_predictions.csv"
    
    try:
        df = pd.read_csv(csv_file)
        
        print("📋 Advanced Filtering Examples:")
        print()
        
        # 1. High-scoring matches
        print("1. HIGH-SCORING MATCHES (3+ goals):")
        high_scoring = df[df['total_goals'] >= 3]
        print(f"   Found {len(high_scoring)} high-scoring matches")
        if not high_scoring.empty:
            for _, match in high_scoring.iterrows():
                print(f"   {match['home_team']} {match['actual_home_score']}-{match['actual_away_score']} {match['away_team']}")
        print()
        
        # 2. High-confidence correct predictions
        print("2. HIGH-CONFIDENCE CORRECT PREDICTIONS:")
        high_conf_correct = df[(df['prediction_confidence'] > 0.7) & (df['prediction_correct'] == 1)]
        print(f"   Found {len(high_conf_correct)} high-confidence correct predictions")
        if not high_conf_correct.empty:
            for _, match in high_conf_correct.iterrows():
                print(f"   {match['home_team']} vs {match['away_team']}: {match['predicted_result_text']} ({match['prediction_confidence']:.3f})")
        print()
        
        # 3. Card-heavy matches
        print("3. CARD-HEAVY MATCHES (5+ cards):")
        card_heavy = df[df['total_cards'] >= 5]
        print(f"   Found {len(card_heavy)} card-heavy matches")
        if not card_heavy.empty:
            for _, match in card_heavy.iterrows():
                print(f"   {match['home_team']} vs {match['away_team']}: {match['total_cards']} cards")
        print()
        
        # 4. Possession dominance
        print("4. POSSESSION DOMINANCE (70%+ possession):")
        high_possession = df[df['home_possession'] >= 70]
        print(f"   Found {len(high_possession)} matches with 70%+ home possession")
        if not high_possession.empty:
            for _, match in high_possession.iterrows():
                result = "Won" if match['actual_result'] == 'H' else "Drew" if match['actual_result'] == 'D' else "Lost"
                print(f"   {match['home_team']}: {match['home_possession']:.1f}% possession, {result}")
        
    except Exception as e:
        print(f"❌ Advanced filtering failed: {e}")


def main():
    """Run all examples."""
    print("🏈 DETAILED CSV EXPORT - COMPREHENSIVE EXAMPLES")
    print("="*80)
    
    # Run examples
    example_1_basic_export()
    example_2_cli_usage()
    example_3_api_usage()
    example_4_data_analysis()
    example_5_advanced_filtering()
    
    print("\n" + "="*80)
    print("✅ ALL EXAMPLES COMPLETED!")
    print("="*80)
    print()
    print("📋 WHAT YOU CAN DO WITH THE DETAILED CSV:")
    print("-" * 50)
    print("✅ Analyze prediction accuracy by confidence level")
    print("✅ Study team performance and form trends")
    print("✅ Examine match statistics and patterns")
    print("✅ Analyze cards, fouls, and disciplinary trends")
    print("✅ Study home advantage and venue effects")
    print("✅ Develop betting strategies based on predictions")
    print("✅ Create custom dashboards and visualizations")
    print("✅ Perform statistical analysis and modeling")
    print("✅ Export data for external analysis tools")
    print("✅ Generate reports and insights")
    print()
    print("📁 Sample CSV available at: data/exports/sample_detailed_predictions.csv")
    print("📚 Full documentation: docs/DETAILED_CSV_EXPORT_GUIDE.md")
    print("="*80)


if __name__ == "__main__":
    main()
