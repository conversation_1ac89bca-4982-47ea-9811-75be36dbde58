# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Data files
data/raw/*
data/processed/*
data/features/*
data/models/*
!data/raw/.gitkeep
!data/processed/.gitkeep
!data/features/.gitkeep
!data/models/.gitkeep

# Logs
logs/
*.log

# Configuration
config/config.yaml
.env

# Database
*.db
*.sqlite
*.sqlite3

# ML Models
*.pkl
*.joblib
*.h5
*.pt
*.pth

# Temporary files
*.tmp
*.temp

# OS
.DS_Store
Thumbs.db

# Testing
.coverage
.pytest_cache/
htmlcov/

# MLflow
mlruns/

# Weights & Biases
wandb/
